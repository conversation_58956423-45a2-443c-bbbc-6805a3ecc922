package cz.kpsys.portaro.auth.internal.credentials;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.commons.object.repo.SeveritedItemNotFoundException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formfield.EnabledFormFieldWhenAnotherFilled;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.BeforeIntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.EmailEditor;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.search.ItemCustomizableContextualSearchFunction;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@Form(id = "tokenedPasswordChangeRequest",
        title = "{mail.SendEmail}",
        footer = {
                "{login.resetPassword.sendMailForm.PoPotvrzeniPoslemeEmail}",
                "{login.resetPassword.sendMailForm.OdkazBudeFunkcniJenXMinut:20}"
        })
@FormSubmit(path = "/api/users/tokened-password-change-mail")
@BeforeIntegrityValidation(bean = "tokenedPasswordChangeMailRequestPreRequiredValidationModifier")
@With
@FieldNameConstants
public record TokenedPasswordChangeMailRequest(

        @FormPropertyLabel("{mail.EnterYourEmailAddress}")
        @EmailEditor
        @ValueEditorModifier("tokenedPasswordChangeMailRequestEmailEditorModifier")
        @Email
        @Size(min = 1, max = 40)
        @NotBlank
        String email,

        @FormPropertyLabel("Uživatel")
        @EnabledFormFieldWhenAnotherFilled(Fields.email)
        @SingleAcceptableEditor(valuesSourceBean = "tokenedPasswordChangeMailRequestAllowedBasicUsersResolver")
        @NotNull(groups = IntegrityValidation.class)
        BasicUser user

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class TokenedPasswordChangeMailRequestPreRequiredValidationModifier implements TypedAuthenticatedContextualObjectModifier<TokenedPasswordChangeMailRequest> {

        @NonNull ItemCustomizableContextualSearchFunction<String, Department, List<BasicUser>> contextualEmailUsersLoader;

        @Override
        public TokenedPasswordChangeMailRequest modify(TokenedPasswordChangeMailRequest formObject, Department ctx, UserAuthentication currentAuth) {
            if (formObject.email() != null) {
                List<BasicUser> users = contextualEmailUsersLoader.getOn(formObject.email(), MapBackedParams.createEmpty(), ctx);
                if (users.size() == 1) {
                    formObject = formObject.withUser(users.getFirst());
                }
            }
            return formObject;
        }

    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class TokenedPasswordChangeMailRequestAllowedBasicUsersResolver implements AcceptableValuesResolver<TokenedPasswordChangeMailRequest, BasicUser> {

        @NonNull ItemCustomizableContextualSearchFunction<String, Department, List<BasicUser>> contextualEmailUsersLoader;

        @Override
        public List<BasicUser> resolveAcceptableValues(TokenedPasswordChangeMailRequest formObject, Department ctx) {
            List<BasicUser> users = contextualEmailUsersLoader.getOn(formObject.email(), MapBackedParams.createEmpty(), ctx);
            if (users.isEmpty()) {
                throw new SeveritedItemNotFoundException(User.class, formObject.email(), "User by email %s does not exist on %s".formatted(formObject.email(), ctx), Texts.ofNative("User with email %s does not exists".formatted(formObject.email())), SeveritedException.SEVERITY_WARNING);
            }
            return users;
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static final class TokenedPasswordChangeMailRequestEmailEditorModifier implements TextValueEditorModifier<TokenedPasswordChangeMailRequest> {

        @Override
        public TextValueEditor modify(TextValueEditor editor, TokenedPasswordChangeMailRequest formObject, Department ctx) {
            if (formObject.email() != null) {
                editor = editor.withVisible(false);
            }
            return editor;
        }
    }


}
