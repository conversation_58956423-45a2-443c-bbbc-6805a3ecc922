package cz.kpsys.portaro.loan.mail;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.request.LoanRequest;
import cz.kpsys.portaro.loan.request.LoanRequestItem;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.Value;

import java.util.List;

@Value
public class MailLoanRequestCommand implements LoanRequest {

    @NonNull
    String requesterEmail;

    @NullableNotBlank
    String requesterName;

    @NonNull
    String subject;

    @NonNull
    String message;

    @NonNull
    @NotEmpty
    List<LoanRequestItem> items;

    @NonNull
    Department ctx;

    @NonNull
    UserAuthentication currentAuth;

    @Override
    public String toString() {
        return "Mail loan request for " + items + ", sender=" + getRequesterEmail() + ", recipient=" + getRequesterEmail() + ", subject=" + getSubject() + ", text=" + getMessage();
    }

}