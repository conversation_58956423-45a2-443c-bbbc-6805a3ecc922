package cz.kpsys.portaro.auth.ezak;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import org.springframework.security.authentication.AuthenticationServiceException;

public class EzakErrorStatusResponseException extends AuthenticationServiceException implements UserFriendlyException {

    private final String errorText;

    public EzakErrorStatusResponseException(String errorText, String responseXml) {
        super("EZAK login error: " + errorText + ", response xml: " + responseXml);
        this.errorText = errorText;
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Chyba při ověřování uživatele v EZAK: " + errorText);
    }
}
