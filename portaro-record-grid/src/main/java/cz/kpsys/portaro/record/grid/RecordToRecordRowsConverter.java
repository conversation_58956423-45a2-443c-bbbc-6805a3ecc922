package cz.kpsys.portaro.record.grid;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.grid.*;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.frontend.ErroredFieldResponse;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.api.PermissionResultResponse;
import cz.kpsys.portaro.security.api.PermissionResultToResponseConverter;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordToRecordRowsConverter implements ViewableItemsTypedConverter<Record, RecordRow> {

    @NonNull Function<@NonNull PermissionResult, @NonNull PermissionResultResponse> permissionResultToResponseConverter = new PermissionResultToResponseConverter();
    @NonNull RecordLockedContextualPredicate recordLockedContextualPredicate;
    @NonNull RecordPhaseAndStatusConverter recordPhaseAndStatusConverter;


    @NonNull
    @Override
    public List<RecordRow> convert(@NonNull List<Record> records, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exportTypes, @NonNull Map<String, Object> additionalModel) {
        return ListUtil.convert(records, record -> convertSingle(record, currentAuth, ctx, locale, exportTypes, additionalModel));
    }

    @NonNull
    @Override
    public RecordRow convertSingle(@NonNull Record record, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exportTypes, @NonNull Map<String, Object> additionalModel) {
        FieldContainer detail = Objects.requireNonNull(record.getDetail(), "Cannot convert record to table record, because record detail is null");

        try {
            SimpleFondResponse fondResponse = SimpleFondResponse.mapFromFond(record.getFond());
            Map<String, List<GridField>> cells = collectAndGroup(detail.streamFields());
            Boolean locked = recordLockedContextualPredicate.getOn(record, ctx);
            List<RecordEditationPhaseTransition> nextPhases = recordPhaseAndStatusConverter.nextPhases(record, ctx, currentAuth);
            List<RecordEditationPhaseTransitionResponse> editationPhaseTransitions = ListUtil.convertStrict(nextPhases, this::mapPhaseTransition);
            return new RecordRow(
                    record.getId(),
                    record.getName(),
                    fondResponse,
                    !record.isActive(),
                    locked,
                    editationPhaseTransitions,
                    cells
            );

        } catch (Exception e) {
            throw new ConversionException("Exception while mapping record detail (%s) to grid cells: %s".formatted(record.getId(), e.getMessage()), e);
        }
    }

    private @NonNull Function<Field<?>, GridField> mapFieldToGridField() {
        return field -> {
            ScalarFieldValue<?> fieldValueHolder = field.getValueHolder();

            if (field.getError() != null) {
                Text text = fieldValueHolder == null ? Texts.ofEmpty() : fieldValueHolder.text();
                return new ErrorGridField(field.getFieldId(), text, ErroredFieldResponse.map(field.getError()), recordRef(field));
            }

            if (!field.getFields().isEmpty()) {
                Map<String, List<GridField>> subcells = collectAndGroup(field.streamFields());
                return new CompoundGridField(field.getFieldId(), field.getText(), subcells, recordRef(field));
            }

            // prazdna pole (bez hodnoty, bez recordu, bez podpoli, bez chyby) zkusime podporovat
            //Assert.state(fieldValueHolder != null, () -> "Cannot convert record to table cell labeled value, because field does not have any subfield and valueHolder is null (field %s)".formatted(field.getFieldId()));
            if (fieldValueHolder == null) {
                return new EmptyGridField(field.getFieldId(), Texts.ofEmpty(), recordRef(field));
            }

            if (fieldValueHolder instanceof AcceptableValueFieldValue<?> acceptableValueHolder) {
                return new OptionGridField<>(field.getFieldId(), acceptableValueHolder.value().getId(), acceptableValueHolder.text(), recordRef(field));
            }

            return new ScalarGridField<>(field.getFieldId(), fieldValueHolder.value(), fieldValueHolder.text(), recordRef(field));
        };
    }

    @Nullable
    private LabeledRecordRef recordRef(Field<?> field) {
        if (field.hasRecordLink()) {
            ScalarFieldValue<?> fieldValueHolder = field.getValueHolder();
            Text text = fieldValueHolder == null ? Texts.ofEmpty() : fieldValueHolder.text();
            return LabeledRecordRef.ofRecordLink(field.getExistingRecordLink(), text);
        }
        return null;
    }

    private @NonNull <F extends Field<?>> Map<String, List<GridField>> collectAndGroup(@NonNull Stream<F> fieldStream) {
        return fieldStream.collect(groupingBy(Field::getTypeId, mapping(mapFieldToGridField(), toList())));
    }

    private @NonNull RecordEditationPhaseTransitionResponse mapPhaseTransition(RecordEditationPhaseTransition transition) {
        return new RecordEditationPhaseTransitionResponse(
                transition.targetPhase(),
                permissionResultToResponseConverter.apply(transition.permission())
        );
    }
}
