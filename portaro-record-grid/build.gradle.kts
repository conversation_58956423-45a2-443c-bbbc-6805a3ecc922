dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-grid"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-web:6.+")

    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("org.hibernate.orm:hibernate-core:6.5.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
}
