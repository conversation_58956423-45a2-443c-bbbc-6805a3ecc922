package cz.kpsys.portaro.record.export.ris;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.export.AbstractToTextualFileExporter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.document.DocumentContentTypeResolver;
import cz.kpsys.portaro.record.print.RecordDetailRawPrinter;
import cz.kpsys.portaro.record.print.RecordDetailUserFriendlyTextPrinter;
import cz.kpsys.portaro.template.TemplateDescriptor;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.TypeDescriptor;

import java.util.Locale;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RisFileDocumentExporter extends AbstractToTextualFileExporter<Record> {

    @NonNull TemplateEngine templateEngine;
    @NonNull TemplateDescriptor template;
    @NonNull DocumentContentTypeResolver documentContentTypeResolver;
    @NonNull Translator<Department> translator;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordsConverter;

    public RisFileDocumentExporter(String filename,
                                   @NonNull TemplateDescriptor template,
                                   @NonNull TemplateEngine templateEngine,
                                   @NonNull DocumentContentTypeResolver documentContentTypeResolver,
                                   @NonNull Translator<Department> translator,
                                   @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordsConverter) {
        super(filename);
        this.template = template;
        this.templateEngine = templateEngine;
        this.documentContentTypeResolver = documentContentTypeResolver;
        this.translator = translator;
        this.recordsToViewableRecordsConverter = recordsToViewableRecordsConverter;
    }

    @Override
    public String exportToString(Record record, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale) {
        Map<String, Object> model = Map.of(
                "record", recordsToViewableRecordsConverter.convertSingle(record, currentAuth, ctx, locale),
                "recordPrinter", new RecordDetailUserFriendlyTextPrinter(translator, ctx, locale),
                "recordPlainPrinter", new RecordDetailRawPrinter(translator, ctx, locale),
                "documentContentType", documentContentTypeResolver.resolve(record)
        );
        return templateEngine.build(template, currentAuth, ctx, model, locale);
    }

    @Override
    public TypeDescriptor getType() {
        return TypeDescriptor.valueOf(Record.class);
    }
}