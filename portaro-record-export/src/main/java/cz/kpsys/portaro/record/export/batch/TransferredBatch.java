package cz.kpsys.portaro.record.export.batch;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.id.UuidGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.UUID;

public record TransferredBatch(

        @NonNull
        UUID id,

        @NonNull
        UUID transferId,

        @NonNull
        DateRange dateRange,

        @NonNull
        Instant startDate,

        @With(AccessLevel.PRIVATE)
        @Nullable
        Instant endDate,

        @With(AccessLevel.PRIVATE)
        @NullableNotBlank
        String errorMessage

) implements IdentifiedRecord<UUID> {

    public static int ERROR_MESSAGE_MAX_LENGTH = 1024;

    public static TransferredBatch createJustStarted(@NonNull UUID transferId,
                                                     @NonNull Instant fromDate) {
        return new TransferredBatch(
                UuidGenerator.forIdentifier(),
                transferId,
                new DateRange(fromDate, Instant.now()),
                Instant.now(),
                null,
                null
        );
    }

    public TransferredBatch withJustFailed(Exception e) {
        String trimmedErrorMessage = StringUtil.limitCharsAndTrimWithoutEllipsis(e.getMessage(), TransferredBatch.ERROR_MESSAGE_MAX_LENGTH, true);
        return withEndDate(Instant.now()).withErrorMessage(trimmedErrorMessage);
    }

    public TransferredBatch withJustSucceeded() {
        return withEndDate(Instant.now());
    }

    @Override
    public boolean equals(Object o) {
        return this == o || (o instanceof TransferredBatch that && id.equals(that.id));
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
