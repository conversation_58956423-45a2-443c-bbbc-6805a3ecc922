package cz.kpsys.portaro.auth.oauth2;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UnsupportedGrantTypeException extends RuntimeException implements UserFriendlyException {

    @Getter @NonNull Text text;

    public UnsupportedGrantTypeException(@NonNull String grantType, @NonNull Text text) {
        super("Unsupported grant type " + grantType);
        this.text = text;
    }
    
}
