<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="portaro [bootRun] on sutortest.systemist.cz" type="GradleRunConfiguration" factoryName="Gradle">
    <ExternalSystemSettings>
      <option name="env">
        <map>
          <entry key="APPSERVER_URL" value="http://*************:8188" />
          <entry key="DATABASE_DBUPDATEPASSWORD" value="Re13gi11st" />
          <entry key="DATABASE_DBUPDATEUSERNAME" value="root" />
          <entry key="DATABASE_FILE" value="db_verbis" />
          <entry key="DATABASE_HOST" value="*************" />
          <entry key="DATABASE_PASSWORD" value="Re13gi11st" />
          <entry key="DATABASE_PORT" value="5438" />
          <entry key="DATABASE_USERNAME" value="root" />
          <entry key="PORTARO_INI__OPAC_WEB__InternalHttpsEnabled" value="ANO" />
          <entry key="PORTARO_INI__OPAC__ForceHttps" value="NE" />
          <entry key="PORTARO_INI__OPAC__URL" value="http://localhost" />
          <entry key="PORTARO_INI__file_portaroBlobDirFilesDirectLoad" value="NE" />
        </map>
      </option>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/portaro-runner" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-Penv=develop --warning-mode=all --stacktrace -Ptested" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="bootRun" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>