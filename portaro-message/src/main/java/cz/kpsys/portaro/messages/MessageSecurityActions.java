package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.security.Action;

import java.util.UUID;

public class MessageSecurityActions {

    public static final Action<Void> MESSAGE_SEARCH = Action.withoutSubject("MessageSearch");
    public static final Action<UUID> THREAD_MESSAGE_SEARCH = Action.withSubject("ThreadMessageSearch", UUID.class);
    public static final Action<UUID> EDIT_THREAD = Action.withSubject("ThreadEdit", UUID.class);
}
