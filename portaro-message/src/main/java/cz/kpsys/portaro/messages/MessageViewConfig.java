package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.searchoredit.SearchOrEditValueEditor;
import cz.kpsys.portaro.form.valueeditor.searchoredit.StaticSearchParams;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.messages.constants.*;
import cz.kpsys.portaro.messages.converter.MessageSendingToResponseConverter;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.MessageSending;
import cz.kpsys.portaro.messages.dto.UserToThreadMessage;
import cz.kpsys.portaro.messages.dto.UserToUserMessage;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import cz.kpsys.portaro.search.view.CompositeSearchFormFactory;
import cz.kpsys.portaro.search.view.SearchFormFactory;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.view.SimpleViewableItemsTypedConverter;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import static cz.kpsys.portaro.form.editedproperty.EditedProperty.createWithProperty;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Configuration
public class MessageViewConfig {

    @NonNull PageSearchLoader<MapBackedParams, Message, Paging> messageSearchLoader;
    @NonNull PageSearchLoader<MapBackedParams, MessageSending, RangePaging> messageSendingSearchLoader;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull CompositeSearchFormFactory searchFormFactory;
    @NonNull SecurityManager securityManager;


    @Bean
    public SearchFactory<Paging> messageSearchFactory() {
        return new SearchFactoryMatching<>(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleKind(BasicMapSearchParams.KIND_MESSAGE)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Message, Paging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_MESSAGE),
                        MessageConstants.SearchParams.SEVERITY, MessageSeverity.CODEBOOK.getAll(),
                        MessageConstants.SearchParams.MESSAGE_STATUS, MessageSendingsSendStatus.CODEBOOK.getAll()
                ));

                return new PageSearchLoaderSearch<>(messageSearchLoader)
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withParamsPermissionChecker(new MessageLoaderPermissionChecker(securityManager));
            }
        };
    }

    @Bean
    public SearchFactory messageSendingSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherBySingleKind(BasicMapSearchParams.KIND_MESSAGE_SENDING)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, MessageSending, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_MESSAGE_SENDING),
                        MessageConstants.SearchParams.SEVERITY, MessageSeverity.CODEBOOK.getAll(),
                        MessageConstants.SearchParams.MESSAGE_MEDIUM, List.of(MessageMedium.INTERNAL)
                ));

                return new PageSearchLoaderSearch<>(messageSendingSearchLoader)
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public SearchFormFactory messageSearchTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("message-search", Texts.ofMessageCoded("hledani.kriteria"));

                form.addField(createWithProperty(MessageConstants.SearchParams.TARGET_USER,
                        SearchOrEditValueEditor.getEmptyEditor()
                                .withType(ValueEditorAliasType.USER)
                                .withStaticSearchParams(StaticSearchParams.createOfKindAndType(BasicMapSearchParams.KIND_USER, SearchViewConstants.TYPE_USER_SEARCH))
                                .withAllowToDeleteValue(true)));

                form.addField(createWithProperty(MessageConstants.SearchParams.SENDER_USER,
                        SearchOrEditValueEditor.getEmptyEditor()
                                .withType(ValueEditorAliasType.USER)
                                .withStaticSearchParams(StaticSearchParams.createOfKindAndType(BasicMapSearchParams.KIND_USER, SearchViewConstants.TYPE_USER_SEARCH))
                                .withAllowToDeleteValue(true)));

                form.addField(createWithProperty(MessageConstants.SearchParams.CREATE_DATE,
                        DateValueEditor.getEmptyEditor()));

                form.addField(createWithProperty(MessageConstants.SearchParams.MESSAGE_STATUS,
                        MultipleAcceptableValueEditor.getEmptyEditor(MessageSendingsSendStatus.CODEBOOK)));

                form.addField(createWithProperty(MessageConstants.SearchParams.SEVERITY,
                        MultipleAcceptableValueEditor.getEmptyEditor(MessageSeverity.CODEBOOK)));

                form.addField(createWithProperty(MessageConstants.SearchParams.TOPIC,
                        MultipleAcceptableValueEditor.getEmptyEditor(MessageTopic.CODEBOOK)));

                form.addField(createWithProperty(MessageConstants.SearchParams.MESSAGE_MEDIUM,
                        MultipleAcceptableValueEditor.getEmptyEditor(MessageMedium.CODEBOOK)));

                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public CommandLineRunner messageSearchRegistrar() {
        return args -> {
            searchFactoryResolver.withStandardOrder(messageSearchFactory());

            // TODO tady se bude muset nějak vyřešit jestli je co je potřeba zeregistrovat.
            viewableItemsConverter.registerStandardNoop(UserToUserMessage.class);
//            viewableItemsConverter.registerStandardNoop(UserToThreadMessage.class);
            searchFormFactory.add(SearchViewConstants.TYPE_MESSAGE_SEARCH, messageSearchTypeSearchFormFactory());

            searchFactoryResolver.withStandardOrder(messageSendingSearchFactory());
            viewableItemsConverter.register(MessageSending.class, ViewableItemsConverter.ViewMode.STANDARD, new SimpleViewableItemsTypedConverter<>(new ListToModifiedListConverter<>(new MessageSendingToResponseConverter())));
            viewableItemsConverter.register(UserToThreadMessage.class, ViewableItemsConverter.ViewMode.STANDARD, (messages, currentAuth, ctx, locale, exports, additionalModel) -> getObjects(messages));
        };
    }

    private static @NonNull List<? extends Object> getObjects(List<UserToThreadMessage> messages) {
        return messages;
    }

}
