package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Locale;
import java.util.Map;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class MessageToViewableMessageConverter implements ViewableItemsTypedConverter<Message, ViewableMessage> {


    @Override
    public @NonNull List<ViewableMessage> convert(@NonNull List<Message> messages, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        return List.of();
    }

    @Override
    public @NonNull ViewableMessage convertSingle(@NonNull Message message, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        return new ViewableMessage(
                message.id(),
        );
    }
}