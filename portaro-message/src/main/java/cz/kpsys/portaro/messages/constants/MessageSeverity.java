package cz.kpsys.portaro.messages.constants;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum MessageSeverity implements LabeledIdentified<String> {

    INFO("info", Texts.ofMessageCoded("message.severity.Info")),
    WARNING("warning", Texts.ofMessageCoded("message.severity.Warning")),
    URGENT("urgent", Texts.ofMessageCoded("message.severity.Urgent"));

    public static final Codebook<MessageSeverity, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull Text text;
}
