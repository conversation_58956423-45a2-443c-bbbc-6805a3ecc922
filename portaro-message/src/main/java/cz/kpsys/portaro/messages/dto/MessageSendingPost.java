package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;


@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public final class MessageSendingPost extends MessageSending {

    @NotBlank
    @NonNull
    String address;

    @Nullable
    Integer fulltextId;

    @Nullable
    Event printingEvent;

    public MessageSendingPost(@NonNull UUID id,
                              @NonNull UUID messageId,
                              @Nullable Event sendingEvent,
                              @Nullable Event receiptionEvent,
                              @Nullable Event confirmationEvent,
                              @NonNull String address,
                              @Nullable Integer fulltextId,
                              @Nullable Event printingEvent) {
        super(id, messageId, MessageMedium.POST, sendingEvent, receiptionEvent, confirmationEvent);
        this.address = address;
        this.fulltextId = fulltextId;
        this.printingEvent = printingEvent;
    }
}
