package cz.kpsys.portaro.auth.details;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.user.AuthableUser;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

public interface UserHoldingUserDetails extends UserDetails {

    AuthableUser getUser();

    @Override
    default Collection<? extends GrantedAuthority> getAuthorities() {
        return ListUtil.convert(getUser().getRole(), SimpleGrantedAuthority::new);
    }

    /**
     * Vzdy vracime null - nebudeme heslo exposovat ven - obecne heslo u uzivatele nemusi byt evidovano
     */
    @Override
    default String getPassword() {
        return null;
    }

    @Override
    default String getUsername() {
        return getUser().getUsername();
    }

    @Override
    default boolean isAccountNonExpired() {
        return true;
    }

    @Override
    default boolean isAccountNonLocked() {
        return true;
    }

    @Override
    default boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    default boolean isEnabled() {
        return true;
    }
}
