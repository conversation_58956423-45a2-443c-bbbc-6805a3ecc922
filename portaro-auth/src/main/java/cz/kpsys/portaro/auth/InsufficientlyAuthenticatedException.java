package cz.kpsys.portaro.auth;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.logging.LogOnlyAsDebug;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@LogOnlyAsDebug
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class InsufficientlyAuthenticatedException extends RuntimeException implements UserFriendlyException {

    Text text = Texts.ofMessageCoded("login.NotVerified");

    public InsufficientlyAuthenticatedException() {
        super("User is not authenticated sufficiently for that action");
    }

    public static void throwIfNot(boolean condition) {
        if (!condition) {
            throw new InsufficientlyAuthenticatedException();
        }
    }

}
