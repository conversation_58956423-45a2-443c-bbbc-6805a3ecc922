package cz.kpsys.portaro.auth;

import cz.kpsys.portaro.auth.process.StaticSuccessAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThisThreadAuthenticatingDynamicRunner implements BiConsumer<Consumer<UserAuthentication>, Department> {

    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<? extends AuthableUser> userProvider;

    @Override
    public void accept(@NonNull Consumer<UserAuthentication> authenticatedRunner, Department ctx) {
        StaticSuccessAuthentication authentication = createAuthentication(ctx);
        authenticationHolder.authenticated(authentication, authenticatedRunner);
    }

    @NonNull
    private StaticSuccessAuthentication createAuthentication(@NonNull Department ctx) {
        return StaticSuccessAuthentication.authenticatedForBackgroundJobs(userProvider.get(), ctx);
    }

}
