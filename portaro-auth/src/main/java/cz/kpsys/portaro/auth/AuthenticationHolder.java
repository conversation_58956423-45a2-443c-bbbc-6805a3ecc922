package cz.kpsys.portaro.auth;

import cz.kpsys.portaro.auth.composite.CompositeSuccessAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuthProvider;
import cz.kpsys.portaro.auth.current.resolver.CompositeUserAuthenticationAuthenticationResolver;
import cz.kpsys.portaro.auth.current.resolver.UserAuthenticationAuthenticationResolver;
import cz.kpsys.portaro.auth.listen.CompositeSuccessAuthenticationListener;
import cz.kpsys.portaro.auth.listen.SuccessAuthenticationListener;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AuthenticationHolder implements BasicAuthenticationHolder, AuthenticationSetter, AuthenticationCleaner, AuthenticationProvider, CurrentAuthProvider {

    @NonNull UserAuthenticationAuthenticationResolver userAuthenticationAuthenticationResolver;
    @NonNull CompositeSuccessAuthenticationListener successAuthenticationListener = new CompositeSuccessAuthenticationListener();

    public static AuthenticationHolder ofForced(AuthoritiedSuccessAuthentication authentication) {
        AuthenticationHolder holder = new AuthenticationHolder(new CompositeUserAuthenticationAuthenticationResolver());
        holder.addAuthentication(authentication);
        return holder;
    }

    public AuthenticationHolder addOnEvidedSuccessListener(@NonNull SuccessAuthenticationListener listener) {
        successAuthenticationListener.addOnEvided(listener);
        return this;
    }


    @Override
    public boolean isPresent() {
        return SecurityContextHolder.getContext().getAuthentication() != null;
    }


    @Override
    public UserAuthentication getCurrentAuth() throws AuthenticationObjectNotExistsException {
        Authentication authentication = getAuthentication();
        return userAuthenticationAuthenticationResolver.resolve(authentication);
    }


    @NonNull
    @Override
    public CompositeSuccessAuthentication getAuthentication() throws AuthenticationObjectNotExistsException {
        if (!isPresent()) {
            throw new AuthenticationObjectNotExistsException();
        }
        return getOrCreateComposite();
    }


    @Override
    public void setAuthentication(CompositeSuccessAuthentication compositeAuth) {
        SecurityContextHolder.getContext().setAuthentication(compositeAuth);
    }


    @Override
    public void addAuthentication(AuthoritiedSuccessAuthentication authentication) {
        CompositeSuccessAuthentication compositeAuth = getOrCreateComposite();
        compositeAuth.add(authentication);
        successAuthenticationListener.onSuccessAuth(authentication);
    }


    @Override
    public void clearAuthentication() {
        SecurityContextHolder.getContext().setAuthentication(null);
    }


    public <U> CompletableFuture<U> asyncAuthenticated(AuthoritiedSuccessAuthentication authentication, Executor executor, Function<UserAuthentication, U> authenticatedFunction) {
        return CompletableFuture.supplyAsync(() -> authenticated(authentication, authenticatedFunction), executor);
    }


    public <U> U authenticated(AuthoritiedSuccessAuthentication authentication, Function<UserAuthentication, U> authenticatedFunction) {
        try {
            addAuthentication(authentication);
            UserAuthentication currentAuth = getCurrentAuth();
            return authenticatedFunction.apply(currentAuth);
        } finally {
            clearAuthentication();
        }
    }


    public void authenticated(AuthoritiedSuccessAuthentication authentication, Consumer<UserAuthentication> runnable) {
        try {
            addAuthentication(authentication);
            UserAuthentication currentAuth = getCurrentAuth();
            runnable.accept(currentAuth);
        } finally {
            clearAuthentication();
        }
    }


    private CompositeSuccessAuthentication getOrCreateComposite() {
        if (!isPresent()) {
            CompositeSuccessAuthentication compositeAuth = CompositeSuccessAuthentication.ofEmpty();
            SecurityContextHolder.getContext().setAuthentication(compositeAuth);
            return compositeAuth;
        }

        Authentication current = SecurityContextHolder.getContext().getAuthentication();
        Assert.isInstanceOf(AuthoritiedSuccessAuthentication.class, current);

        if (current instanceof CompositeSuccessAuthentication compositeSuccessAuthentication) {
            return compositeSuccessAuthentication;
        }

        CompositeSuccessAuthentication compositeAuth = CompositeSuccessAuthentication.ofSingle((AuthoritiedSuccessAuthentication) current);
        SecurityContextHolder.getContext().setAuthentication(compositeAuth);
        return compositeAuth;
    }

}
