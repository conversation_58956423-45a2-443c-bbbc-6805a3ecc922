package cz.kpsys.portaro.auth.current.resolver;

import cz.kpsys.portaro.auth.composite.CompositeSuccessAuthentication;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeAuthenticationAuthoritiesResolver implements Function<CompositeSuccessAuthentication, Collection<? extends GrantedAuthority>> {

    @NonNull UserAuthenticationAuthenticationResolver userAuthenticationAuthenticationResolver;
    @NonNull Set<Class<? extends AuthoritiedSuccessAuthentication>> standaloneAuthentications = new HashSet<>();

    public CompositeAuthenticationAuthoritiesResolver addStandalone(Class<? extends AuthoritiedSuccessAuthentication> standaloneAuthClass) {
        standaloneAuthentications.add(standaloneAuthClass);
        return this;
    }

    @Override
    public Collection<? extends GrantedAuthority> apply(CompositeSuccessAuthentication authentication) {
        List<AuthoritiedSuccessAuthentication> auths = new ArrayList<>(authentication.getAuths());

        List<AuthoritiedSuccessAuthentication> standalones = auths.stream()
                .filter(a -> standaloneAuthentications.contains(a.getClass()))
                .toList();
        Assert.state(standalones.size() <= 1, "Cannot resolve which of %s auths to use as standalone (auths: %s)".formatted(standalones.size(), standalones));
        if (!standalones.isEmpty()) {
            return userAuthenticationAuthenticationResolver.resolveRoles(standalones.getFirst());
        }

        return auths.stream()
                .flatMap(a -> userAuthenticationAuthenticationResolver.resolveRoles(a).stream())
                .collect(Collectors.toSet());
    }
}
