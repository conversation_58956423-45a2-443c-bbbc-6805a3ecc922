package cz.kpsys.portaro.userpreferences.gdpr;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.logging.LogOnlyAsDebug;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

@LogOnlyAsDebug
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class UserPreferenceNotSetException extends RuntimeException implements SeveritedException, AdditionalFieldsException, UserFriendlyException {

    @NonNull Text text;
    @NonNull List<Integer> userPreferenceKeys;
    int severity = SEVERITY_WARNING;

    public UserPreferenceNotSetException(@NonNull Text text, @NonNull List<Integer> userPreferenceKeys) {
        this.text = text;
        this.userPreferenceKeys = userPreferenceKeys;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of("userPreferenceKeys", userPreferenceKeys);
    }
}
