package cz.kpsys.portaro.ncip;

/**
 * Created by Jan on 16. 6. 2015.
 */
public class NcipConstants {

    public static final String MESSAGE_VERSION = "http://www.niso.org/schemas/ncip/v2_0/imp1/xsd/ncip_v2_0.xsd";


    public static final String USER_ID_USER_IDENTIFIER_TYPE_INSTITUTION_ID = "Institution Id Number";



    public static final String EXEMPLAR_ACCESSION_NUMBER_ITEM_IDENTIFIER_TYPE = "Accession Number";

    /**
     * melo to byt "Item Id", ale vsechna id nyni budou nazyvana "Accession Number"
     */
    public static final String EXEMPLAR_ITEM_ID_ITEM_IDENTIFIER_TYPE = EXEMPLAR_ACCESSION_NUMBER_ITEM_IDENTIFIER_TYPE;



    public static final String DOCUMENT_LOCAL_DEPOSIT_NUMBER = "Legal Deposit Number";



    public static final String ITEM_USE_RESTRICTION_TYPE__NOT_FOR_LOAN = "Not For Loan";
    public static final String ITEM_USE_RESTRICTION_TYPE__IN_LIBRARY_USE_ONLY = "In Library Use Only";
    public static final String ITEM_USE_RESTRICTION_TYPE__LIMITED_CIRCULATION = "Limited Circulation";
    public static final String ITEM_USE_RESTRICTION_TYPE__LONG_LOAN_PERIOD = "Limited Circulation, Long Loan Period";
    public static final String ITEM_USE_RESTRICTION_TYPE__NORMAL_LOAN_PERIOD = "Limited Circulation, Normal Loan Period";
    public static final String ITEM_USE_RESTRICTION_TYPE__SHORT_LOAN_PERIOD = "Limited Circulation, Short Loan Period";
    public static final String ITEM_USE_RESTRICTION_TYPE__OVERNIGHT_ONLY = "Overnight Only";
    public static final String ITEM_USE_RESTRICTION_TYPE__WITHOUT_RENEWABILITY = "Renewals Not Permitted";
    public static final String ITEM_USE_RESTRICTION_TYPE__IN_CONTROLLED_ACCESS_ONLY = "Use Only In Controlled Access";





    /**
     * Označuje související exemplář jako vyzvednutelný.
     */
    public static final String CIRCULATION_STATUS__AVAILABLE_FOR_PICKUP = "Available For Pickup";

    /**
     * Označuje související exemplář jako objednatelný.
     */
    public static final String CIRCULATION_STATUS__AVAILABLE_ON_SHELF = "Available On Shelf";

    /**
     * Označuje související exemplář neznámým výpůjčním statusem.
     */
    public static final String CIRCULATION_STATUS__UNDEFINED = "Circulation Status Undefined";

    /**
     * Označuje související exemplář jako vyřizující se..
     */
    public static final String CIRCULATION_STATUS__IN_PROCESS = "In Process";

    /**
     * Označuje související exemplář jako právě vypůjčený.
     */
    public static final String CIRCULATION_STATUS__ON_LOAN = "On Loan";

    /**
     */
    public static final String CIRCULATION_STATUS__ON_ORDER = "On Order";

    /**
     * Označuje související exemplář jako nedostupný.
     */
    public static final String CIRCULATION_STATUS__NOT_AVAILABLE = "Not Available";

    /**
     * Označuje související exemplář jako cestující mezi pobočkami.
     */
    public static final String CIRCULATION_STATUS__IN_TRANSIT_BETWEEN_LOCATIONS = "In Transit Between Library Locations";



}
