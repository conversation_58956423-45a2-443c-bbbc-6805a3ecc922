
package cz.kpsys.portaro.ncip.schema;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ResponseHeader" minOccurs="0"/>
 *         &lt;choice>
 *           &lt;element ref="{http://www.niso.org/2008/ncip}Problem" maxOccurs="unbounded"/>
 *           &lt;element ref="{http://www.niso.org/2008/ncip}ItemId"/>
 *         &lt;/choice>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "responseHeader",
    "problem",
    "itemId",
    "ext"
})
@XmlRootElement(name = "CreateItemResponse")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class CreateItemResponse {

    @XmlElement(name = "ResponseHeader")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ResponseHeader responseHeader;
    @XmlElement(name = "Problem")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<Problem> problem;
    @XmlElement(name = "ItemId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ItemId itemId;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the responseHeader property.
     * 
     * @return
     *     possible object is
     *     {@link ResponseHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ResponseHeader getResponseHeader() {
        return responseHeader;
    }

    /**
     * Sets the value of the responseHeader property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setResponseHeader(ResponseHeader value) {
        this.responseHeader = value;
    }

    /**
     * Gets the value of the problem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the problem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getProblem().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Problem }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<Problem> getProblem() {
        if (problem == null) {
            problem = new ArrayList<Problem>();
        }
        return this.problem;
    }

    /**
     * Gets the value of the itemId property.
     * 
     * @return
     *     possible object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ItemId getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setItemId(ItemId value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
