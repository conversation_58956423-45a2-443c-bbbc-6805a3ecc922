
package cz.kpsys.portaro.ncip.schema;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}InitiationHeader" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ItemId"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}RequestId" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}BibliographicDescription"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ItemUseRestrictionType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}CirculationStatus" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}ItemDescription" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Location" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}PhysicalCondition" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}SecurityMarker" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}SensitizationFlag" minOccurs="0"/>
 *         &lt;element ref="{http://www.niso.org/2008/ncip}Ext" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "initiationHeader",
    "itemId",
    "requestId",
    "bibliographicDescription",
    "itemUseRestrictionType",
    "circulationStatus",
    "itemDescription",
    "location",
    "physicalCondition",
    "securityMarker",
    "sensitizationFlag",
    "ext"
})
@XmlRootElement(name = "ItemCreated")
@Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
public class ItemCreated {

    @XmlElement(name = "InitiationHeader")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected InitiationHeader initiationHeader;
    @XmlElement(name = "ItemId", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ItemId itemId;
    @XmlElement(name = "RequestId")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected RequestId requestId;
    @XmlElement(name = "BibliographicDescription", required = true)
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected BibliographicDescription bibliographicDescription;
    @XmlElement(name = "ItemUseRestrictionType")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<SchemeValuePair> itemUseRestrictionType;
    @XmlElement(name = "CirculationStatus")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair circulationStatus;
    @XmlElement(name = "ItemDescription")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected ItemDescription itemDescription;
    @XmlElement(name = "Location")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected List<Location> location;
    @XmlElement(name = "PhysicalCondition")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected PhysicalCondition physicalCondition;
    @XmlElement(name = "SecurityMarker")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SchemeValuePair securityMarker;
    @XmlElement(name = "SensitizationFlag")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected SensitizationFlag sensitizationFlag;
    @XmlElement(name = "Ext")
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    protected Ext ext;

    /**
     * Gets the value of the initiationHeader property.
     * 
     * @return
     *     possible object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public InitiationHeader getInitiationHeader() {
        return initiationHeader;
    }

    /**
     * Sets the value of the initiationHeader property.
     * 
     * @param value
     *     allowed object is
     *     {@link InitiationHeader }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setInitiationHeader(InitiationHeader value) {
        this.initiationHeader = value;
    }

    /**
     * Gets the value of the itemId property.
     * 
     * @return
     *     possible object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ItemId getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link ItemId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setItemId(ItemId value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the requestId property.
     * 
     * @return
     *     possible object is
     *     {@link RequestId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public RequestId getRequestId() {
        return requestId;
    }

    /**
     * Sets the value of the requestId property.
     * 
     * @param value
     *     allowed object is
     *     {@link RequestId }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setRequestId(RequestId value) {
        this.requestId = value;
    }

    /**
     * Gets the value of the bibliographicDescription property.
     * 
     * @return
     *     possible object is
     *     {@link BibliographicDescription }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public BibliographicDescription getBibliographicDescription() {
        return bibliographicDescription;
    }

    /**
     * Sets the value of the bibliographicDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link BibliographicDescription }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setBibliographicDescription(BibliographicDescription value) {
        this.bibliographicDescription = value;
    }

    /**
     * Gets the value of the itemUseRestrictionType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the itemUseRestrictionType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getItemUseRestrictionType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SchemeValuePair }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<SchemeValuePair> getItemUseRestrictionType() {
        if (itemUseRestrictionType == null) {
            itemUseRestrictionType = new ArrayList<SchemeValuePair>();
        }
        return this.itemUseRestrictionType;
    }

    /**
     * Gets the value of the circulationStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getCirculationStatus() {
        return circulationStatus;
    }

    /**
     * Sets the value of the circulationStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setCirculationStatus(SchemeValuePair value) {
        this.circulationStatus = value;
    }

    /**
     * Gets the value of the itemDescription property.
     * 
     * @return
     *     possible object is
     *     {@link ItemDescription }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public ItemDescription getItemDescription() {
        return itemDescription;
    }

    /**
     * Sets the value of the itemDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link ItemDescription }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setItemDescription(ItemDescription value) {
        this.itemDescription = value;
    }

    /**
     * Gets the value of the location property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the location property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLocation().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Location }
     * 
     * 
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public List<Location> getLocation() {
        if (location == null) {
            location = new ArrayList<Location>();
        }
        return this.location;
    }

    /**
     * Gets the value of the physicalCondition property.
     * 
     * @return
     *     possible object is
     *     {@link PhysicalCondition }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public PhysicalCondition getPhysicalCondition() {
        return physicalCondition;
    }

    /**
     * Sets the value of the physicalCondition property.
     * 
     * @param value
     *     allowed object is
     *     {@link PhysicalCondition }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setPhysicalCondition(PhysicalCondition value) {
        this.physicalCondition = value;
    }

    /**
     * Gets the value of the securityMarker property.
     * 
     * @return
     *     possible object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SchemeValuePair getSecurityMarker() {
        return securityMarker;
    }

    /**
     * Sets the value of the securityMarker property.
     * 
     * @param value
     *     allowed object is
     *     {@link SchemeValuePair }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setSecurityMarker(SchemeValuePair value) {
        this.securityMarker = value;
    }

    /**
     * Gets the value of the sensitizationFlag property.
     * 
     * @return
     *     possible object is
     *     {@link SensitizationFlag }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public SensitizationFlag getSensitizationFlag() {
        return sensitizationFlag;
    }

    /**
     * Sets the value of the sensitizationFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link SensitizationFlag }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setSensitizationFlag(SensitizationFlag value) {
        this.sensitizationFlag = value;
    }

    /**
     * Gets the value of the ext property.
     * 
     * @return
     *     possible object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public Ext getExt() {
        return ext;
    }

    /**
     * Sets the value of the ext property.
     * 
     * @param value
     *     allowed object is
     *     {@link Ext }
     *     
     */
    @Generated(value = "com.sun.tools.internal.xjc.Driver", date = "2014-12-15T07:55:33+01:00", comments = "JAXB RI v2.2.4-2")
    public void setExt(Ext value) {
        this.ext = value;
    }

}
