package cz.kpsys.portaro.test.e2e.web.apicaller;

import io.restassured.response.Response;
import lombok.NonNull;

import java.util.Map;

public interface ApiCaller {

    Response get(@NonNull String path);

    Response getWithHeaders(@NonNull String path, Map<String, String> headers);

    void post(@NonNull String path, @NonNull Object body);

    Response postXmlWithResponse(@NonNull String path, @NonNull Object body);

    Response postWithoutBody(@NonNull String path);

    Response postWithResponse(@NonNull String path, @NonNull Object body);

}
