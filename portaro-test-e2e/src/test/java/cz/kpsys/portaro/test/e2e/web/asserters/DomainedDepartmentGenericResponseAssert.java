package cz.kpsys.portaro.test.e2e.web.asserters;

import io.restassured.response.Response;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import static cz.kpsys.portaro.test.e2e.web.utils.ResponseDataExtractor.extractAsBoolean;
import static cz.kpsys.portaro.test.e2e.web.utils.ResponseDataExtractor.extractAsString;
import static org.junit.Assert.assertEquals;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@NonFinal
@Slf4j
public class DomainedDepartmentGenericResponseAssert extends GenericResponseAssert {

    public DomainedDepartmentGenericResponseAssert(@NonNull Response resp) {
        super(resp);
    }

    public DomainedDepartmentGenericResponseAssert assertHttpStatusOk() {
        return (DomainedDepartmentGenericResponseAssert) super.assertHttpStatusOk();
    }


    public DomainedDepartmentGenericResponseAssert assertLocationUrl(@NonNull String locationUrl) {
        assertEquals(locationUrl, extractAsString(resp, "url"));
        return this;
    }

    public DomainedDepartmentGenericResponseAssert assertFinished(@NonNull Boolean finished) {
        assertEquals(finished, extractAsBoolean(resp, "finished"));
        return this;
    }

    public DomainedDepartmentGenericResponseAssert assertResponseType(@NonNull String responseType) {
        assertEquals(responseType, extractAsString(resp, "responseType"));
        return this;
    }
}
