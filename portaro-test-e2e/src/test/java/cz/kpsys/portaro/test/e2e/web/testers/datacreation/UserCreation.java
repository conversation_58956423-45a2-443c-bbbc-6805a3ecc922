package cz.kpsys.portaro.test.e2e.web.testers.datacreation;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

import static cz.kpsys.portaro.test.e2e.TestConstants.LIBRARIAN_FIRST_NAME;
import static cz.kpsys.portaro.test.e2e.TestConstants.LIBRARIAN_LAST_NAME;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserCreation {

    public Map<String, Object> createEditorUserJson(@NonNull String username, @NonNull String newPassword, @NonNull Integer groupId) {
        return Map.ofEntries(
                Map.entry("active", true),
                Map.entry("kind", "PERSON"),
                Map.entry("username", username),
                Map.entry("newPassword", newPassword),
                Map.entry("lastName", LIBRARIAN_LAST_NAME),
                Map.entry("firstName", LIBRARIAN_FIRST_NAME),
                Map.entry("emails", List.of(createEmailJson("Tester"))),
                Map.entry("addresses", List.of(createAddressJson())),
                Map.entry("birthDate", "1999-09-29T15:53:18.000Z"),
                Map.entry("editorAccounts", List.of(createEditorAccountJson(groupId))),
                Map.entry("editableDepartments", List.of(0, 1))
        );
    }

    public Map<String, Object> createInstitutionJson(@NonNull String username) {
        return Map.ofEntries(
                Map.entry("active", true),
                Map.entry("kind", "INSTITUTION"),
                Map.entry("institutionName", username),
                Map.entry("emails", List.of(createEmailJson("Tester"))),
                Map.entry("addresses", List.of(createAddressJson())),
                Map.entry("editableDepartments", List.of(0, 1))
        );
    }

    public Map<String, Object> createLibraryJson(@NonNull String username) {
        return Map.ofEntries(
                Map.entry("active", true),
                Map.entry("kind", "LIBRARY"),
                Map.entry("institutionName", username),
                Map.entry("emails", List.of(createEmailJson("Tester"))),
                Map.entry("addresses", List.of(createAddressJson())),
                Map.entry("editableDepartments", List.of(0, 1))
        );
    }

    public Map<String, Object> createFamilyJson(@NonNull String name, @NonNull String readerCategoryId) {
        return Map.ofEntries(
                Map.entry("active", true),
                Map.entry("kind", "FAMILY"),
                Map.entry("institutionName", name),
                Map.entry("readerAccounts", List.of(createReaderAccountJson(readerCategoryId)))
        );
    }

    public Map<String, Object> createReaderUserJson(@NonNull String username, @NonNull String newPassword, @NonNull String firstName, @NonNull String lastName, @NonNull String email, @NonNull String barCode) {
        return Map.of(
                "active", true,
                "kind", "PERSON",
                "username", username,
                "newPassword", newPassword,
                "lastName", lastName,
                "firstName", firstName,
                "emails", List.of(createEmailJson(email)),
                "birthDate", "1999-09-29T15:53:18.000Z",
                "readerAccounts", List.of(createReaderAccountJsonWithBarCode(barCode))
        );
    }

    public Map<String, Object> createReaderUserJson(@NonNull String username, @NonNull String newPassword, @NonNull String firstName, @NonNull String lastName, @NonNull String email, @NonNull String barCode, String readerCategoryId) {
        return Map.of(
                "active", true,
                "kind", "PERSON",
                "username", username,
                "newPassword", newPassword,
                "lastName", lastName,
                "firstName", firstName,
                "emails", List.of(createEmailJson(email)),
                "birthDate", "1999-09-29T15:53:18.000Z",
                "readerAccounts", List.of(createReaderAccountJsonWithBarCode(barCode, readerCategoryId))
        );
    }

    public Map<String, Object> createUnisUserJson(@NonNull String username, @NonNull String newPassword, @NonNull String netId, String readerCategoryId) {
        return Map.of(
                "active", true,
                "kind", "PERSON",
                "username", username,
                "newPassword", newPassword,
                "lastName", "PlaceholderLastName",
                "firstName", "PlaceholderFirstName",
                "emails", List.of(createEmailJson("PlaceholderLastName")),
                "birthDate", "1999-09-29T15:53:18.000Z",
                "netId", netId,
                "readerAccounts", List.of(createReaderAccountJson(readerCategoryId))
        );
    }

    private Map<String, Object> createEditorAccountJson(@NonNull Integer groupId) {
        return Map.of(
                "group", groupId,
                "editLevel", 99
        );
    }

    private Map<String, Object> createReaderAccountJson() {
        return createReaderAccountJson("DO");
    }

    private Map<String, Object> createReaderAccountJson(String readerCategoryId) {
        return Map.of(
                "readerCategory", readerCategoryId
        );
    }

    private Map<String, Object> createReaderAccountJsonWithBarCode(@NonNull String barCode) {
        return createReaderAccountJsonWithBarCode(barCode, "DO");
    }

    private Map<String, Object> createReaderAccountJsonWithBarCode(@NonNull String barCode, @NonNull String readerCategoryId) {
        return Map.of(
                "readerCategory", readerCategoryId,
                "barCode", barCode
        );
    }

    private Map<String, Object> createEmailJson(String email) {
        return Map.of(
                "source", "internal",
                "value", "%<EMAIL>".formatted(email)
        );
    }

    private Map<String, Object> createAddressJson() {
        return Map.of(
                "source", "internal",
                "city", "Pardubice",
                "street", "Testovaci 25",
                "postalCode", "53002",
                "country", "CZE"
        );
    }


}
