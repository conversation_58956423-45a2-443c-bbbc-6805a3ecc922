package cz.kpsys.portaro.test.e2e.web;

import cz.kpsys.portaro.test.e2e.TestEnvironmentFactory;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroGuiTester;
import cz.kpsys.portaro.test.e2e.web.testers.PortaroTester;
import cz.kpsys.portaro.test.e2e.web.utils.Junit5VncRecorder;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testcontainers.junit.jupiter.Testcontainers;

@Testcontainers
@Tag("ci")
@Tag("e2e")
@ExtendWith(Junit5VncRecorder.class)
public class PortaroWebHomepageTest {

    private PortaroTester portaroTester;
    private PortaroGuiTester portaroGuiTester;

    @BeforeAll
    public static void startupEnv() {
        TestEnvironmentFactory.startup();
        TestEnvironmentFactory.startPortaroGuiTester();
    }

    @BeforeEach
    public void startupTest() {
        portaroTester = TestEnvironmentFactory.portaroTester();
        portaroGuiTester = TestEnvironmentFactory.portaroGuiTester();
    }

    @AfterEach
    public void cleanup(TestInfo testInfo) {
        portaroGuiTester.getScreenshotAndLogout(testInfo);
        portaroTester.cleanup();
    }

    @AfterAll
    public static void cleanupEnv() {
        TestEnvironmentFactory.stopPortaroGuiTester();
    }

    @Test
    void shouldShowHomepage() {
        portaroGuiTester.getPortaroBrowserCaller().goToHomepage();
        portaroGuiTester.getPortaroBrowserCaller().assertTitleContains("Portaro - library catalog");
    }
}
