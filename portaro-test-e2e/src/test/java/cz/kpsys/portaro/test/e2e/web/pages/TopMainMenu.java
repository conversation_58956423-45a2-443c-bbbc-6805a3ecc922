package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class TopMainMenu {

    final PageUtils pageUtils;

    @FindBy(css = ".menu-id-2")
    WebElement searchMenuButton;

    @FindBy(css = ".menu-id-6")
    WebElement addDocumentMenuHover;

    @FindBy(css = ".menu-id-6_1")
    WebElement addDocumentMonographySubmenuButton;

    @FindBy(css = ".menu-id-8")
    WebElement readersMenuButton;

    @FindBy(css = ".menu-id-10018")
    WebElement loansMenuHover;

    @FindBy(css = ".menu-id-14000")
    WebElement loansToProcessMenuButton;

    @FindBy(css = ".menu-id-14001")
    WebElement loansToPickupMenuButton;

    @FindBy(css = ".menu-id-9")
    WebElement userAccountMenuButton;

    @FindBy(css = ".user-menu a")
    WebElement loginButton;

    @FindBy(css = ".user-menu a[data-qa='user-menu-item-user-account']")
    WebElement userAccountSubmenuButton;

    @FindBy(css = ".user-menu a[data-qa='user-menu-item-logout']")
    WebElement logoutSubmenuButton;

    @FindBy(css = ".user-menu .user-nav-item")
    WebElement loginButtonText;

    public TopMainMenu(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public TopMainMenu clickOnSearchMenuButton() {
        pageUtils.waitForElementAndClick(searchMenuButton);
        return this;
    }

    public TopMainMenu clickOnAddNewMonographyButton() {
        pageUtils.hoverAndClick(addDocumentMenuHover, addDocumentMonographySubmenuButton);
        return this;
    }

    public TopMainMenu clickOnReadersMenuButton() {
        pageUtils.waitForElementAndClick(readersMenuButton);
        return this;
    }

    public TopMainMenu clickOnLoansToProcessMenuButton() {
        pageUtils.hoverAndClick(loansMenuHover, loansToProcessMenuButton);
        return this;
    }

    public TopMainMenu clickOnLoansToPickupMenuButton() {
        pageUtils.hoverAndClick(loansMenuHover, loansToPickupMenuButton);
        return this;
    }

    public TopMainMenu clickOnLoginButton() {
        loginButton.click();
        return this;
    }

    public TopMainMenu assertLoginButtonText(String value) {
        pageUtils.waitForElementContent(loginButtonText, value);
        return this;
    }

    public TopMainMenu clickOnUserAccountSubmenuButton() {
        pageUtils.hoverAndClick(loginButton, userAccountSubmenuButton);
        return this;
    }

    public TopMainMenu clickOnLogoutSubmenuButton() {
        pageUtils.hoverAndClick(loginButton, logoutSubmenuButton);
        return this;
    }

    public TopMainMenu clickOnUserAccountMenuButton() {
        userAccountMenuButton.click();
        return this;
    }
}
