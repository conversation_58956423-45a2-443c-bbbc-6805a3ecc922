package cz.kpsys.portaro.ext.obalkyknih;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.StaticContextualDelegatingProvider;
import cz.kpsys.portaro.commons.convert.EToEConverter;
import cz.kpsys.portaro.commons.io.ExternalServiceFailoverer;
import cz.kpsys.portaro.commons.io.Failoverer;
import cz.kpsys.portaro.commons.io.RemoteFileDataStreamer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdDeleter;
import cz.kpsys.portaro.commons.object.repo.PreConvertingByIdDeleter;
import cz.kpsys.portaro.commons.object.repo.PreConvertingSaver;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.webclient.*;
import cz.kpsys.portaro.database.FlushingJpaByIdDeleter;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.obalkyknih.cover.ObalkyKnihApiCoverLoader;
import cz.kpsys.portaro.ext.obalkyknih.db.Obalkyknih;
import cz.kpsys.portaro.ext.obalkyknih.db.ObalkyknihEntity;
import cz.kpsys.portaro.ext.obalkyknih.db.ObalkyknihToEntityConverter;
import cz.kpsys.portaro.ext.obalkyknih.db.SpringDbObalkyknihSearchLoader;
import cz.kpsys.portaro.ext.obalkyknih.metadata.ObalkyknihAuthorityMetadataLoader;
import cz.kpsys.portaro.ext.obalkyknih.metadata.ObalkyknihDocumentMetadataLoader;
import cz.kpsys.portaro.ext.obalkyknih.web.ObalkyknihConstants;
import cz.kpsys.portaro.ext.obalkyknih.web.ObalkyknihHttpHeaders;
import cz.kpsys.portaro.ext.obalkyknih.web.ObalkyknihRequestParamFactory;
import cz.kpsys.portaro.file.FileExtensionDetector;
import cz.kpsys.portaro.file.ImageDownloader;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.httpclient.RestOperationsFactory;
import cz.kpsys.portaro.logging.TraceIdRepository;
import cz.kpsys.portaro.record.file.cover.CoverDownloader;
import cz.kpsys.portaro.record.file.cover.CoverFactory;
import cz.kpsys.portaro.record.file.cover.FailoveredCoverDownloader;
import cz.kpsys.portaro.record.file.cover.NamedCoverLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.ParameterizedSearchLoaderImpl;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ObalkyknihConfig {

    public static final Pattern OBALKYKNIH_PATTERN = Pattern.compile("^(https?://)?cache\\d?\\.obalkyknih\\.cz");

    @NonNull Provider<@NonNull Department> rootDepartmentProvider;
    @NonNull Provider<@NullableNotBlank String> rootServerUrlProvider;
    @NonNull Provider<@NonNull List<@NonNull String>> allSiglasProvider;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> nullableSiglaProvider;
    @NonNull HttpFactory httpFactory;
    @NonNull TraceIdRepository traceIdRepository;
    @NonNull Provider<@NonNull String> userAgentProvider;
    @NonNull Provider<Integer> portaroUserIdProvider;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull EntityManager entityManager;
    @NonNull QueryFactory queryFactory;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;

    @Bean
    public RemoteFileDataStreamer obalkyknihFileDataStreamer() {
        return new RestRemoteFileDataStreamer(obalkyknihRestTemplate());
    }

    @Bean
    public Failoverer obalkyknihFailoverer() {
        return new ExternalServiceFailoverer(
                OBALKYKNIH_PATTERN,
                List.of(
                        new Failoverer.SourceSpec(ObalkyknihConstants.BASE_URL, Duration.ofHours(1)),
                        new Failoverer.SourceSpec(ObalkyknihConstants.FAILOVER_URL_1, Duration.ofMinutes(1)),
                        new Failoverer.SourceSpec(ObalkyknihConstants.FAILOVER_URL_2, Duration.ofMinutes(1))
                ));
    }

    @Bean
    public Provider<@NonNull Boolean> obalkyknihUpStatusProvider() {
        Failoverer failoverer = obalkyknihFailoverer();
        return Provider.of(failoverer::isReady);
    }

    @Bean
    public RestTemplate obalkyknihRestTemplate() {
        var headers = new ObalkyknihHttpHeaders(
                userAgentProvider,
                new StaticContextualDelegatingProvider<>(nullableSiglaProvider, rootDepartmentProvider),
                rootServerUrlProvider);

        var fastTimeoutConnectionManager = httpFactory.newDefaultConnectionManager();
        fastTimeoutConnectionManager.setDefaultConnectionConfig(ConnectionConfig.custom()
                .setConnectTimeout(Timeout.ofSeconds(4))
                .setSocketTimeout(Timeout.ofSeconds(10))
                .build());
        fastTimeoutConnectionManager.setDefaultSocketConfig(SocketConfig.custom()
                .setSoTimeout(Timeout.ofSeconds(10))
                .build());

        HttpClient obalkyknihHttpClient = httpFactory.httpClientBuilder(fastTimeoutConnectionManager)
                .setDefaultHeaders(headers.get())
                .build();
        return new RestOperationsFactory().rest(obalkyknihHttpClient, traceIdRepository);
    }

    @Bean
    public ExternalDownloader obalkyknihRestDownloader() {
        return new FailoveredExternalDownloader(
                new RestExternalDownloader(
                        obalkyknihRestTemplate()),
                obalkyknihFailoverer());
    }

    @Bean
    public CoverDownloader obalkyknihCoverDownloader() {
        return new FailoveredCoverDownloader(
                new ImageDownloader(
                        obalkyknihFileDataStreamer()),
                obalkyknihFailoverer(),
                coverFactory());
    }

    @Bean
    public NamedCoverLoader obalkyknihCoverLoader() {
        return new ObalkyKnihApiCoverLoader(
                obalkyknihCoverDownloader(),
                new ObalkyknihRequestParamFactory(allSiglasProvider));
    }

    @Bean
    public CoverFactory coverFactory() {
        return new CoverFactory(portaroUserIdProvider, new FileExtensionDetector(), fileCategoryBySystemTypeLoader);
    }

    @Bean
    public ObalkyknihDocumentMetadataLoader obalkyknihDocumentMetadataLoader() {
        return new ObalkyknihDocumentMetadataLoader(
                obalkyknihRestDownloader(),
                new ObalkyknihRequestParamFactory(allSiglasProvider));
    }

    @Bean
    public ObalkyknihAuthorityMetadataLoader obalkyknihAuthorityMetadataLoader() {
        return new ObalkyknihAuthorityMetadataLoader(obalkyknihRestDownloader());
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Obalkyknih> obalkyknihSearchLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new SpringDbObalkyknihSearchLoader(jdbcTemplate, queryFactory)
        );
    }

    @Bean
    public SimpleJpaRepository<ObalkyknihEntity, UUID> obalkyknihEntityRepository() {
        return new SimpleJpaRepository<>(ObalkyknihEntity.class, entityManager);
    }

    @Bean
    public Saver<Obalkyknih, ObalkyknihEntity> obalkyknihSaver() {
        return new PreConvertingSaver<>(new ObalkyknihToEntityConverter(),
                new FlushingJpaSaver<>(obalkyknihEntityRepository()));
    }

    @Bean
    public ByIdDeleter<UUID> obalkyknihByIdDeleter() {
        return new PreConvertingByIdDeleter<>(new EToEConverter<>(),
                new FlushingJpaByIdDeleter<>(obalkyknihEntityRepository())
        );
    }

}
