package cz.kpsys.portaro.search.z;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

@Tag("ci")
@Tag("unit")
public class ZServerQueryBuilderTest {

    private void buildAndCheck(MapBackedParams p, String expected) {
        String query = new ZServerQueryBuilder().build(p);
        System.out.println(query);
        System.out.println(expected);
        Assertions.assertEquals(StringUtil.trimXml(expected), StringUtil.trimXml(query));
    }

    @Test
    public void shouldGenerateQueryWithSingleField() {
        MapBackedParams params = MapBackedParams.build(p -> {
            p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
            p.set(CoreSearchParams.NAME, "Babička");
            p.set(RecordConstants.SearchParams.FOND, List.of(Fond.testingMonography()));
        });
        buildAndCheck(params, """
                <constraintModel>
                  <constraint>
                    <semantic>4</semantic>
                    <relation>3</relation>
                    <position>3</position>
                    <structure>1</structure>
                    <truncation>100</truncation>
                    <completion>1</completion>
                  </constraint>
                  <model>Babička</model>
                </constraintModel>""");
    }
    
    @Test
    public void shouldGenerateConjunctionOfTrimmedIsbnAndAuthorWhenIsbnOrIssnIsOsbn() {
        MapBackedParams params = MapBackedParams.build(p -> {
            p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
            p.set(RecordConstants.SearchParams.FOND, List.of(Fond.testingMonography()));
            p.set(RecordConstants.SearchParams.ISBN_OR_ISSN, "978-80-87108-14-7");
            p.set(RecordConstants.SearchParams.AUTHOR, "Božena Němcová");
        });
        buildAndCheck(params, """
                <and>
                  <constraintModel>
                    <constraint>
                      <semantic>7</semantic>
                      <relation>3</relation>
                      <position>3</position>
                      <structure>1</structure>
                      <truncation>100</truncation>
                      <completion>1</completion>
                    </constraint>
                    <model>9788087108147</model>
                  </constraintModel>
                  <constraintModel>
                    <constraint>
                    <semantic>1003</semantic>
                      <relation>3</relation>
                      <position>3</position>
                      <structure>1</structure>
                      <truncation>100</truncation>
                      <completion>1</completion>
                    </constraint>
                    <model>Božena Němcová</model>
                  </constraintModel>
                </and>""");
    }

    @Test
    public void shouldGenerateConjunctionOfMultipleFields() {
        MapBackedParams params = MapBackedParams.build(p -> {
            p.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
            p.set(CoreSearchParams.NAME, "Babička");
            p.set(RecordConstants.SearchParams.FOND, List.of(Fond.testingMonography()));
            p.set(RecordConstants.SearchParams.AUTHOR, "Božena Němcová");
            p.set(RecordConstants.SearchParams.PUBLICATION_YEAR, 2017);
        });
        buildAndCheck(params, """
                <and>
                  <constraintModel>
                    <constraint>
                     <semantic>4</semantic>
                     <relation>3</relation>
                     <position>3</position>
                     <structure>1</structure>
                     <truncation>100</truncation>
                     <completion>1</completion>
                    </constraint>
                    <model>Babička</model>
                  </constraintModel>
                  <constraintModel>
                    <constraint>
                      <semantic>1003</semantic>
                      <relation>3</relation>
                      <position>3</position>
                      <structure>1</structure>
                      <truncation>100</truncation>
                      <completion>1</completion>
                    </constraint>
                    <model>Božena Němcová</model>
                  </constraintModel>
                  <constraintModel>
                    <constraint>
                      <semantic>31</semantic>
                      <relation>3</relation>
                      <position>3</position>
                      <structure>1</structure>
                      <truncation>100</truncation>
                      <completion>1</completion>
                    </constraint>
                    <model>2017</model>
                  </constraintModel>
                </and>""");
    }
}
