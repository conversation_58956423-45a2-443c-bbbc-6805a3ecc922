package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.sip2.client.Sip2Command;
import cz.kpsys.portaro.sip2.client.model.Sip2RenewAllRequest;
import cz.kpsys.portaro.sip2.client.model.Sip2RenewAllResponse;
import cz.kpsys.portaro.sip2.client.serialize.Sip2RequestSerializerTestParent;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.CommandCharacters.NEWLINE;
import static cz.kpsys.portaro.sip2.client.TestUtils.SIP2_DATE_TIME_INSTANT;

@Tag("ci")
@Tag("unit")
public class Sip2RenewAllRequestSerializerTest extends Sip2RequestSerializerTestParent<Sip2RenewAllRequest, Sip2RenewAllResponse> {

    protected TypedSip2MessageSerializer<Sip2Command<Sip2RenewAllRequest, Sip2RenewAllResponse>> getSerializer() {
        return new NewlineAddingSip2MessageSerializer<>(ErrorDetectionAddingSip2RequestSerializer.withSequenceAndChecksum(Sip2RenewAllRequestSerializer.createDefault()));
    }

    @Test
    public void testRenewAllRequestWithoutErrorDetection() {
        test(
                () -> new Sip2RenewAllRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "terminalPassword", "patronId", "patronPassword"),
                false,
                "6520210814    083455AOinstitutionId|AApatronId|ADpatronPassword|ACterminalPassword|" + NEWLINE
        );
    }

    @Test
    public void testRenewAllRequestWithErrorDetection1() {
        test(
                () -> new Sip2RenewAllRequest(SIP2_DATE_TIME_INSTANT, "patronId"),
                true,
                "6520210814    083455AO|AApatronId|AY0AZF59A" + NEWLINE
        );
    }

    @Test
    public void testRenewAllRequestWithErrorDetection2() {
        test(
                () -> {
                    Sip2RenewAllRequest req = new Sip2RenewAllRequest(Instant.parse("2011-11-11T10:22:33Z"), "institutionId", "patronId");
                    req.setSequence(1);
                    return req;
                },
                true,
                "6520111111    112233AOinstitutionId|AApatronId|AY1AZF039" + NEWLINE
        );
    }

    @Test
    public void testRenewAllRequestWithErrorDetection3() {
        test(
                () -> {
                    Sip2RenewAllRequest req = new Sip2RenewAllRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "patronId", "patronPassword");
                    req.setSequence(3);
                    req.setFeeAcknowledged(Throolean.FALSE);
                    return req;
                },
                true,
                "6520210814    083455AOinstitutionId|AApatronId|ADpatronPassword|BON|AY3AZE7DD" + NEWLINE
        );
    }

    @Test
    public void testRenewAllRequestWithErrorDetection4() {
        test(
                () -> {
                    Sip2RenewAllRequest req = new Sip2RenewAllRequest(SIP2_DATE_TIME_INSTANT, "institutionId", "terminalPassword", "patronId", "patronPassword");
                    req.setFeeAcknowledged(Throolean.TRUE);
                    return req;
                },
                true,
                "6520210814    083455AOinstitutionId|AApatronId|ADpatronPassword|ACterminalPassword|BOY|AY0AZE026" + NEWLINE
        );
    }
}
