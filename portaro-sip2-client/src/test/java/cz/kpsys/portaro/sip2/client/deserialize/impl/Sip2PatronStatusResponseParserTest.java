package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.model.Language;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronStatusResponse;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class Sip2PatronStatusResponseParserTest {

    private final Sip2PatronStatusResponseParser parser = Sip2PatronStatusResponseParser.createDefault();

    @Test
    void shouldParse() {
        Sip2PatronStatusResponse response = parser.parse(RequiredSip2ValueParsing.of("24 Y Y YY  YYY  00120230717    182434AA210123456789|AEtestFirstName testLastName|AFUzivatel testFirstName testLastName|AOKPSYS|BLY|CQY|AY8AZD5BB\r"));

        assertFalse(response.getStatus().isChargePrivilegesDenied());
        assertTrue(response.getStatus().isRenewalPrivilegesDenied());
        assertFalse(response.getStatus().isRecallPrivilegesDenied());
        assertTrue(response.getStatus().isHoldPrivilegesDenied());
        assertFalse(response.getStatus().isCardReportedLost());
        assertTrue(response.getStatus().isTooManyItemsCharged());
        assertTrue(response.getStatus().isTooManyItemsOverdue());
        assertFalse(response.getStatus().isTooManyRenewals());
        assertFalse(response.getStatus().isTooManyClaimsOfItemsReturned());
        assertTrue(response.getStatus().isTooManyItemsLost());
        assertTrue(response.getStatus().isExcessiveOutstandingFines());
        assertTrue(response.getStatus().isExcessiveOutstandingFees());
        assertFalse(response.getStatus().isRecallOverdue());
        assertFalse(response.getStatus().isTooManyItemsBilled());
        assertEquals(Language.ENGLISH, response.getLanguage());
        assertEquals(Instant.parse("2023-07-17T16:24:34Z"), response.getTransactionDate());

        assertEquals("210123456789", response.getPatronIdentifier());
        assertEquals("testFirstName testLastName", response.getPersonalName());
        assertEquals(List.of("Uzivatel testFirstName testLastName"), response.getScreenMessage());
        assertEquals(List.of(), response.getPrintLine());
        assertEquals("KPSYS", response.getInstitutionId());
        assertEquals(Throolean.TRUE, response.getValidPatron());
        assertEquals(Throolean.TRUE, response.getValidPatronPassword());
        assertNull(response.getCurrencyType());
        assertNull(response.getFeeAmount());
        assertEquals(8, response.getSequence());
    }
}