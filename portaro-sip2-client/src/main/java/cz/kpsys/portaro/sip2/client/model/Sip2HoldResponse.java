package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageResponse;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

/**
 * The ILS must send this message in response to the
 * {@link Sip2HoldRequest SIP2HoldRequest} message.
 */
@Getter
@Setter
public class Sip2HoldResponse extends Sip2MessageResponse {

    /**
     * Tells that the requested action was allowable and completed succesfully.
     */
    private boolean ok;

    /**
     * True means that the item is available, it's not checked out or on hold.
     */
    private boolean available;

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * The expiration date of the hold.
     */
    @NullableNotBlank
    private String expirationDate;

    /**
     * A numeric value for the patron's position in the hold queue for an item.
     */
    @NullableNotBlank
    private String queuePosition;

    /**
     * The pickup location of the hold.
     */
    @NullableNotBlank
    private String pickupLocation;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId;

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier;

    /**
     * Identifying value for the item.
     */
    @NullableNotBlank
    private String itemIdentifier;

    /**
     * Identifying value for the title.
     */
    @NullableNotBlank
    private String titleIdentifier;

    /**
     * Bibliographic id of the record.
     * This is a Voyager ESIP extension.
     */
    @NullableNotBlank
    private String bibId;

    /**
     * ISBN of the record that was put on hold.
     * This is a Voyager ESIP extension.
     */
    @NullableNotBlank
    private String isbn;

    /**
     * LCCN number of the record that was put on hold. This is a Voyager 
     * ESIP extension.
     */
    @NullableNotBlank
    private String lccn;

    /**
     * Variable-length field. This field provides a way for the ILS to display messages on the system's screen. They are never required. When used, there can be one or more of those fields, which are then displayed on consecutive lines of the screen.
     */
    private List<String> screenMessage;

    /**
     * Variable-length field. This field provides a way for the ILS to print messages on the system's printer. They are never required. When used, there can be one or more of these fields, which are then pronted on consecutive lines of the printer.
     */
    private List<String> printLine;

    /**
     * Constructs and initializes a new SIP2HoldResponse object
     * containing the given data.
     */
    public Sip2HoldResponse() {
        super(CommandType.HOLD_RESPONSE);
    }

}
