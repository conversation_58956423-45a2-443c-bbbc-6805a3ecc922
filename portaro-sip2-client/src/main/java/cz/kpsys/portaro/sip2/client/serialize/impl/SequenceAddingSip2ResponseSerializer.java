package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.Sip2MessageResponse;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.AY_SEQUENCE;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SequenceAddingSip2ResponseSerializer<RES extends Sip2MessageResponse> implements TypedSip2MessageSerializer<RES> {

    @NonNull TypedSip2MessageSerializer<RES> delegate;

    @Override
    public void serialize(@NonNull RES command, @NonNull CommandSerializing ser) {
        delegate.serialize(command, ser);

        ser.nonDelimitedField(AY_SEQUENCE).when(command.hasSequence()).char1ByInt(command.getSequence());
    }
}
