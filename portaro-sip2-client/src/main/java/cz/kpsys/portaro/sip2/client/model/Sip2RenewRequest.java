package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageRequest;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.time.Instant;

/**
 * This class represents the message that is used to renew an item. The ILS SIP
 * server should respond with the {@link Sip2RenewResponse SIP2RenewResponse}
 * message. Either or both the item identifier and title identifier fields
 * must be present for the message to be useful.
 */
@Getter
@Setter
public class Sip2RenewRequest extends Sip2MessageRequest<Sip2RenewResponse> {

    /**
     * If this value is false then ILS should not allow third party renewals. This allows the library staff to prevent third part renewals from this terminal.
     */
    private boolean thirdPartyAllowed;

    /**
     * Notifies the ILS SIP server that the item was already checked in or out while the SIP server wasn't on-line. When true the SIP server should not block the transaction, because it has already been executed.
     * The system can perform transactions while the SIP server is off-line.
     * These transactions are stored and will be sent to the SIP server when it comes back on-line.
     */
    private boolean noBlock;

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * No block due date items were given during off-line operation.
     */
    @Setter(AccessLevel.NONE)
    @NonNull
    private final Instant nbDueDate;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId = "";

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier = "";

    /**
     * Password (PIN) of the patron. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String patronPassword;

    /**
     * Identifying value for the item.
     */
    @NullableNotBlank
    private String itemIdentifier;

    /**
     * Identifying value for the title.
     */
    @NullableNotBlank
    private String titleIdentifier;

    /**
     * Password for the system to login to the ILS. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String terminalPassword;

    /**
     * Specific item information that can be used for identifying an item, such as item weight, size, security marker, etc.
     */
    @NullableNotBlank
    private String itemProperties;

    /**
     * If false and there's a fee associated with the transaction, the ILS SIP server should tell the system in the response that there's a fee, and refuse to complete the transaction. If the system and the patron then interact and the patron agrees to pay the fee, this field will be set to true on a second request message, indicating to the ILS SIP server that the patron has acknowledged the fee and the transaction should not be refused just because there is a fee associated with it.
     */
    private Throolean feeAcknowledged = Throolean.UNKNOWN;

    private Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate) {
        super(CommandType.RENEW_REQUEST);
        this.transactionDate = transactionDate;
        this.nbDueDate = nbDueDate;
    }

    public Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate, @NonNull String patronId) {
        this(transactionDate, nbDueDate);
        this.patronIdentifier = patronId;
    }

    public Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate, @NonNull String patronId, String itemId) {
        this(transactionDate, nbDueDate);
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
    }

    public Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate, @NonNull String patronId, String itemId, String titleId) {
        this(transactionDate, nbDueDate);
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
        this.titleIdentifier = titleId;
    }

    public Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate, @NonNull String institutionId, @NonNull String patronId, String itemId, String titleId) {
        this(transactionDate, nbDueDate);
        this.institutionId = institutionId;
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
        this.titleIdentifier = titleId;
    }

    public Sip2RenewRequest(@NonNull Instant transactionDate, @NonNull Instant nbDueDate, @NonNull String institutionId, @NonNull String terminalPassword, @NonNull String patronId, String itemId, String titleId) {
        this(transactionDate, nbDueDate);
        this.institutionId = institutionId;
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
        this.titleIdentifier = titleId;
        this.terminalPassword = terminalPassword;
    }
}
