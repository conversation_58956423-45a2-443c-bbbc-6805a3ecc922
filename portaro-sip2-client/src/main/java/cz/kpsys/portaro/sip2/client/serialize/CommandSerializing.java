package cz.kpsys.portaro.sip2.client.serialize;

import cz.kpsys.portaro.commons.object.Throolean;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.function.Consumer;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommandSerializing {

    @NonNull Sip2Serializing sip2 = new Sip2Serializing();

    public void newline() {
        sip2.newline();
    }

    public void delim() {
        sip2.delim();
    }

    public void commandIdentifier(@NonNull String commandNumber) {
        sip2.string(commandNumber);
    }

    public ValueSerializing header() {
        return new ValueSerializing(
                sip2 -> {},
                () -> true,
                sip2 -> {},
                sip2
        );
    }

    public ValueSerializing field(String fieldCode) {
        return new ValueSerializing(
                sip2 -> sip2.fieldCode(fieldCode),
                () -> true,
                Sip2Serializing::delim,
                sip2
        );
    }

    public ValueSerializing nonDelimitedField(String fieldCode) {
        return new ValueSerializing(
                sip2 -> sip2.fieldCode(fieldCode),
                () -> true,
                sip2 -> {},
                sip2
        );
    }

    public String build() {
        return sip2.build();
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class ValueSerializing {

        @NonNull Consumer<Sip2Serializing> prefixBuilder;
        @NonNull Supplier<Boolean> toDo;
        @NonNull Consumer<Sip2Serializing> suffixBuilder;
        @NonNull Sip2Serializing sip2;

        public ValueSerializing when(boolean check) {
            return new ValueSerializing(prefixBuilder, () -> check, suffixBuilder, sip2);
        }

        public ValueSerializing whenNotNull(@Nullable Object obj) {
            return new ValueSerializing(prefixBuilder, () -> obj != null, suffixBuilder, sip2);
        }

        public void custom(Consumer<ValueSerializing> consumer) {
            if (toDo.get()) {
                prefixBuilder.accept(sip2);
                consumer.accept(new ValueSerializing(sip2 -> {}, () -> true, sip2 -> {}, sip2));
                suffixBuilder.accept(sip2);
            }
        }

        private void doIt(Consumer<Sip2Serializing> consumer) {
            if (toDo.get()) {
                prefixBuilder.accept(sip2);
                consumer.accept(sip2);
                suffixBuilder.accept(sip2);
            }
        }

        public ValueSerializing string(String s) {
            doIt(val -> val.string(s));
            return this;
        }

        public void char1ByInt(@Min(0) @Max(9) Integer number) {
            doIt(val -> val.char1ByInt(number));
        }

        public void char2ByInt(@Min(0) @Max(99) Integer number) {
            doIt(val -> val.char2ByInt(number));
        }

        public void char3ByInt(@Min(0) @Max(999) Integer number) {
            doIt(val -> val.char3ByInt(number));
        }

        public void char4ByInt(@Min(0) @Max(9999) Integer number) {
            doIt(val -> val.char4ByInt(number));
        }

        public void char1ByString(@Size(min = 1, max = 1) String s) {
            doIt(val -> val.char1ByString(s));
        }

        public void char3ByString(@Size(min = 3, max = 3) String s) {
            doIt(val -> val.char3ByString(s));
        }

        public void char1SpaceYByBoolean(boolean bool) {
            doIt(val -> val.char1YEmptyByBoolean(bool));
        }

        public void charNYByBoolean(boolean bool) {
            doIt(val -> val.charYNByBoolean(bool));
        }

        public void char01ByBoolean(boolean bool) {
            doIt(val -> val.char10ByBoolean(bool));
        }

        public void space() {
            doIt(Sip2Serializing::space);
        }

        public void charNYUByThroolean(Throolean value) {
            doIt(val -> val.charYNUByThroolean(value));
        }

        public void charNYOrNoopByThroolean(Throolean value) {
            if (value.isKnown()) {
                doIt(val -> val.charYNByBoolean(value.toBooleanOrThrow()));
            }
        }

        public void date(String date) {
            doIt(val -> val.date(date));
        }

        public void char1(char c) {
            doIt(val -> val.char1(c));
        }

    }

}
