package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.Sip2CheckoutResponse;
import cz.kpsys.portaro.sip2.client.model.Sip2RenewResponse;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2RenewResponseSerializer implements TypedSip2MessageSerializer<Sip2RenewResponse> {

    @NonNull TypedSip2MessageSerializer<Sip2CheckoutResponse> checkoutResponseSerializer;

    @Override
    public void serialize(@NonNull Sip2RenewResponse request, @NonNull CommandSerializing ser) {
        checkoutResponseSerializer.serialize(request, ser);
    }
}
