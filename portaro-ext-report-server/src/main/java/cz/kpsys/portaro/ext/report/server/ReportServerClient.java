package cz.kpsys.portaro.ext.report.server;

import cz.kpsys.portaro.ext.report.server.datatypes.PrintParameterServerResponse;
import cz.kpsys.portaro.ext.report.server.datatypes.ReportTemplateReportServerResponse;
import cz.kpsys.portaro.ext.report.server.datatypes.ServerFolderReportServerResponse;
import lombok.NonNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

import static cz.kpsys.portaro.ext.report.server.ReportServerParameterVocabulary.DEPARTMENT_PARAMETER_NAME;
import static cz.kpsys.portaro.ext.report.server.ReportServerParameterVocabulary.LOCALE_PARAMETER_NAME;

@FeignClient(name = "reportServer", url = "https://thisWillBeReplacedWithInterceptor")
public interface ReportServerClient {

    @GetMapping(path = "/templates")
    List<ReportTemplateReportServerResponse> getTemplates();

    @GetMapping(path = "/management/templatefolders")
    ServerFolderReportServerResponse getFolders();

    @GetMapping(path = "/report/{id}/parameters")
    List<PrintParameterServerResponse> getTemplateParameters(@PathVariable String id);

    @GetMapping(path = "/report/{id}/export")
    ResponseEntity<@NonNull Resource> getExportReportTemplate(@PathVariable String id, @RequestParam("$" + DEPARTMENT_PARAMETER_NAME) int departmentId, @RequestParam("$" + LOCALE_PARAMETER_NAME) String locale);

    @GetMapping(path = "/report/{id}/export")
    ResponseEntity<@NonNull Resource> getExportReportTemplateWithParameters(@PathVariable String id, @RequestParam("$" + DEPARTMENT_PARAMETER_NAME) int departmentId, @RequestParam("$" + LOCALE_PARAMETER_NAME) String locale, @RequestParam Map<String, Object> props);

}
