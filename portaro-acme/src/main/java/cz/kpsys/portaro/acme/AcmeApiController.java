package cz.kpsys.portaro.acme;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AcmeApiController extends GenericApiController {

    @NonNull AcmeService acmeService;
    @NonNull CertificateValidityChecker automaticRenewingCertificateValidityChecker;

    @RequestMapping("/api/acme/renew")
    public ActionResponse renew() {
        acmeService.renew();
        return FinishedActionResponse.ok();
    }

    @RequestMapping("/api/acme/renew-expiring")
    public ActionResponse checkAndRenew() {
        boolean renewed = automaticRenewingCertificateValidityChecker.check();
        return new FinishedActionResponse(Texts.ofNative("Certificate was " + (renewed ? "" : "not ") + "renewed"));
    }
    
}
