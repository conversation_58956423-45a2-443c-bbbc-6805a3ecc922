package cz.kpsys.portaro.acme;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AcmeHttpChallengeController extends GenericApiController {

    public static final String ACME_CHALLENGE_URL_PREFIX = "/.well-known/acme-challenge/";

    @NonNull ContextualProvider<Department, String> acmeKeyProvider;

    @RequestMapping(value = ACME_CHALLENGE_URL_PREFIX + "{keyPart1}", produces = "text/plain")
    public String getAcmeChallengeKey(@PathVariable("keyPart1") String keyPart1,
                                      @CurrentDepartment Department currentDepartment) {
        String settedKey = Objects.requireNonNull(acmeKeyProvider.getOn(currentDepartment), "Acme challenge key is not set");

        String[] split = settedKey.split("[.]");
        Assert.state(split.length == 2, "Acme challenge key must have 2 dot-separated parts, but is " + settedKey);

        String checkedKeyPart1 = split[0];
        Assert.state(keyPart1.equals(checkedKeyPart1), "Acme challenge endpoint part key does not match with given path variable");

        return settedKey;
    }
    
}
