package cz.kpsys.portaro.auth.bankid;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.security.SecurityManager;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/api/bankid")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class BankIDApiController {

    @NonNull SecurityManager securityManager;
    @NonNull BankIDUserLinkDeleter bankIDUserLinkDeleter;

    @PostMapping("/delete-link")
    public ActionResponse bankIDLinkDeletion(@Valid @RequestBody BankIDLinkDeletionRequest request,
                                             @CurrentDepartment Department ctx,
                                             UserAuthentication currentAuth) {
        securityManager.throwIfCannot(BankIDAuthSecurityActions.USER_BANKID_LINK_DELETE, currentAuth, ctx, request.user());
        bankIDUserLinkDeleter.deleteLink(request.user(), ctx, currentAuth);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }

}
