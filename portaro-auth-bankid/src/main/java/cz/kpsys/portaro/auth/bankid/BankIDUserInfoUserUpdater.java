package cz.kpsys.portaro.auth.bankid;

import cz.kpsys.portaro.auth.AuthPairingProvider;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.edit.command.PersonEditationCommand;
import cz.kpsys.portaro.user.merge.UserMerger;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BankIDUserInfoUserUpdater {

    @NonNull Provider<@NonNull User> bankIDUserProvider;
    @NonNull BankIDUserInfoDataPopulator bankIDUserInfoDataPopulator;
    @NonNull Map<AuthPairingProvider, UserMerger<Department>> userMergerByAuthPairingProviderMap;

    public User update(Department ctx, User user, BankIDUserInfoResult userInfoResult) {
        UserAuthentication bankIDCurrentAuth = CurrentAuth.createWithAbsoluteAuthenticity(bankIDUserProvider.get());
        PersonEditationCommand populate = bankIDUserInfoDataPopulator.populate(ctx, userInfoResult);

        return userMergerByAuthPairingProviderMap.get(AuthPairingProvider.BANKID).merge(ctx, bankIDCurrentAuth, populate, user);
    }
}
