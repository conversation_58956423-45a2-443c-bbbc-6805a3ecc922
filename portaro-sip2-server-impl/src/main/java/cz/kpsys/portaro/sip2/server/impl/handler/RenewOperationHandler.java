package cz.kpsys.portaro.sip2.server.impl.handler;

import cz.kpsys.portaro.appserver.AppserverInformativeResponseException;
import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.renewal.RenewalCommand;
import cz.kpsys.portaro.loan.renewal.RenewalResult;
import cz.kpsys.portaro.loan.renewal.RenewalService;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.sip2.server.adapter.Sip2RenewServerRequest;
import cz.kpsys.portaro.sip2.server.adapter.Sip2RenewServerResponse;
import cz.kpsys.portaro.sip2.server.impl.*;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RenewOperationHandler implements DepartmentedSip2Handler<Sip2RenewServerRequest, Sip2RenewServerResponse> {

    @NonNull ContextualProvider<Department, @NonNull String> institutionIdProvider;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull Sip2UserLoader sip2UserLoader;
    @NonNull Sip2ItemLoader sip2ItemLoader;
    @NonNull SecurityManager securityManager;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Loan> loanSearchLoader;
    @NonNull RenewalService renewalService;
    @NonNull Sip2DocumentPrinter sip2DocumentPrinter;


    @Override
    public void handle(@NonNull Sip2RenewServerRequest request, @NonNull Sip2RenewServerResponse response, @NonNull Department ctx, @NonNull Department terminalDepartment, @NonNull UserAuthentication currentAuth) {
        response.setOk(false);
        response.setInstitutionId(institutionIdProvider.getOn(ctx));
        response.setPatronIdentifier(request.getPatronIdentifier());
        response.setItemIdentifier(request.getItemIdentifier());
        response.setDesensitize(false);
        response.setRenewalOk(false);
        response.setMagneticMedia(null);

        // GET LENDER
        BasicUser lender = sip2UserLoader.getUser(request.getPatronIdentifier(), ctx);

        securityManager.throwIfCannot(Sip2ServerSecurityActions.SIP2_LOAN_RENEW, currentAuth, ctx, lender);

        // GET EXEMPLAR
        Sip2Item item = sip2ItemLoader.getItem(request.getItemIdentifier());
        response.setTitleIdentifier(sip2DocumentPrinter.getTitleIdentifier(item));

        // GET LOAN
        Loan loan = getLoan(item, lender);

        // RENEW
        RenewalResult renewalResult = renewSingle(loan, terminalDepartment, ctx, currentAuth);

        response.setDueDate(renewalResult.returnDate());
        response.setPrintLine("%s %s".formatted(
                sip2DocumentPrinter.getDocumentAndExemplarPrintLine(item.document(), request.getItemIdentifier()),
                localizer.localize(Texts.ofMessageCodedWithLocalizedArgs("loan.ProdlouzenoDoXYteProdlouzeni", Texts.ofDateWithoutTime(renewalResult.returnDate()), Texts.ofNumber(renewalResult.renewalsCount())), ctx)));
        response.setOk(true);
        response.setDesensitize(true);
        response.setRenewalOk(true);
        response.setScreenMessage(localizer.localize(Texts.ofMessageCoded("SIP2.ProdlouzenoOK"), ctx));
    }


    private Loan getLoan(@NonNull Sip2Item item, BasicUser lender) throws Sip2HandleException {
        if (item instanceof LoanBasedSip2Item loanBasedSip2Item) {
            return loanBasedSip2Item.loan();
        }
        if (item instanceof ExemplarBasedSip2Item exemplarBasedSip2Item) {
            Optional<Loan> loan = loanSearchLoader.getMaxOne(StaticParamsModifier.of(
                    LoanConstants.SearchParams.EXEMPLAR, List.of(exemplarBasedSip2Item.exemplar().getId()),
                    LoanConstants.SearchParams.LENDER, List.of(lender),
                    LoanConstants.SearchParams.LOAN_STATE, LoanState.renewable()
            ));
            return loan.orElseThrow(() -> new Sip2HandleException(Texts.ofNative("Vypujcka neexistuje"), "No loan found by exemplar %s and lender %s".formatted(exemplarBasedSip2Item.exemplar(), lender)));
        }
        throw new IllegalStateException("Unknown Sip2Item type %s".formatted(item.getClass().getSimpleName()));
    }


    private RenewalResult renewSingle(@NonNull Loan loan, @NonNull Department renewingDepartment, @NonNull Department ctx, @NonNull BasicUserAuthentication currentAuth) throws Sip2HandleException {
        try {
            return renewalService.renew(new RenewalCommand(loan, renewingDepartment, ctx, currentAuth));
        } catch (AppserverInformativeResponseException e) {
            throw new Sip2HandleException(e.getText(), e);
        }
    }

}
