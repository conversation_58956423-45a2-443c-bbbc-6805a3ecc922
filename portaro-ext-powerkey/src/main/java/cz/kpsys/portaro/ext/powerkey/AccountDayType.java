package cz.kpsys.portaro.ext.powerkey;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Getter
public enum AccountDayType implements Identified<String> {

    WORK_DAY("P", "Pracovní den"),
    SATURDAY("S", "Sobota"),
    SUNDAY("N", "Ned<PERSON><PERSON>"),
    HOLIDAY("V", "Svátek"),
    WHENEVER("C", "Cokoliv");

    public static final Codebook<AccountDayType, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull String desc;

}
