package cz.kpsys.portaro.grid;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.RecordEditationPhaseTransitionResponse;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import lombok.NonNull;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public record RecordRow(

        @NonNull UUID id,
        @NullableNotBlank String name,
        @NonNull SimpleFondResponse fond,
        @NonNull Boolean draft,
        @NonNull Boolean locked,
        @NonNull List<RecordEditationPhaseTransitionResponse> editationPhaseTransitions,
        @NonNull Map<String, List<GridField>> fields

) implements IdentifiedRecord<UUID>, Fieldable {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, RecordRow.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}
