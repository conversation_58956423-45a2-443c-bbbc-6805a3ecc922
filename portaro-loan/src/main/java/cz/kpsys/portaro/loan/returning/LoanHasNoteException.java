package cz.kpsys.portaro.loan.returning;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;
import static cz.kpsys.portaro.loan.returning.LoanReturnRequest.Fields.ignoreLoanHasNote;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LoanHasNoteException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    String note;

    public LoanHasNoteException(String message, String note) {
        super(message);
        this.note = note;
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

    public String getNote() {
        return this.note;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(
                "note", note,
                CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofArgumentedMessageCoded("loan.PoznamkaKVraceniX", note), ignoreLoanHasNote));
    }
}
