package cz.kpsys.portaro.loan.availability.timeslot;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.availability.DocumentAvailabilityRequest;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

import java.time.LocalDate;
import java.time.ZoneId;

public record DocumentSlotAvailabilityRequest(

        @NonNull
        Record document,

        @NonNull
        BasicUser user,

        @NonNull
        Department requestDepartment,

        @NonNull
        DateRange period
) {

    public static DocumentSlotAvailabilityRequest of(@NonNull DocumentAvailabilityRequest documentAvailabilityRequest, @NonNull DateRange period) {
        return new DocumentSlotAvailabilityRequest(documentAvailabilityRequest.document(), documentAvailabilityRequest.requester(), documentAvailabilityRequest.requestDepartment(), period);
    }

    public static DocumentSlotAvailabilityRequest forDay(@NonNull DocumentAvailabilityRequest documentAvailabilityRequest, @NonNull LocalDate date, @NonNull ZoneId timeZoneId) {
        DateRange period = DateRange.ofDay(date, timeZoneId);
        return of(documentAvailabilityRequest, period);
    }
}
