package cz.kpsys.portaro.loan.availability.capability;

/**
 * Představuje možnost výpůjčky daného dokumentu daným uživatelem v dané chvíli za daných podmínek.
 * Např. zda může uživatel daný dokument objednat přes standardní objednávku (mělo by se kontrolovat, zda
 * už není náhodou dokument objednaný, v tom případě by ob<PERSON><PERSON><PERSON><PERSON> znovu nebylo umožněno).
 */
public interface Capability {

    static boolean isForPickupOrOrderOrReserve(Capability c) {
        return c instanceof Pickupability ||
               c instanceof StandardOrderability ||
               c instanceof StandardReservability;
    }

    static boolean isForVisitation(Capability c) {
        return c instanceof ForVisitation;
    }

    String getName();

    boolean isLoginRequired();

}
