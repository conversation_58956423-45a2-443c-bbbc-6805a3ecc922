package cz.kpsys.portaro.loan.availability.capability;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PalmknihyPdfLoanability extends AbstractCapability implements ExternalLoanLoanability {

    public static final String NAME = "palmknihyPdfLoanability";

    @NonNull
    Integer externalLoanRecordId;

    @NonNull
    String url;

    public PalmknihyPdfLoanability(@NonNull Integer externalLoanRecordId, @NonNull String url) {
        super(NAME, true);
        this.externalLoanRecordId = externalLoanRecordId;
        this.url = url;
    }

    public Text getText() {
        return Texts.ofMessageCoded("loan.palmknihy.Nazev");
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PalmknihyPdfLoanability that)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        if (!Objects.equals(externalLoanRecordId, that.externalLoanRecordId)) {
            return false;
        }
        return Objects.equals(url, that.url);
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + externalLoanRecordId;
        result = 31 * result + url.hashCode();
        return result;
    }
}
