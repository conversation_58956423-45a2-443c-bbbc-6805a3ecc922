package cz.kpsys.portaro.loan.lending.exceptions;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;
import static cz.kpsys.portaro.loan.lending.webapi.LendingRequest.Fields.ignoreSpecialInformationOnLocation;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LendingSpecialInformationOnLocationException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    String location;

    public LendingSpecialInformationOnLocationException(String message, String location) {
        super(message);
        this.location = location;
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(
                "location", location,
                CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofArgumentedMessageCoded("loan.OpravduVypujcitZvlastniInfoKLokaci", location), ignoreSpecialInformationOnLocation));
    }
}
