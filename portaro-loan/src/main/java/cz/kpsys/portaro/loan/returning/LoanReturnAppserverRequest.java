package cz.kpsys.portaro.loan.returning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.Value;
import lombok.experimental.NonFinal;

@JacksonXmlRootElement(localName = "vrac")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Value
@NonFinal
public class LoanReturnAppserverRequest {

    @JacksonXmlProperty(localName = "ID_VYPUC")
    @NonNull
    Long loanRealizationId;

    @JacksonXmlProperty(localName = "ID_CTEN")
    @NonNull
    Long userId;

    @JacksonXmlProperty(localName = "PUJCOVNA")
    @NonNull
    Long departmentId;

    @JacksonXmlProperty(localName = "HlasitINILok")
    @NonNull
    Boolean checkSpecialInformationOnLocation;

    @JacksonXmlProperty(localName = "HlasitCiziBudovy")
    @NonNull
    Boolean checkReturnOnDifferentDepartment;

    @JacksonXmlProperty(localName = "HlasitCiziLokace")
    @NonNull
    Boolean checkDocumentIsFromDifferentLocation;

    @JacksonXmlProperty(localName = "HlasitINIKateg")
    @NonNull
    Boolean checkSpecialInformationOnCategory;

    @JacksonXmlProperty(localName = "HlasitPrekrocenyPocetKusu")
    @NonNull
    Boolean checkReturningMoreItemsThenIsRented;

    @JacksonXmlProperty(localName = "HlasitMVS")
    @NonNull
    Boolean checkReturningMVS;

    @JacksonXmlProperty(localName = "HlasitZpetKat")
    @NonNull
    Boolean checkDocumentHasBackwardCataloging;

    @JacksonXmlProperty(localName = "HlasitINIStatus")
    @NonNull
    Boolean checkSpecialInformationOnStatus;

    @JacksonXmlProperty(localName = "HlasitPrilohy")
    @NonNull
    Boolean checkDocumentHasAttachment;

    @JacksonXmlProperty(localName = "HlasitINIStavZnak")
    @NonNull
    Boolean checkSpecialInformationOnHelperField;

    @JacksonXmlProperty(localName = "HlasitPoznamka")
    @NonNull
    Boolean checkLoanHasNote;

    @JacksonXmlProperty(localName = "DotazPokuta")
    @NonNull
    Boolean checkPenaltyGeneration;
}
