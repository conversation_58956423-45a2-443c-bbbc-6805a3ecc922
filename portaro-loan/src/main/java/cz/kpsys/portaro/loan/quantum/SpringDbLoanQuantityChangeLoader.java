package cz.kpsys.portaro.loan.quantum;

import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.databasestructure.LoanDb.VYPUC_TITPER;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.LoanDb.VYPUC.FK_VYPUC;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbLoanQuantityChangeLoader implements LoanQuantityChangeLoader, RowMapper<LoanQuantityChange> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<LoanQuantityChange> getAll(int loanRealizationId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(VYPUC_TITPER.TABLE);
        sq.where().eq(TC(VYPUC_TITPER.TABLE, FK_VYPUC), loanRealizationId);
        sq.orderBy().addAsc(VYPUC_TITPER.DATUM);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public LoanQuantityChange mapRow(ResultSet rs, int rowNum) throws SQLException {
        int value = rs.getInt(VYPUC_TITPER.ZMENA);
        Instant date = DbUtils.instantNotNull(rs, VYPUC_TITPER.DATUM);
        return new LoanQuantityChange(value, date);
    }
}
