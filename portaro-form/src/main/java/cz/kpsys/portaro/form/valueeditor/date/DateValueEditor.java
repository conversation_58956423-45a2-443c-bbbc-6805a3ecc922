package cz.kpsys.portaro.form.valueeditor.date;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.form.valueeditor.LocalizationsAwareValueEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.date.DateValueEditorGranularity;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import lombok.val;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Optional;

@With
@Value
public class DateValueEditor implements LocalizationsAwareValueEditor<DateValueEditorOptions, DateValueEditorValidations, DateValueEditor> {

    @NonNull
    ValueEditorType type;
    String editorId;
    String editorName;
    Text placeholder;
    Boolean disabled;
    @JsonProperty("isVisible")
    Boolean visible;
    DateValueEditorOptions options;
    DateValueEditorValidations validations;

    Map<String, Text> localizations;

    public static DateValueEditor getEmptyEditor() {
        return new DateValueEditor(BasicValueEditorType.DATE, null, null, null, null, null, null, null, null);
    }

    public Optional<String> getEditorId() {
        return Optional.ofNullable(editorId);
    }

    public Optional<String> getEditorName() {
        return Optional.ofNullable(editorName);
    }

    public Optional<Text> getPlaceholder() {
        return Optional.ofNullable(placeholder);
    }

    @Override
    public Optional<Boolean> getDisabled() {
        return Optional.ofNullable(disabled);
    }

    @Override
    public Optional<Boolean> getVisible() {
        return Optional.ofNullable(visible);
    }

    public Optional<DateValueEditorOptions> getOptions() {
        return Optional.ofNullable(options);
    }

    public Optional<DateValueEditorValidations> getValidations() {
        return Optional.ofNullable(validations);
    }

    public Optional<Map<String, Text>> getLocalizations() {
        return Optional.ofNullable(localizations);
    }

    public DateValueEditor addLocalization(String messageKey, Text localization) {
        return LocalizationsAwareValueEditor.addLocalization(this, messageKey, localization);
    }

    /* --- */

    @Override
    public DateValueEditor withRequired(boolean required) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(DateValueEditorValidations::getEmptyValidations)
                        .withRequired(required)
        );
    }

    public DateValueEditor withViewFormat(String viewFormat) {
        return this.withOptions(
                this.getOptions()
                        .orElseGet(DateValueEditorOptions::getEmptyOptions)
                        .withViewFormat(viewFormat)
        );
    }

    public DateValueEditor withOnlyDate(Boolean onlyDate) {
        return this.withOptions(
                this.getOptions()
                        .orElseGet(DateValueEditorOptions::getEmptyOptions)
                        .withOnlyDate(onlyDate)
        );
    }

    public DateValueEditor withMaximumGranularity(DateValueEditorGranularity granularity) {
        return this.withOptions(
                this.getOptions()
                        .orElseGet(DateValueEditorOptions::getEmptyOptions)
                        .withMaximumGranularity(granularity)
        );
    }

    public DateValueEditor withFutureValidation(Provider<ZoneId> timeZoneProvider) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(DateValueEditorValidations::getEmptyValidations)
                        .withMinDateProvider(() -> futureValidationDate(timeZoneProvider))
        );
    }

    public DateValueEditor withPastValidation() {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(DateValueEditorValidations::getEmptyValidations)
                        .withMaxDateProvider(Instant::now)
        );
    }

    public DateValueEditor withMinDateValidation(Provider<Instant> minDateProvider) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(DateValueEditorValidations::getEmptyValidations)
                        .withMinDateProvider(minDateProvider)
        );
    }

    public DateValueEditor withMaxDateValidation(Provider<Instant> maxDateProvider) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(DateValueEditorValidations::getEmptyValidations)
                        .withMaxDateProvider(maxDateProvider)
        );
    }

    private Instant futureValidationDate(Provider<ZoneId> timeZoneProvider) {
        val granularity = Optional.ofNullable(this.options)
                .flatMap(DateValueEditorOptions::getMaximumGranularity)
                .orElse(DateValueEditorGranularity.DATE_ONLY);

        var dateTime = ZonedDateTime.now(timeZoneProvider.get());

        dateTime = dateTime.truncatedTo(ChronoUnit.MINUTES);

        if (granularity == DateValueEditorGranularity.DATE_ONLY) {
            dateTime = dateTime.truncatedTo(ChronoUnit.DAYS);
        }

        return dateTime.toInstant();
    }
}
