package cz.kpsys.portaro.form.valueeditor.multipleacceptable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.StaticAllValuesProvider;
import cz.kpsys.portaro.form.valueeditor.DefaultValueEditorValidations;
import cz.kpsys.portaro.form.valueeditor.LocalizationsAwareValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorType;
import lombok.NonNull;
import lombok.Value;
import lombok.With;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

@With
@Value
public class MultipleAcceptableValueEditor<MODEL> implements LocalizationsAwareValueEditor<MultipleValueEditorOptions<MODEL>, ValueEditorValidations, MultipleAcceptableValueEditor<MODEL>> {

    public static <MODEL> MultipleAcceptableValueEditor<MODEL> getEmptyEditor(Collection<MODEL> acceptableValues) {
        return new MultipleAcceptableValueEditor<>(BasicValueEditorType.MULTIPLE_ACCEPTABLE, null, null, null, null, null, MultipleValueEditorOptions.getEmptyOptions(StaticAllValuesProvider.of(new ArrayList<>(acceptableValues))), null, null, true);
    }

    public static <MODEL> MultipleAcceptableValueEditor<MODEL> getEmptyEditor(AllValuesProvider<MODEL> acceptableValuesProvider) {
        return new MultipleAcceptableValueEditor<>(BasicValueEditorType.MULTIPLE_ACCEPTABLE, null, null, null, null, null, MultipleValueEditorOptions.getEmptyOptions(acceptableValuesProvider), null, null, true);
    }

    @NonNull
    ValueEditorType type;

    String editorId;

    String editorName;

    Text placeholder;

    Boolean disabled;

    Boolean visible;

    @NonNull
    MultipleValueEditorOptions<MODEL> options;

    ValueEditorValidations validations;

    Map<String, Text> localizations;

    @JsonIgnore
    boolean visibleIfSingleValue;


    public Optional<String> getEditorId() {
        return Optional.ofNullable(editorId);
    }

    public Optional<String> getEditorName() {
        return Optional.ofNullable(editorName);
    }

    public Optional<Text> getPlaceholder() {
        return Optional.ofNullable(placeholder);
    }

    @Override
    public Optional<Boolean> getDisabled() {
        return Optional.ofNullable(disabled);
    }

    @Override
    public Optional<Boolean> getVisible() {
        if (this.visibleIfSingleValue) {
            return Optional.ofNullable(visible);
        }

        boolean isVisible = options.getAcceptableValues().size() > 1;

        return isVisible ? Optional.empty() : Optional.of(false);
    }

    public Optional<MultipleValueEditorOptions<MODEL>> getOptions() {
        return Optional.of(options);
    }

    public Optional<ValueEditorValidations> getValidations() {
        return Optional.ofNullable(validations);
    }

    public Optional<Map<String, Text>> getLocalizations() {
        return Optional.ofNullable(localizations);
    }

    public MultipleAcceptableValueEditor<MODEL> addLocalization(String messageKey, Text localization) {
        return LocalizationsAwareValueEditor.addLocalization(this, messageKey, localization);
    }

    @Override
    public MultipleAcceptableValueEditor<MODEL> withRequired(boolean required) {
        return this.withValidations(this.getValidations().orElseGet(DefaultValueEditorValidations::getEmptyValidations).withRequired(required));
    }

    public MultipleAcceptableValueEditor<MODEL> withReorderable(Boolean reorderable) {
        return this.withOptions(options.withReorderable(reorderable));
    }

    public MultipleAcceptableValueEditor<MODEL> withForcedCheckboxes() {
        return this.withSwitchToInlineModeThreshold(0);
    }

    public MultipleAcceptableValueEditor<MODEL> withForcedDropdownMenu() {
        return this.withSwitchToInlineModeThreshold(1);
    }

    public MultipleAcceptableValueEditor<MODEL> withSwitchToInlineModeThreshold(Integer threshold) {
        return this.withOptions(options.withSwitchToInlineModeThreshold(threshold));
    }
}
