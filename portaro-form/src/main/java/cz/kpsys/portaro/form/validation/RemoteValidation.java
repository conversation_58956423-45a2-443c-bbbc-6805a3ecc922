package cz.kpsys.portaro.form.validation;

import lombok.NonNull;
import lombok.Value;
import lombok.With;
import lombok.experimental.FieldNameConstants;
import lombok.val;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Value
@FieldNameConstants
public class RemoteValidation {

    public static RemoteValidation getEmptyValidation(String validationPathUrl) {
        return new RemoteValidation(validationPathUrl, false, null);
    }

    @With
    @NonNull
    String validationPathUrl;

    @With
    boolean sendWholeForm;

    @With
    Map<String, Object> staticParams;

    public Optional<Map<String, Object>> getStaticParams() {
        return Optional.ofNullable(staticParams);
    }

    public ValueEditorAsyncValidation toValueEditorAsyncValidation() {
        val additionalParameters = new HashMap<>(this.getStaticParams().orElseGet(HashMap::new));

        additionalParameters.put(Fields.validationPathUrl, this.validationPathUrl);

        return new ValueEditorAsyncValidation(sendWholeForm ? true : null, additionalParameters);
    }

    public RemoteValidation addStaticParam(String name, Object value) {
        val params = this.getStaticParams().orElseGet(HashMap::new);
        params.put(name, value);

        return this.withStaticParams(params);
    }
}
