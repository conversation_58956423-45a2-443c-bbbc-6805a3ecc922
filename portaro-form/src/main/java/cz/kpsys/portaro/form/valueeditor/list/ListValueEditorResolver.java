package cz.kpsys.portaro.form.valueeditor.list;

import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.editedproperty.ValueEditorFromTypeAnnotationsResolver;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import cz.kpsys.portaro.form.validation.ValueEditorAsyncValidation;
import cz.kpsys.portaro.form.valueeditor.*;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.list.ListEditor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AnnotationConfigurationException;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ListValueEditorResolver implements AnnotationsAwareValueEditorResolver {

    @NonNull ValidationsResolver validationsResolver;
    @NonNull ValueEditorFromTypeAnnotationsResolver valueEditorFromTypeAnnotationsResolver;
    @NonNull FormObjectModelValuePrototypeFactory formObjectModelValuePrototypeFactory;
    @NonNull AnnotationValueEditorModifier<Department> annotationValueEditorModifier;

    @Override
    public Optional<ListValueEditor<?, ?>> resolve(Object formObject, String fieldName, Department currentDepartment) {
        return resolveEditor(formObject, fieldName, currentDepartment, false);
    }

    @Override
    public Optional<ListValueEditor<?, ?>> resolveByTypeAnnotation(Object formObject, String fieldName, Department currentDepartment) {
        return resolveEditor(formObject, fieldName, currentDepartment, true);
    }

    private Optional<ListValueEditor<?, ?>> resolveEditor(Object formObject, String fieldName, Department department, boolean searchInTypeAnnotation) {

        if (!AnnotationUtil.hasAnnotationIncludingTypeAnnotations(formObject, fieldName, ListEditor.class, searchInTypeAnnotation)) {
            return Optional.empty();
        }

        Optional<ListEditor> annotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, ListEditor.class, searchInTypeAnnotation);

        Optional<EditorOptions> editorOptions = annotation.map(ListEditor::editorOptions);

        Optional<String> prototypeBeanName = annotation
                .map(ListEditor::prototypeBeanName)
                .filter(StringUtils::isNoneBlank);

        Optional<Boolean> autoCreateNewItemIfRequired = annotation
                .map(ListEditor::autoCreateNewItemIfRequired);

        ValueEditor<?, ?, ?> subValueEditor = valueEditorFromTypeAnnotationsResolver
                .resolveFromTypeAnnotations(formObject, fieldName, department)
                .orElseThrow(() -> new AnnotationConfigurationException("No inner value editor annotation present"));

        Object prototype;

        if (prototypeBeanName.isPresent()) {
            prototype = formObjectModelValuePrototypeFactory.getPrototypeFromBeanName(prototypeBeanName.get(), formObject);
        } else {
            prototype = formObjectModelValuePrototypeFactory.getPrototypeFromType(formObject, fieldName);
        }

        ListValueEditorOptions listValueEditorOptions = ListValueEditorOptions.<ValueEditor, Object>getEmptyOptions()
                .withSubEditor(subValueEditor)
                .withNewItemPrototype(prototype);

        if (autoCreateNewItemIfRequired.isPresent()) {
            listValueEditorOptions = listValueEditorOptions.withAutoCreateNewItemIfRequired(autoCreateNewItemIfRequired.get());
        }

        ListValueEditor listValueEditor = ListValueEditor.getEmptyEditor().withOptions(listValueEditorOptions);

        listValueEditor = ValueEditor.fillValueEditorWithOptions(listValueEditor, editorOptions);

        listValueEditor = processEditorValidations(formObject, fieldName, listValueEditor, searchInTypeAnnotation);

        listValueEditor = ValueEditorLocalizationsHelper.processLocalizations(listValueEditor, formObject, fieldName, searchInTypeAnnotation);

        listValueEditor = ValueEditorAliasHelper.aliasEditor(formObject, fieldName, searchInTypeAnnotation, listValueEditor);

        listValueEditor = annotationValueEditorModifier.modifyEditorIfModifierPresent(formObject, fieldName, searchInTypeAnnotation, listValueEditor, department);

        return Optional.of(listValueEditor);
    }

    private ListValueEditor<?, ?> processEditorValidations(Object formObject, String fieldName, ListValueEditor<?, ?> listValueEditor, boolean searchInTypeAnnotation) {
        Optional<Boolean> required = validationsResolver.getRequired(formObject, fieldName, searchInTypeAnnotation);
        Optional<ValueEditorAsyncValidation> async = validationsResolver.getAsync(formObject, fieldName, searchInTypeAnnotation);
        Optional<Integer> maxCount = validationsResolver.getMaxCount(formObject, fieldName, searchInTypeAnnotation);

        if (required.isPresent() || async.isPresent() || maxCount.isPresent()) {
            listValueEditor = listValueEditor.withValidations(new ListValueEditorValidations(
                    required.orElse(null),
                    async.orElse(null),
                    maxCount.map(StaticProvider::of).orElse(null)
            ));
        }

        return listValueEditor;
    }
}
