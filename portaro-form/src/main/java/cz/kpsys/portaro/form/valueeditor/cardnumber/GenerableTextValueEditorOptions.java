package cz.kpsys.portaro.form.valueeditor.cardnumber;

import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import lombok.Value;
import lombok.With;

import java.util.Map;
import java.util.Optional;

@With
@Value
public class GenerableTextValueEditorOptions implements ValueEditorOptions {

    public static GenerableTextValueEditorOptions getEmptyOptions() {
        return new GenerableTextValueEditorOptions(null);
    }

    Map<String, Object> requestParameters;

    public Optional<Map<String, Object>> getRequestParameters() {
        return Optional.ofNullable(requestParameters);
    }
}
