package cz.kpsys.portaro.form.valueeditor.object;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.form.formfield.FormField;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.Optional;

@With
@Value
public class HintAwareObjectValueEditorField<OPTIONS extends ValueEditorOptions, VALIDATIONS extends ValueEditorValidations, EDITOR extends ValueEditor<OPTIONS, VALIDATIONS, EDITOR>> implements FormField<OPTIONS, VALIDATIONS> {

    @NonNull
    Text label;

    @NonNull
    String fieldName;

    @NonNull
    EDITOR editor;

    @Nullable
    Text hint;

    @Override
    public Text getLabel() {
        return label;
    }

    @Override
    public String getFieldName() {
        return fieldName;
    }

    public Optional<Text> getHint() {
        return Optional.ofNullable(hint);
    }

    @Override
    public EDITOR getEditor() {
        return editor;
    }

}
