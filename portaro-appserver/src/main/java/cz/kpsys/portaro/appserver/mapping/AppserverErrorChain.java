package cz.kpsys.portaro.appserver.mapping;

import org.jdom2.Document;

import java.util.LinkedList;
import java.util.Queue;

/**
 *
 * <AUTHOR>
 * @param <E> Typ navratove hodnoty jednotlivych performeru
 */
public class AppserverError<PERSON>hain<E> {
    
    private final Queue<Performer<E>> performers = new LinkedList<>();
    
    public void addPerformer(Performer<E> p) {
        performers.add(p);
    }
    
    public E next(int errorNumber, String errorMessage, Document xmlDocument) {
        Performer<E> nextPerformer = performers.poll();
        return nextPerformer.perform(errorNumber, errorMessage, xmlDocument);
    }
    
    public interface Performer<E> {
        E perform(int errorNumber, String errorMessage, Document d);
    }
    
}
