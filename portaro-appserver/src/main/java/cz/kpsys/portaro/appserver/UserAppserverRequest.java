package cz.kpsys.portaro.appserver;

import cz.kpsys.portaro.appserver.oxm.AppserverTag;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;
import java.util.regex.Matcher;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserAppserverRequest implements AppserverRequest<String> {

    @NonNull
    XmlFormatter xmlFormatter = new XmlFormatter();

    @NonNull
    AppserverRequest<?> target;

    @Getter
    @NonNull
    Integer userId;

    public UserAppserverRequest(@NonNull AppserverRequest<?> target,
                                @NonNull Integer userId) {
        Assert.isInstanceOf(String.class, target.getBody(), "Other than string request is not supported yet");
        this.target = target;
        this.userId = userId;
    }


    @Override
    public String getBody() {
        String userIdElement = String.valueOf(new AppserverTag("id_uziv", userId));
        String unformattedXml = ((String) target.getBody()).replaceFirst("\\?>\\s*<collection>", Matcher.quoteReplacement("?><collection>" + userIdElement));
        return xmlFormatter.tryConvertToPrettyString(unformattedXml);
    }

    @Override
    public MultiValueMap<String, String> getHeaders() {
        return new LinkedMultiValueMap<>();
    }

    @Override
    public String getPath() {
        return target.getPath();
    }

    @Override
    public HttpMethod getMethod() {
        return target.getMethod();
    }

    @Override
    public Map<String, Object> getUriVariables() {
        return target.getUriVariables();
    }

    @Override
    public Map<String, Resource> getFiles() {
        return target.getFiles();
    }

}
