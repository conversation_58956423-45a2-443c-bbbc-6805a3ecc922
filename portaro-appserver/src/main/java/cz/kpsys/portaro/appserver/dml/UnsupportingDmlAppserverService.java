package cz.kpsys.portaro.appserver.dml;

import cz.kpsys.portaro.appserver.mapping.AppserverResponseHandler;

import java.util.List;

public class UnsupportingDmlAppserverService implements DmlAppserverService {

    @Override
    public <E> E executeStatement(List<TableWrite> tableWrites, AppserverResponseHandler<E> responseMapper) {
        throw new UnsupportedOperationException("This dml appserver service is mock");
    }
}
