package cz.kpsys.portaro.appserver;

import org.jdom2.Document;

public interface AppserverService {

    Document call(AppserverRequest<?> request) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException;

    <T> T call(AppserverRequest<?> request, Class<T> requiredType) throws AppserverAccessException, AppserverResponseTimeoutException, AppserverTransferException, AppserverErrorResponseException, AppserverInformativeResponseException;
}
