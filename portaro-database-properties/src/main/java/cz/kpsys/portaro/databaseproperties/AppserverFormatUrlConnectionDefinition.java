package cz.kpsys.portaro.databaseproperties;

import lombok.NonNull;

import static cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings.DBTYPE_FIREBIRD;

public record AppserverFormatUrlConnectionDefinition(
        @NonNull String url,
        @NonNull String databaseType,
        @NonNull String username,
        @NonNull String password
) {

    public static AppserverFormatUrlConnectionDefinition of(@NonNull String url, @NonNull String databaseType, @NonNull String username, @NonNull String password) {
        return new AppserverFormatUrlConnectionDefinition(url, databaseType, username, password);
    }

    public ParsedConnectionDefinition parse() {
        String[] tokens = url.split("[/:]");

        String host = tokens[0];

        int port;
        if (Character.isDigit(tokens[1].charAt(0))) { //pokud za url je cislo, jedna se o port
            port = Integer.parseInt(tokens[1]);
        } else { //pokud za url neni cislo, port neni uveden a nastavi se defaultni
            port = getDefaultPort();
        }

        String dbFile;
        if (Character.isDigit(tokens[1].charAt(0))) { //pokud za url je cislo, je tam port -> databaseFile je az dalsi
            dbFile = tokens[2];
        } else { //pokud za url neni cislo, port neni uveden a tim padem databaseFile je uz ted
            dbFile = tokens[1];
        }

        return new ParsedConnectionDefinition(databaseType, host, port, dbFile, username, password);
    }

    private int getDefaultPort() {
        return databaseType.equalsIgnoreCase(DBTYPE_FIREBIRD) ? DatabaseProperties.FIREBIRD_DEFAULT_PORT : DatabaseProperties.POSTGRESQL_DEFAULT_PORT;
    }
}
