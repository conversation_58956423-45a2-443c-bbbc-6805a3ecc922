package cz.kpsys.portaro.databaseproperties;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AutodetectedDatabaseConnectionSettingsContextualProvider<CTX> implements ContextualProvider<CTX, DatabaseConnectionSettings> {

    @NonNull ContextualProvider<CTX, @NonNull String> connectionStringProvider;
    @NonNull ContextualProvider<CTX, @NonNull String> usernameProvider;
    @NonNull ContextualProvider<CTX, @NonNull String> passwordProvider;

    @Override
    public DatabaseConnectionSettings getOn(CTX ctx) throws ItemNotFoundException {
        String connectionString = connectionStringProvider.getOn(ctx);
        String username = usernameProvider.getOn(ctx);
        String password = passwordProvider.getOn(ctx);
        return UrlBasedDatabaseProperties.ofAutodetectedType(connectionString, username, password);
    }
}
