package cz.kpsys.portaro.user.sdi;

import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;

public record SdiSendingCreationCommand(
        @NonNull Integer sdiRequestId,
        @NonNull Instant date,
        @Nullable Integer recordsCount,
        @Nullable String error
) {

    public static SdiSendingCreationCommand success(@NonNull Integer sdiRequestId, @NonNull Integer recordsCount) {
        return new SdiSendingCreationCommand(sdiRequestId, Instant.now(), recordsCount, null);
    }

    public static SdiSendingCreationCommand failed(@NonNull Integer sdiRequestId, @Nullable Integer recordsCount, @NonNull String error) {
        return new SdiSendingCreationCommand(sdiRequestId, Instant.now(), recordsCount, error);
    }
}
