package cz.kpsys.portaro.auth.bankid.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public enum NotificationEventType {

    @JsonProperty("claims_updated")
    CLAIMS_UPDATED;

}
