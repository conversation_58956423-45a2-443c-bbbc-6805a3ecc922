dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.apache.commons:commons-lang3:3.+")
    testImplementation(testFixtures(project(":portaro-commons")))

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    api(project(":portaro-auth"))
    api(project(":portaro-commons"))
    implementation(project(":portaro-commons-image"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-export"))
    implementation(project(":portaro-search"))
    api(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.data:spring-data-jpa:3.+")

    implementation("com.drewnoakes:metadata-extractor:+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("javax.annotation:javax.annotation-api:1.+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("org.hibernate.orm:hibernate-core:6.5.+")
}
