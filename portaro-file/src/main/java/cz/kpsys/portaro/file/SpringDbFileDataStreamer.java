package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.io.*;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.HttpRangeNotSatisfiableException;
import cz.kpsys.portaro.sql.generator.BlobHandler;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Min;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.Optional;

import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.*;
import static cz.kpsys.portaro.sql.generator.BlobHandlerBase.BLOB_LIMIT;
import static java.lang.String.format;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@Slf4j
public class SpringDbFileDataStreamer implements FileDataStreamer {

    public enum ThrowingMode {
        WHEN_NULL_DATA, WHEN_EMPTY_DATA, NEVER
    }

    // SQLSTATE 22011 = data exception: substring error
    public static final String SQL_DATA_EXCEPTION_SUBSTRING_ERROR = "22011";
    public static final String BLOBID_BLOB_FS = "BLOB:FS:";
    public static final String BLOBID_BLOB_URL = "BLOB:URL:";

    /**
     * cz.kpsys.kpwin2.services.FilesystemBlobStorageService#zakodujBlobIdAsBlob
     *
     * @param relativeFilePath relativní cesta souboru k blobDiru
     *
     * @return relativní cesta k blobu s prefixem, který z něj dělá blob cestu na souborovém systému pro uložení v DB.
     */
    public static InputStream encodeBlobPath(@org.jspecify.annotations.NonNull String relativeFilePath) {
        return new ByteArrayInputStream(encodeBlobPathAsBytes(relativeFilePath));
    }

    public static byte[] encodeBlobPathAsBytes(@org.jspecify.annotations.NonNull String relativeFilePath) {
        if (StringUtil.isNullOrBlank(relativeFilePath)) {
            throw new IllegalArgumentException("Must supply nonnull, nonempty relative path String");
        }
        return (BLOBID_BLOB_FS + relativeFilePath).getBytes(StandardCharsets.UTF_8);
    }

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull String dataColumn;
    @Nullable SpecificFileDataStreamer filesystemStreamer;
    @Nullable SpecificFileDataStreamer webStreamer;

    @NonFinal ThrowingMode throwingMode = ThrowingMode.WHEN_NULL_DATA;

    public static SpringDbFileDataStreamer ofOriginalData(NamedParameterJdbcOperations jdbcTemplate,
                                                          QueryFactory queryFactory,
                                                          @NonNull SpecificFileDataStreamer filesystemStreamer,
                                                          @NonNull SpecificFileDataStreamer webStreamer) {
        return new SpringDbFileDataStreamer(jdbcTemplate, queryFactory, ORIGINAL, filesystemStreamer, webStreamer);
    }

    public static SpringDbFileDataStreamer ofTextData(NamedParameterJdbcOperations jdbcTemplate,
                                                      QueryFactory queryFactory) {
        return new SpringDbFileDataStreamer(jdbcTemplate, queryFactory, TEXT, null, null);
    }

    public static SpringDbFileDataStreamer ofThumbnailData(NamedParameterJdbcOperations jdbcTemplate,
                                                           QueryFactory queryFactory) {
        return new SpringDbFileDataStreamer(jdbcTemplate, queryFactory, NAHLED, null, null);
    }

    public SpringDbFileDataStreamer throwing(ThrowingMode throwingMode) {
        this.throwingMode = throwingMode;
        return this;
    }

    @Override
    public void streamData(final @NonNull Long id,
                           final @Nullable BytesRange requestedRange,
                           final @NonNull FileStreamConsumer streamConsumer) {
        LoadedData smallData = new LoadedData();

        BlobHandler blobHandler = queryFactory.getDbSpecifics().newBlobHandlerForColumn(dataColumn)
                .withRange(requestedRange);

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(FILENAME).selectBlob(blobHandler);
        sq.from(TABLE);
        sq.where().eq(ID_FULLTEXT, id);

        try {
            jdbcTemplate.query(sq.getSql(), sq.getParamMap(), (ResultSetExtractor<Void>) rs -> {
                rs.next();

                String filename = rs.getString(FILENAME);

                try (InputStream is = blobHandler.getInputStream(rs)) {
                    if (is == null) {
                        handleNullBlob(id, streamConsumer, filename);
                    } else {
                        Long dataSize = blobHandler.getSize(rs);
                        handleBlob(id, streamConsumer, filename, dataSize, requestedRange, is, smallData);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                return null;
            });
        } catch (DataAccessException e) {
            if (e.getCause() instanceof SQLException sqlException) {
                if (SQL_DATA_EXCEPTION_SUBSTRING_ERROR.equals(sqlException.getSQLState())) {
                    throw new HttpRangeNotSatisfiableException(e);
                }
            } else {
                throw e;
            }
        }

        if (smallData.isLoaded) {
            streamRemoteFileData(id, smallData, streamConsumer, requestedRange);
        }
    }

    @SneakyThrows
    private void handleNullBlob(Long id, FileStreamConsumer streamConsumer, String filename) {
        if (throwingMode == ThrowingMode.WHEN_NULL_DATA) {
            throw new ItemNotFoundException("Blob", format("%s.%s of id %s", TABLE, dataColumn, id));
        }
        try (InputStream dataStream = new ByteArrayInputStream(new byte[0])) {
            streamConsumer.consume(
                    StreamInfo.wholeFile(filename, 0L),
                    dataStream);
        }
    }

    @SneakyThrows
    private void handleBlob(Long id,
                            FileStreamConsumer streamConsumer,
                            String filename,
                            @Nullable @Min(1) Long size,
                            @Nullable BytesRange range,
                            InputStream inputStream,
                            LoadedData outLoadedData) {
        if (size == null && throwingMode == ThrowingMode.WHEN_EMPTY_DATA) {
            throw new ItemNotFoundException("byte[]", format("%s.%s of id %s", TABLE, dataColumn, id));
        }

        if (size == null || size > BLOB_LIMIT) {
            // v datech je primo cely soubor
            AbsoluteRangeBytes absRange = BytesRange.toAbsoluteRange(range, size);
            streamConsumer.consume(
                    StreamInfo.detectFile(filename, size, absRange),
                    inputStream);
        } else {
            // v datech může být cesta k souboru (budto na filesystem nebo na web)
            outLoadedData.loadData(filename, inputStream);
        }

    }


    private void streamRemoteFileData(Long id,
                                      LoadedData loadedData,
                                      FileStreamConsumer reader,
                                      @Nullable BytesRange range) {
        final String blobContentText = new String(loadedData.data, StandardCharsets.ISO_8859_1);

        final Optional<String> filesystemLocation = StringUtil.removePrefixOpt(blobContentText, BLOBID_BLOB_FS);
        if (filesystemLocation.isPresent()) { // v blobu je cesta k souboru na FS
            Assert.state(filesystemStreamer != null, "Loaded FS BLOB data but filesystem streamer was not set!");
            filesystemStreamer.streamData(id, filesystemLocation.get(), reader, loadedData.filename, range);
            return;
        }

        final Optional<String> webLocation = StringUtil.removePrefixOpt(blobContentText, BLOBID_BLOB_URL);
        if (webLocation.isPresent()) { // v blobu je url adresa
            Assert.state(webStreamer != null, "Loaded URL BLOB data but web streamer was not set!");
            webStreamer.streamData(id, webLocation.get(), reader, loadedData.filename, range);
            return;
        }

        // v blobu jsou data
        FileStreamConsumer.streamBytes(reader, loadedData.filename, loadedData.data, range);
    }

    private static class LoadedData {
        private boolean isLoaded = false;
        private String filename;
        private byte[] data;

        public void loadData(String filename, InputStream inputStream) {
            this.isLoaded = true;
            this.filename = filename;
            this.data = FileUtils.getBytesOfResource(new InputStreamResource(inputStream));
        }
    }

}
