package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.Instant;

import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.*;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.DATUM_OPRAV;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.DATUM_ZAL;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FILENAME;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FK_FULLTEXT_PRISTUP;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FK_FULLTEXT_SKUPINY;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FK_TYP_FULLTEXT;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FK_UZIV;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.FK_UZIV_OPRAV;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.HEIGHT;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.IS_INDEX_LUCENE;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.PORADI;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.SERVICE;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.TEXT;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.VELIKOST;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.WIDTH;
import static cz.kpsys.portaro.databasestructure.FileDb.FULLTEXT_SOUBORY.ZDROJ;
import static cz.kpsys.portaro.file.FileConstants.FileConstraint.FILENAME_MAX_LENGTH;

@MappedSuperclass
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class BaseIdentifiedFileEntity implements Identified<Integer> {

    @Id
    @Column(name = ID_FULLTEXT)
    @NonNull
    Integer id;

    @Column(name = POPIS)
    @Nullable
    String name;

    @Column(name = EXTENSION)
    @Nullable
    String extension;

    @Column(name = FK_UZIV)
    @NonNull
    Integer creatorUserId;

    @Column(name = DATUM_ZAL)
    @NonNull
    Instant creationDate;

    @Column(name = FK_UZIV_OPRAV)
    @Nullable
    Integer lastModifierUserId;

    @Column(name = DATUM_OPRAV)
    @Nullable
    Instant lastModificationDate;

    @Column(name = FILENAME)
    @Size(max = FILENAME_MAX_LENGTH)
    @NullableNotBlank
    String filename;

    @Column(name = VELIKOST)
    @Nullable
    Long size;

    @Column(name = IS_INDEX_LUCENE)
    @NonNull
    Integer isIndexLucene;

    @Column(name = FK_FULLTEXT_SKUPINY)
    @NonNull
    Integer directoryId;

    @Column(name = FK_FULLTEXT_PRISTUP)
    @Nullable
    Integer accessTypeId;

    @Column(name = FK_TYP_FULLTEXT)
    @NonNull
    Integer categoryId;

    @Column(name = ZDROJ)
    @Nullable
    String source;

    @Column(name = WIDTH)
    @Nullable
    Integer width;

    @Column(name = HEIGHT)
    @Nullable
    Integer height;

    @Column(name = SERVICE)
    @Nullable
    String service;

    @Column(name = PORADI)
    @NonNull
    Integer order;

    @Column(name = TEXT)
    @Nullable
    String text;
}
