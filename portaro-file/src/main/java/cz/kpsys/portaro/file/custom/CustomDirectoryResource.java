package cz.kpsys.portaro.file.custom;

import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.Resource;
import org.springframework.util.StringUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomDirectoryResource extends AbstractResource {

    @NonNull String directoryPath;
    @NonNull ByIdLoadable<CustomFile, String> customFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;

    @Override
    public Resource createRelative(@NonNull String fileName) {
        var filePath = StringUtils.applyRelativePath(directoryPath, fileName);
        return new CustomFileResource(filePath, customFileLoader, fileDataStreamer, Instant.now());
    }

    @NonNull
    @Override
    public String getDescription() {
        return "Custom directory resource [" + this.directoryPath + "]";
    }

    @Override
    public boolean exists() {
        return true;
    }

    @NonNull
    @Override
    public InputStream getInputStream() throws IOException {
        throw new FileNotFoundException("Cannot get input stream of directory " + getDescription());
    }
}
