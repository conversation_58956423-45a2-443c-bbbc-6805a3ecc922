package cz.kpsys.portaro.file;

import cz.kpsys.portaro.commons.file.FileViewForm;
import cz.kpsys.portaro.commons.image.Dimensions;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import cz.kpsys.portaro.file.links.Link;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Created by Jan on 7. 5. 2015.
 */
public interface Resource extends AccessTypeAware {


    boolean hasTextualForm();


    String PROPERTY_NAME = "name";

    String getName();

    void setName(String name);


    String PROPERTY_FILE_GROUP = "directory";

    Directory getDirectory();

    void setDirectory(Directory directory);



    List<FileViewForm> getViewForms();

    default boolean hasForm(FileViewForm form) {
        return getViewForms().stream().anyMatch(fileViewForm -> fileViewForm.is(form));
    }




    String PROPERTY_CATEGORY = "category";

    FileCategory getCategory();

    void setCategory(FileCategory category);




    String PROPERTY_ACCESS_TYPE = "accessType";

    FileAccessType getAccessType();

    void setAccessType(FileAccessType accessType);



    String PROPERTY_FILENAME = "filename";

    void setFilename(String filename);

    String getFilename();


    /**
     * vrati field size, potomci (napr. Cover) mohou vracet primo velikost dat, pokud je maji.
     */
    @Nullable Long getSize();

    void setSize(@Nullable Long size);



    String PROPERTY_SOURCE = "source";

    String getSource();

    void setSource(String source);


    void setCreatorUserId(Integer creatorUserId);

    Integer getCreatorUserId();


    void setLastModifierUserId(Integer lastModifierUserId);

    Integer getLastModifierUserId();


    Instant getCreationDate();

    void setCreationDate(Instant date);


    Instant getLastModificationDate();

    void setLastModificationDate(Instant date);


    Optional<Dimensions> getDimensions();

    void setDimensions(@Nullable Dimensions dimensions);


    @Nullable String getService();

    void setService(@Nullable String serviceString);


    @Nullable Link getLink();

}
