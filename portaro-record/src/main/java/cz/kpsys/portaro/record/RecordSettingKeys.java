package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.UUID;

public class RecordSettingKeys {

    public static final String SECTION_RECORD = "record";
    public static final String SECTION_RECORD_DETAIL = "record.detail";
    public static final String SECTION_RECORD_STATUS = "record.status";
    public static final String SECTION_OPAC_RECORD = "OPAC_RECORD";
    public static final SettingKey<String> NEWS_TEMPLATE = new SettingKey<>(SECTION_OPAC_RECORD, "TemplateRecordCarousel");
    public static final String SECTION_OPAC_SEARCH = "OPAC_SEARCH";
    public static final SettingKey<Integer> NEWS_SEARCH_MAX_AGE = new SettingKey<>(SECTION_OPAC_SEARCH, "DefaultMaxAgeOfNewsSearch");
    public static final String SECTION_DEFVAL = "DEFVAL";
    public static final String SECTION_RECORD_HOLDING = "record.holding";
    public static final String SECTION_RECORD_SEARCH_NEWEST = "record.search.newest";
    public static final String SECTION_RECORD_SEARCH_NEWEST_SLIDER = "record.search.newest.slider";
    public static final String SECTION_KATALOGIZACE = "KATALOGIZACE";

    public static final SettingKey<@NonNull Boolean> PORTARO_DETAIL_LOAD_ENABLED = new SettingKey<>(SECTION_RECORD_DETAIL, "portaroLoad");
    public static final SettingKey<@NonNull Boolean> PORTARO_DETAIL_RECORD_FIELD_LOAD_ENABLED = new SettingKey<>(SECTION_RECORD_DETAIL, "portaroRecordFieldLoad");
    public static final SettingKey<@NonNull Boolean> CYRILLIC_ENABLED = new SettingKey<>(SECTION_RECORD, "cyrillic");
    public static final SettingKey<@NonNull List<Integer>> FORBIDDEN_RECORD_STATUSES = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "ForbiddenRecordStatuses");
    public static final SettingKey<@NonNull List<UUID>> FORBIDDEN_RECORDS = new SettingKey<>(SECTION_OPAC_RECORD, "ForbiddenRecords");
    public static final SettingKey<@NonNull List<Integer>> AUTHORITY_FONDS = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "FondyAutorit");
    public static final SettingKey<@NonNull List<Integer>> DOCUMENT_FONDS = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "FondyDokumentu");
    public static final SettingKey<@NonNull List<Integer>> HOLDING_ASSIGNABLE_FROM_CONTEXTS = new SettingKey<>(SECTION_RECORD_HOLDING, "holdingAssignableFromContexts");

    // NEWS SEARCH
    public static final SettingKey<@NonNull String> NEWEST_SEARCH_VIEW_NAME = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST, "viewName");
    public static final SettingKey<List<Integer>> NEWS_EXEMPLAR_STATUSES = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST, "exemplarStatuses");

    // STATUS
    public static final SettingKey<@Nullable Integer> EDIT_LOCK_THRESHOLD = new SettingKey<>(SECTION_RECORD_STATUS, "editLockThreshold");

    // SLIDER
    public static final SettingKey<@NonNull List<Integer>> NEWS_SLIDER_BUILDINGS = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST_SLIDER, "departments");
    public static final SettingKey<@NonNull Boolean> NEWS_WITH_COVER_PREFERING_ENABLED = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST_SLIDER, "preferWithCover");
    public static final SettingKey<@NonNull Integer> NEWS_SLIDER_PRESEARCHED_COUNT = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST_SLIDER, "presearchedCount");
    public static final SettingKey<@NonNull Integer> NEWS_SLIDER_SHOWED_COUNT = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST_SLIDER, "showedCount");
    public static final SettingKey<@NonNull Boolean> NEWS_INCLUDE_PERIODICAL_FONDS_ENABLED = new SettingKey<>(SECTION_RECORD_SEARCH_NEWEST_SLIDER, "includePeriodicals");

    // KATALOGIZACE
    public static final SettingKey<@NonNull String> RECORD_CODE_LABELING_FORMAT = new SettingKey<>(SECTION_KATALOGIZACE, "KODOZNACENIZAZNAMU");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_CODE_LABELING = new SettingKey<>(SECTION_KATALOGIZACE, "KODOZNACENIKNIHOVNY");
}
