package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordSaveCommand;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.dflt.DefaultFieldValueCommandResolver;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValueCommand;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingUpsertCommand;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordFieldEditor {

    @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader;
    @NonNull RecordSaver recordSaver;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver;
    @NonNull FieldDependencyResolver fieldDependencyResolver;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull RecordEditationFactory recordEditationFactory;

    public Field<?> getOrCreateField(@NonNull RecordEditation recordEditation, @NonNull FieldCreationCommand command) {
        return journalIt(recordEditation, journal -> getOrCreateFieldInternal(recordEditation, command, journal, "", recordEditation.getFond()));
    }

    public void editField(@NonNull RecordEditation recordEditation, @NonNull FieldEditationCommand command) {
        journalIt(recordEditation, journal -> {
            editFieldInternal(recordEditation, command, journal, "", recordEditation.getFond());
            return null;
        });
    }

    public void deleteField(@NonNull RecordEditation recordEditation, @NonNull FieldDeletionCommand command) {
        journalIt(recordEditation, journal -> {
            deleteFieldInternal(recordEditation.getRecord().getDetail(), command, journal, "");
            return null;
        });
    }

    private <RET> RET journalIt(@NonNull RecordEditation recordEditation, Function<Journal, RET> action) {
        Journal journal = Journal.create();

        RET ret = action.apply(journal);

        for (JournalAction journalAction : journal) {
            RecordEditationEvent.Type eventType = switch (journalAction.type()) {
                case FIELD_CREATION -> RecordEditationEvent.Type.FIELD_CREATION;
                case FIELD_EDITATION -> RecordEditationEvent.Type.FIELD_VALUE_EDITATION;
                case FIELD_CLEARING -> RecordEditationEvent.Type.FIELD_VALUE_DELETION;
                case FIELD_DELETION -> RecordEditationEvent.Type.FIELD_REMOVAL;
            };
            recordEditation.notifyChange(RecordEditationEvent.createFieldEvent(eventType, journalAction.recordFieldId.fieldId()));
        }

        return ret;
    }

    private @NonNull Field<?> getOrCreateFieldInternal(@NonNull CreatableFieldContainer container, @NonNull FieldCreationCommand command, @NonNull Journal journal, @NonNull String logPrefix, @NonNull Fond fond) {
        // get or create a parent
        RecordFieldId parentRecordFieldId = command.parentRecordFieldId();
        CreatableFieldContainer parentContainer = container;
        if (parentRecordFieldId != null) {
            List<FieldId> fieldIdTopDownChain = parentRecordFieldId.fieldId().asSegments();
            for (FieldId segmentFieldId : fieldIdTopDownChain) {
                Optional<Field<?>> field = parentContainer.getFirstField(By.fieldId(segmentFieldId));
                if (field.isPresent()) {
                    parentContainer = field.get();
                } else if (command.missingHierarchyCreating()) {
                    parentContainer = parentContainer.createField(EmptyFieldCreation.to(segmentFieldId.getFieldTypeId(), segmentFieldId.getRepetition()));
                } else {
                    throw new IllegalStateException("Editing/deleting field " + segmentFieldId + " does not exist.");
                }
            }
        }

        // get or create a field itself
        if (command.existingReusing()) {
            Predicate<Field<?>> matcher = command.repetition().isPresent()
                    ? By.typeAndRepetition(command.fieldTypeId(), command.repetition().get())
                    : By.typeId(command.fieldTypeId());
            Optional<Field<?>> existingField = parentContainer.getFirstField(matcher);
            if (existingField.isPresent()) {
                return existingField.get();
            }
        }

        log.debug("{}Creating field of type {} to {} ({})", logPrefix, command.fieldTypeId(), parentContainer, command);
        EmptyFieldCreation creationCmd = command.repetition().isPresent()
                ? EmptyFieldCreation.to(command.fieldTypeId(), command.repetition().get())
                : EmptyFieldCreation.toEnd(command.fieldTypeId());
        if (command.customIdSetting()) {
            creationCmd = creationCmd.withId(command.existingCustomId());
        }
        Field<?> field = parentContainer.createField(creationCmd);

        journal.addCreation(field.getRecordFieldId());

        if (command.initialValueCreating()) {
            defaultFieldValueCommandResolver.findOn(field.fieldTypeId(), fond, command.existingInitialValueCtx(), command.existingCurrentAuth())
                    .ifPresent(defaultFieldValueCommand ->
                            editFieldInternal(container, FieldEditationCommand.of(field.getRecordFieldId(), defaultFieldValueCommand), journal, logPrefix, fond)
                    );
        }

        return field;
    }

    private void editFieldInternal(@NonNull CreatableFieldContainer container, @NonNull FieldEditationCommand command, @NonNull Journal journal, @NonNull String logPrefix, @NonNull Fond fond) {
        log.debug("{}Start editing field {} ({})", logPrefix, command.fieldId(), command);

        RecordFieldId originallyEditingFieldId = command.recordFieldId();
        RecordFieldId editingFieldId = command.influencerFieldPreferring()
                ? getSingleInfluencerField(originallyEditingFieldId, journal, logPrefix)
                : originallyEditingFieldId;

        deleteFollowers(container, editingFieldId, journal, logPrefix);

        FieldType fieldType = fieldTypeLoader.getById(editingFieldId.fieldId().getFieldTypeId());
        command = replaceScalarCommandWithRecordCommand(command, fieldType);
        command = saveLinkedRecordAndPotentiallyGetAnotherCommand(command, editingFieldId);

        FieldCreationCommand getOrCreateCmd = FieldCreationCommand.of(editingFieldId)
                .reuseExisting()
                .withMissingHierarchyCreating(command.missingHierarchyCreating());
        Field<?> editingField = getOrCreateFieldInternal(container, getOrCreateCmd, journal, logPrefix, fond);
        log.debug("{}Actually setting value {} to field {}", logPrefix, command.valueCommand(), editingField);
        editingField.setEditedValue(command.valueCommand());
        journal.addEditation(editingField.getRecordFieldId());
    }

    private FieldEditationCommand replaceScalarCommandWithRecordCommand(FieldEditationCommand command, FieldType fieldType) {
        if (fieldType.getLinkRootFond().isEmpty() || !(command.valueCommand() instanceof ScalarFieldValueCommand<?> scalarFieldValueCommand)) {
            return command;
        }

        FieldTypeId entryFieldTypeId = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(fieldType.getLinkRootFond().get());

        List<Record> linkedRecords = detailedRecordSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(fieldType.getLinkRootFond().get()));
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(scalarFieldValueCommand.ctx(), HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypedSearchFieldParsing.ofValue(entryFieldTypeId).toSearchField(), new Eq(scalarFieldValueCommand.value()))
            ));
        });

        Record linkedRecord = linkedRecords.isEmpty()
                ? createLinkedRecord(fieldType.getLinkRootFond().get(), entryFieldTypeId, scalarFieldValueCommand)
                : DataUtils.requireSingle(linkedRecords, Record.class, "linked record");

        DetailedRecordValueCommand recordValueCommand = new DetailedRecordValueCommand(linkedRecord, scalarFieldValueCommand.ctx(), scalarFieldValueCommand.currentAuth());

        return command.withValueCommand(recordValueCommand);
    }

    private Record createLinkedRecord(Fond fond, FieldTypeId entryFieldTypeId, ScalarFieldValueCommand<?> originalValueCommand) {
        RecordEditation recordEditation = recordEditationFactory
                .on(originalValueCommand.ctx())
                .ofNew(fond)
                .build(originalValueCommand.currentAuth());

        FieldEditationCommand entryFieldEditationCommand = FieldEditationCommand.of(recordEditation.getRecord(), entryFieldTypeId.toFieldIdWithAllFirstIndices(), originalValueCommand).createMissingHierarchy();
        editField(recordEditation, entryFieldEditationCommand);

        return recordEditation.publish(originalValueCommand.ctx(), originalValueCommand.currentAuth()).getRecord();
    }

    private FieldEditationCommand saveLinkedRecordAndPotentiallyGetAnotherCommand(FieldEditationCommand command, RecordFieldId editingFieldId) {
        if (!(command.valueCommand() instanceof DetailedRecordValueCommand valueCommand)) {
            return command;
        }

        FieldType fieldType = fieldTypeLoader.getById(editingFieldId.fieldId().getFieldTypeId());

        if (fieldType.getLinkRootFond().isEmpty()) {
            return command;
        }

        //nejdrive se podivame, zda nenastavujeme z-kovy zaznam nebo zdrojovy dokument - pak musime ulozit
        Record record = valueCommand.record();
        Record recordToSaveHolding = record;
        if (record.getType().equals(Record.TYPE_DOCUMENT) && fieldType.getLinkRootFond().get().isForSourceDocument()) { // source document
            Record savedRecord = recordSaver.save(RecordSaveCommand.createSourceDocument(record, valueCommand.ctx(), valueCommand.ctx(), valueCommand.currentAuth(), List.of(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_PUBLICATION)), false, fieldType.getLinkRootFond().get()));
            valueCommand = new DetailedRecordValueCommand(savedRecord, valueCommand.ctx(), valueCommand.currentAuth());
            recordToSaveHolding = savedRecord;

        } else if (record.isExternal()) { // external (z) record
            Record savingRecord = Record.createNewByPattern(record);
            Record savedRecord = recordSaver.save(RecordSaveCommand.createStandard(savingRecord, valueCommand.ctx(), valueCommand.ctx(), valueCommand.currentAuth(), List.of(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_PUBLICATION)), false));
            valueCommand = new DetailedRecordValueCommand(savedRecord, valueCommand.ctx(), valueCommand.currentAuth());
            recordToSaveHolding = savedRecord;
        }

        if (recordToSaveHolding.getFond().isAdoptingLinked()) {
            recordHoldingUpserter.upsert(new RecordHoldingUpsertCommand(recordToSaveHolding, valueCommand.ctx(), false, valueCommand.ctx(), valueCommand.currentAuth()));
        }

        return command.withValueCommand(valueCommand);
    }

    private void deleteFieldInternal(@NonNull FieldContainer container, @NonNull FieldDeletionCommand command, @NonNull Journal journal, @NonNull String logPrefix) {
        log.debug("{}Start to {} field {} ({})", logPrefix, command.level(), command.fieldId(), command);

        RecordFieldId originallyDeletingFieldId = command.recordFieldId();
        RecordFieldId deletingFieldId = command.influencerFieldPreferring()
                ? getSingleInfluencerField(originallyDeletingFieldId, journal, logPrefix)
                : originallyDeletingFieldId;

        // delete ("invalidate") all followers first
        var deletingFieldOpt = findField(container, deletingFieldId, command.missingHierarchyIgnoring());
        if (deletingFieldOpt.isPresent()) {
            deleteFollowers(container, deletingFieldId, journal, logPrefix);
        }

        // then delete given field - we must search for it again, because field could be deleted while deleting followers (they can cause whole hierarchy deletion)
        deletingFieldOpt = findField(container, deletingFieldId, true);
        if (deletingFieldOpt.isPresent()) {
            Field<?> deletingField = deletingFieldOpt.get();

            switch (command.level()) {
                case CLEAR -> {
                    log.debug("{}Actually clearing field {}", logPrefix, deletingField);
                    deletingField.deletePayload();
                    journal.addClearing(deletingField.getRecordFieldId());
                }
                case DELETE -> {
                    log.debug("{}Actually deleting field {}", logPrefix, deletingField);
                    FieldContainer parent = deletingFieldId.isRootFieldId()
                            ? container
                            : getField(container, deletingFieldId.existingParentFieldId());
                    parent.remove(deletingField);
                    journal.addDeletion(deletingField.getRecordFieldId());
                }
            }
        }

        // now, we are sure that given field is deleted. Then delete also parent field if is empty now
        if (command.emptyHierarchyDeleting()) {
            deleteEmptyParents(container, deletingFieldId, true, journal, logPrefix);
        }
    }

    private static Optional<Field<?>> findField(FieldContainer container, RecordFieldId recordFieldId, boolean missingFieldIgnoring) {
        var editedFieldOpt = container.getFirstFieldRecursive(By.fieldId(recordFieldId.fieldId()));
        if (editedFieldOpt.isPresent()) {
            return editedFieldOpt;
        }
        if (missingFieldIgnoring) {
            return editedFieldOpt;
        }
        throw new IllegalStateException("Editing/deleting field " + recordFieldId.fieldId() + " does not exist.");
    }

    private static Field<?> getField(FieldContainer fieldContainer, RecordFieldId recordFieldId) {
        return findField(fieldContainer, recordFieldId, false).orElseThrow();
    }

    private RecordFieldId getSingleInfluencerField(@NonNull RecordFieldId sourceFieldId, @NonNull Journal journal, @NonNull String logPrefix) {
        FieldType fieldType = fieldTypeLoader.getById(sourceFieldId.fieldId().getFieldTypeId());
        if (fieldType.getLookups().isEmpty()) {
            log.debug("{}Field {} has no influencer -> returning field itself", logPrefix, sourceFieldId.fieldId());
            return sourceFieldId;
        }
        if (fieldType.getLookups().size() > 1) {
            throw new IllegalStateException("Edited field " + sourceFieldId.fieldId() + " has more than one influencer, cannot be edited directly");
        }
        LookupDefinition lookupDef = ListUtil.getSingle(fieldType.getLookups());
        if (lookupDef.isSelfRecord()) {
            throw new IllegalStateException("Edited field's lookupDef (field " + sourceFieldId.fieldId() + ", lookupDef " + lookupDef + ") has self record link, cannot be edited");
        }
        RecordFieldId influencerFieldId = lookupDef.linkFieldSpec().toLinkFieldSpecOnSpecifiedField(sourceFieldId);
        log.debug("{}Field {} has another single influencer field {}", logPrefix, sourceFieldId, influencerFieldId);
        return influencerFieldId;
    }

    private void deleteFollowers(@NonNull FieldContainer container, @NonNull RecordFieldId influencerFullId, @NonNull Journal journal, @NonNull String logPrefix) {
        List<RecordFieldId> followerIds = streamFollowers(container, influencerFullId).map(Field::getRecordFieldId).toList();
        if (followerIds.isEmpty()) {
            log.debug("{}Field {} has not any followers to delete", logPrefix, influencerFullId.fieldId());
        } else {
            log.debug("{}Field {} has {} followers, deleting them", logPrefix, influencerFullId.fieldId(), followerIds);
        }
        for (RecordFieldId followerId : followerIds) {
            FieldDeletionCommand command = FieldDeletionCommand.of(followerId)
                    .ignoreMissingHierarchy()
                    .notPreferInfluencerFieldDeletion()
                    .deleteAlsoEmptyHierarchy();
            deleteFieldInternal(container, command, journal, extendPrefix(logPrefix));
        }
    }

    private Stream<Field<?>> streamFollowers(@NonNull FieldContainer container, @NonNull RecordFieldId influencerFieldId) {
        FieldFinder<FieldContainer, Field<?>> followersFinder = FieldFinders.by(f -> fieldDependencyResolver.isFieldFollowerOf(f, influencerFieldId));
        return followersFinder.findIn(container);
    }

    private void deleteEmptyParents(@NonNull FieldContainer container, RecordFieldId childFieldId, boolean missingFieldIgnoring, @NonNull Journal journal, @NonNull String logPrefix) {
        if (childFieldId.isRootFieldId()) {
            log.debug("{}Field {} is root, stop deleting empty parents", logPrefix, childFieldId.fieldId());
            return;
        }

        // parent field nemusi existovat (pokud treba vubec neexistovalo ani to puvodne mazane pole), takze ho musime nejdrive hledat
        RecordFieldId parentId = childFieldId.existingParentFieldId();
        var parentFieldOpt = findField(container, parentId, missingFieldIgnoring);
        if (parentFieldOpt.isEmpty()) {
            log.debug("{}Parent field {} does not exist, traversing up in hierarchy for deleting empty parent of this parent", logPrefix, parentId.fieldId());
            deleteEmptyParents(container, parentId, missingFieldIgnoring, journal, logPrefix);
            return;
        }

        Field<?> parentField = parentFieldOpt.get();
        if (parentField.hasAutonomousValue()) {
            log.debug("{}Parent field {} has some autonomous value, stop deleting empty parents", logPrefix, parentId.fieldId());
            return;
        }

        log.debug("{}Parent field {} has no autonomous value, deleting it", logPrefix, parentId.fieldId());
        // asi budeme mazat i kdyz by mel recordLink bez hodnoty (coz podle me snad nikdy nenastane, protoze v tom pripade se asi jedna o komplexni main pole a u toho nejdrive mazeme prave tohoto nadrazeneho)
        FieldDeletionCommand parentDeletionCommand = FieldDeletionCommand.of(parentId)
                .ignoreMissingHierarchy()
                .notPreferInfluencerFieldDeletion()
                .deleteAlsoEmptyHierarchy();
        deleteFieldInternal(container, parentDeletionCommand, journal, extendPrefix(logPrefix));
    }

    private static @NonNull String extendPrefix(@NonNull String logPrefix) {
        return logPrefix + "  ";
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor(staticName = "create")
    private static class Journal implements Iterable<JournalAction> {

        @NonNull
        List<JournalAction> actions = new ArrayList<>();

        public void add(@NonNull JournalItemType type, @NonNull RecordFieldId recordFieldId) {
            actions.add(new JournalAction(type, recordFieldId));
        }

        public void addCreation(@NonNull RecordFieldId recordFieldId) {
            add(JournalItemType.FIELD_CREATION, recordFieldId);
        }

        public void addEditation(@NonNull RecordFieldId recordFieldId) {
            add(JournalItemType.FIELD_EDITATION, recordFieldId);
        }

        public void addClearing(@NonNull RecordFieldId recordFieldId) {
            add(JournalItemType.FIELD_CLEARING, recordFieldId);
        }

        public void addDeletion(@NonNull RecordFieldId recordFieldId) {
            add(JournalItemType.FIELD_DELETION, recordFieldId);
        }

        @Override
        public @NonNull Iterator<JournalAction> iterator() {
            return actions.iterator();
        }

        public Stream<JournalAction> stream() {
            return actions.stream();
        }

        @Override
        public String toString() {
            return "Journal{" + actions + '}';
        }
    }

    private record JournalAction(
            @NonNull JournalItemType type,
            @NonNull RecordFieldId recordFieldId
    ) {

        @Override
        public String toString() {
            return type + " of " + recordFieldId;
        }
    }

    private enum JournalItemType {
        FIELD_CREATION,
        FIELD_EDITATION,
        FIELD_CLEARING,
        FIELD_DELETION
    }

}
