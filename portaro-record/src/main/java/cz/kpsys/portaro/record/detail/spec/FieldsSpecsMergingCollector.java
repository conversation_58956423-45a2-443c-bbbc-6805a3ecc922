package cz.kpsys.portaro.record.detail.spec;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldsSpecsMergingCollector implements Collector<FieldsSpec, FieldsSpecsMergingCollector.Builder, FieldsSpec> {

    @Override
    public Supplier<Builder> supplier() {
        return Builder::ofEmpty;
    }

    @Override
    public BiConsumer<Builder, FieldsSpec> accumulator() {
        return Builder::addAll;
    }

    @Override
    public BinaryOperator<Builder> combiner() {
        return (builder1, builder2) -> {
            builder1.addAll(builder2);
            return builder1;
        };
    }

    @Override
    public Function<Builder, FieldsSpec> finisher() {
        return Builder::build;
    }

    @Override
    public Set<Characteristics> characteristics() {
        return Set.of(Characteristics.UNORDERED);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    public static class Builder {

        @Nullable HashSet<FieldSpec> set;

        public static Builder ofEmpty() {
            return new Builder(new HashSet<>());
        }

        public void setAll() {
            set = null;
        }

        public boolean forAll() {
            return set == null;
        }

        public Set<FieldSpec> existingSpecs() {
            return Objects.requireNonNull(set);
        }

        public void addAll(FieldsSpec fieldsSpec) {
            if (forAll()) {
                return;
            }
            if (fieldsSpec.isForAll()) {
                setAll();
                return;
            }
            if (!fieldsSpec.existingSpecs().isEmpty()) {
                existingSpecs().addAll(fieldsSpec.existingSpecs());
            }
        }

        public void addAll(Builder other) {
            if (forAll()) {
                return;
            }
            if (other.forAll()) {
                setAll();
                return;
            }
            if (!other.existingSpecs().isEmpty()) {
                existingSpecs().addAll(other.existingSpecs());
            }
        }

        public FieldsSpec build() {
            if (forAll()) {
                return FieldsSpec.ofAll();
            }
            return FieldsSpec.of(existingSpecs());
        }
    }
}
