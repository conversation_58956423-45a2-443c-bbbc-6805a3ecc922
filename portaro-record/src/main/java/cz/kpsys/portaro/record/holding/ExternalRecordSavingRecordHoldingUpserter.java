package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.record.RecordSaveCommand;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.edit.RecordEditationEvent;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExternalRecordSavingRecordHoldingUpserter implements RecordHoldingUpserter {

    @NonNull RecordSaver recordSaver;
    @NonNull RecordHoldingUpserter delegate;

    @Override
    public RecordHoldingUpsertResult upsert(RecordHoldingUpsertCommand command) {
        if (command.record().isExternal()) {
            RecordSaveCommand recordSaveCmd = RecordSaveCommand.createStandard(
                    command.record(),
                    command.department(),
                    command.ctx(),
                    command.currentAuth(),
                    List.of(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_PUBLICATION)),
                    false
            );
            recordSaver.save(recordSaveCmd);
        }

        return delegate.upsert(command);
    }
}
