package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.detail.FieldType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AcceptableValueTypedValueConverter implements TypedFieldValueConverter<StringValueCommand, FieldPayload<AcceptableValueFieldValue<LabeledIdentified<String>>>, AcceptableValueFieldValue<LabeledIdentified<String>>> {

    @NonNull ByIdLoadable<? extends LabeledIdentified<String>, String> acceptableValuesLoader;

    @NonNull
    @Override
    public FieldPayload<AcceptableValueFieldValue<LabeledIdentified<String>>> convert(@NonNull StringValueCommand command, @NonNull FieldType fieldType) {
        var acceptableValue = getAcceptableValue(command.value().trim());
        return FieldPayload.of(AcceptableValueFieldValue.ofStringIdentified(acceptableValue));
    }

    private LabeledIdentified<String> getAcceptableValue(String acceptableValueId) {
        return acceptableValuesLoader.getById(acceptableValueId);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
