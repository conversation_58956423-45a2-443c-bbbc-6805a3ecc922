package cz.kpsys.portaro.record;

import cz.kpsys.portaro.record.edit.RecordEditationEvent;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordSaver implements RecordSaver {

    @NonNull RecordSaver delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public Record save(@NonNull RecordSaveCommand cmd) {
        throwIfCannot(cmd);
        return delegate.save(cmd);
    }

    private void throwIfCannot(RecordSaveCommand cmd) {
        if (!cmd.record().isActive()) {
            securityManager.throwIfCannot(RecordSecurityActions.RECORD_DRAFT_SAVE, cmd.currentAuth(), cmd.ctx(), cmd.record());
            return;
        }
        if (RecordEditationEvent.containsAnyOfType(cmd.unsavedModifications(), RecordEditationEvent.Type.RECORD_PUBLICATION)) {
            securityManager.throwIfCannot(RecordSecurityActions.RECORD_PUBLISH, cmd.currentAuth(), cmd.ctx(), cmd.record());
        } else {
            securityManager.throwIfCannot(RecordSecurityActions.RECORD_EDIT, cmd.currentAuth(), cmd.ctx(), cmd.record());
        }
    }
}
