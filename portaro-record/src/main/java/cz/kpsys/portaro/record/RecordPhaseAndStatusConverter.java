package cz.kpsys.portaro.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;
import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordPhaseAndStatusConverter {

    @NonNull ContextualProvider<Department, RecordEditationPhase> publishingDocumentCatalogizationPhaseProvider;
    @NonNull ContextualProvider<Department, Optional<RecordStatus>> editLockThreshold;
    @NonNull SecurityManager securityManager;

    public RecordStatus mapPhaseToStatus(RecordEditationPhase phase, Record record, Department ctx) {
        return switch (phase) {
            case DRAFT -> RecordStatus.DRAFT;
            case EDITING -> RecordStatus.STANDARD_DOCUMENT;
            case FINISHED -> getCatalogedStatus(record);
            case LOCKED -> {
                Optional<RecordStatus> editLockThresholdOn = editLockThreshold.getOn(ctx);
                if (editLockThresholdOn.isEmpty()) {
                    yield getCatalogedStatus(record);
                }
                RecordStatus recordStatus = editLockThresholdOn.get();
                if (record.getType().equals(TYPE_AUTHORITY)) {
                    yield RecordStatus.fromLegacyAuthorityStatusId(recordStatus.getId());
                }
                if (record.getType().equals(TYPE_DOCUMENT)) {
                    yield recordStatus;
                }
                throw new IllegalStateException();
            }
        };
    }

    private static @NonNull RecordStatus getCatalogedStatus(Record record) {
        if (record.getType().equals(TYPE_AUTHORITY)) {
            return RecordStatus.NATIONAL;
        }
        if (record.getType().equals(TYPE_DOCUMENT)) {
            return RecordStatus.FINISHED_CATALOGING;
        }
        throw new IllegalStateException();
    }

    public List<RecordEditationPhaseTransition> nextPhases(Record record, Department ctx, @NonNull UserAuthentication currentAuth) {
        if (!record.isActive()) {
            RecordEditationPhase publishPhase = TYPE_DOCUMENT.equals(record.getType())
                    ? publishingDocumentCatalogizationPhaseProvider.getOn(ctx)
                    : RecordEditationPhase.FINISHED;
            PermissionResult publishPermission = securityManager.resolve(RecordSecurityActions.RECORD_PUBLISH, currentAuth, ctx, record);
            return List.of(new RecordEditationPhaseTransition(publishPhase, publishPermission));
        }

        RecordStatus status = record.getStatus();
        PermissionResult editFondPermission = securityManager.resolve(RecordSecurityActions.RECORD_EDIT_OF_FOND, currentAuth, ctx, record.getFond()); // Sice mame record, ale zjistovani jeho konkretnich prav je pomale (nacita se po jednom editLevel z record operation), takze tady budeme resit jen fond

        Optional<RecordStatus> thresholdOpt = editLockThreshold.getOn(ctx);
        if (thresholdOpt.isPresent() && thresholdOpt.get().isHigherThan(status)) {
            return List.of(new RecordEditationPhaseTransition(RecordEditationPhase.LOCKED, editFondPermission));
        }

        return switch (status) {
            case DRAFT -> List.of(new RecordEditationPhaseTransition(RecordEditationPhase.EDITING, editFondPermission));
            case STANDARD_DOCUMENT -> List.of(new RecordEditationPhaseTransition(RecordEditationPhase.FINISHED, editFondPermission));
            default -> List.of();
        };
    }
}
