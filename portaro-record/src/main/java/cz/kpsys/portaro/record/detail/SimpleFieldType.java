package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValueConverter;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SimpleFieldType extends BasicIdentified<String> implements FieldType, Identified<String>, NamedLabeledIdentified<String>, Serializable {

    @JsonIgnore
    @Getter
    @NonNull
    FieldTypeId fieldTypeId;

    @NonNull
    String nativeName;

    @JsonIgnore
    @Getter
    boolean virtualGroup;

    @Getter
    boolean repeatable;

    @Getter
    boolean autonomous;

    @NonNull
    Optional<Formula<?>> formula;

    @NonNull
    Optional<Codebook<? extends LabeledIdentified<String>, String>> codebook;

    @JsonIgnore
    @Getter
    @NonNull
    FieldGeneration fieldGeneration;

    @JsonIgnore
    @Getter
    @NonNull
    FieldSource fieldSource;

    @Getter
    @NonNull
    TransferType transferType;

    @NonNull
    Optional<ScalarDatatype> valueDatatype;

    @NonNull
    Optional<Fond> linkRootFond;

    @Getter
    @NonNull
    FieldExportSetting exportSetting;

    @NonNull
    List<FieldType> subfieldTypes = new ArrayList<>();

    @NonNull
    Function<FieldTypeId, FieldType> unknownSubfieldTypeFactory = UnknownFieldTypeFactory::createDefaultUnknownSubfieldType;

    @NonNull
    Function<FieldType, FieldValueConverter> fieldValueConverterFactory;

    @NullableNotBlank
    String pic;

    public SimpleFieldType(@NonNull FieldTypeId id,
                           @NonNull String nativeName,
                           boolean virtualGroup,
                           boolean repeatable,
                           boolean autonomous,
                           @NonNull Optional<Formula<?>> formula,
                           @NonNull Optional<Codebook<? extends LabeledIdentified<String>, String>> codebook,
                           @NonNull FieldGeneration fieldGeneration,
                           @NonNull FieldSource fieldSource,
                           @NonNull TransferType transferType,
                           @NonNull Optional<ScalarDatatype> valueDatatype,
                           @NonNull Optional<Fond> linkRootFond,
                           @NonNull FieldExportSetting exportSetting,
                           @NonNull Function<FieldType, FieldValueConverter> fieldValueConverterFactory,
                           @NullableNotBlank String pic) {
        super(id.toString());
        this.fieldTypeId = id;
        this.nativeName = ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(nativeName), FieldTypes.DEFAULT_NATIVE_NAME);
        this.virtualGroup = virtualGroup;
        this.repeatable = repeatable;
        this.autonomous = autonomous;
        this.formula = formula;
        this.codebook = codebook;
        this.fieldGeneration = fieldGeneration;
        this.fieldSource = fieldSource;
        this.transferType = transferType;
        this.valueDatatype = valueDatatype;
        this.linkRootFond = linkRootFond;
        this.exportSetting = exportSetting;
        this.fieldValueConverterFactory = fieldValueConverterFactory;
        this.pic = pic;
    }


    @Override
    public Text getText() {
        Text columnMessageCode = Texts.ofColumnMessageCoded(
                (fieldTypeId.isInAuthority() ? "FDEFAUT" : "FDEF"),
                "NAZEV",
                (fieldTypeId.getLevel() == FieldTypeId.LEVEL_SUBFIELD ? fieldTypeId.existingParent().getCode() : fieldTypeId.getCode()),
                (fieldTypeId.getLevel() == FieldTypeId.LEVEL_SUBFIELD ? fieldTypeId.getCode() : null)
        );
        Text dflt = Texts.ofNative(nativeName);
        return Texts.ofDefaulted(columnMessageCode, dflt);
    }


    @Override
    public String getName() {
        return nativeName;
    }

    @Override
    public @NonNull String getCode() {
        return fieldTypeId.getCode();
    }

    @JsonIgnore
    @Override
    public Optional<ScalarDatatype> getDatatype() {
        return valueDatatype;
    }

    @JsonIgnore
    @Override
    public @NonNull Optional<Fond> getLinkRootFond() {
        return linkRootFond;
    }

    @JsonIgnore
    @Override
    public ScalarDatatype getDatatypeOrThrow() {
        return getDatatype()
                .orElseThrow(() -> {
                    String detail = subfieldTypes.isEmpty() ? "but it is also without any subfield" : "because it is datafield with subfields " + subfieldTypes;
                    return new IllegalStateException("Cannot get datatype, because this field type (%s) has not any datatype (%s)".formatted(this, detail));
                });
    }

    @JsonIgnore
    @NonNull
    public Optional<Codebook<? extends LabeledIdentified<String>, String>> getCodebook() {
        return codebook;
    }

    @JsonIgnore
    @Override
    public @NonNull Optional<Formula<?>> getFormula() {
        return formula;
    }

    @Override
    public @NonNull List<FieldType> getSubfieldTypes() {
        return subfieldTypes;
    }

    @Override
    public FieldType addSubfieldType(FieldType subfieldType) {
        if (!subfieldTypes.contains(subfieldType)) {
            subfieldTypes.add(subfieldType);
        }
        return this;
    }

    @Deprecated
    @NonNull
    @Override
    public FieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull FieldTypeId fieldTypeId) {
        for (FieldType subfieldType : subfieldTypes) {
            if (subfieldType.isVirtualGroup() && subfieldType.getSubfieldTypes().stream().anyMatch(By.type(fieldTypeId))) {
                return subfieldType;
            }
        }
        return subfieldTypes.stream()
                .filter(By.type(fieldTypeId))
                .findFirst()
                .orElseGet(() -> unknownSubfieldTypeFactory.apply(fieldTypeId));
    }

    @Override
    public @NonNull FieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        for (FieldType subfieldType : subfieldTypes) {
            if (subfieldType.isVirtualGroup() && subfieldType.getSubfieldTypes().stream().anyMatch(By.code(subfieldCode))) {
                return subfieldType;
            }
        }
        return subfieldTypes.stream()
                .filter(By.code(subfieldCode))
                .findFirst()
                .orElseGet(() -> unknownSubfieldTypeFactory.apply(fieldTypeId.sub(subfieldCode)));
    }

    @NonNull
    @Override
    public FieldType getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        for (FieldType subfieldType : subfieldTypes) {
            if (subfieldType.isVirtualGroup()) {
                Optional<? extends FieldType> subsubfieldType = subfieldType.getSubfieldTypes().stream()
                        .filter(By.type(fieldTypeId))
                        .findFirst();
                if (subsubfieldType.isPresent()) {
                    return subsubfieldType.get();
                }
            }
        }
        return subfieldTypes.stream()
                .filter(By.type(fieldTypeId))
                .findFirst()
                .orElseGet(() -> unknownSubfieldTypeFactory.apply(fieldTypeId));
    }

    @JsonIgnore
    @Override
    public FieldValueConverter getValueConverter() {
        return fieldValueConverterFactory.apply(this);
    }

    @JsonIgnore
    @Override
    public @NullableNotBlank String pic() {
        return pic;
    }

}
