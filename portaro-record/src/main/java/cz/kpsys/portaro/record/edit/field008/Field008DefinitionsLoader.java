package cz.kpsys.portaro.record.edit.field008;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Field008DefinitionsLoader implements CacheCleaner {

    @NonNull ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> acceptableValuesProviderLoader;
    @NonNull AllValuesProvider<Field008CodeEntity> codeEntityProvider;
    @NonNull AllValuesProvider<Field008LabelEntity> labelEntitiesProvider;
    @NonNull AllValuesProvider<Field008DocumentTypeEntity> documentTypeEntitiesProvider;
    @NonNull Provider<Field008Position> publicationStatusProvider;
    @NonNull Provider<Field008Position> modifiedRecordProvider;
    @NonNull Provider<Field008Position> catalogingSourceProvider;

    public static final String CACHE_NAME = "field008definitions";
    private static final ScalarDatatype COUNTRY_CODE_DATATYPE = Datatype.scalar("VALZEME");
    private static final ScalarDatatype LANGUAGE_CODE_DATATYPE = Datatype.scalar("VALJAZYK");

    @Cacheable(value = CACHE_NAME)
    public Field008Definitions getDefinitions() {
        return new Field008Definitions(
                codeEntityProvider.getAll(),
                labelEntitiesProvider.getAll(),
                documentTypeEntitiesProvider.getAll(),
                createPositionFromDictionary(Field008EditFormData.PLACE_OF_PUBLICATION_LABEL, COUNTRY_CODE_DATATYPE),
                createPositionFromDictionary(Field008EditFormData.LANGUAGE_LABEL, LANGUAGE_CODE_DATATYPE),
                publicationStatusProvider.get(),
                modifiedRecordProvider.get(),
                catalogingSourceProvider.get()
        );
    }

    @NonNull
    private Field008PositionWithDictionaryValue createPositionFromDictionary(String placeOfPublicationLabel, ScalarDatatype countryCodeDatatype) {
        AllValuesProvider<? extends LabeledIdentified<?>> valuesProvider = Objects.requireNonNull(acceptableValuesProviderLoader.getById(countryCodeDatatype));
        return new Field008PositionWithDictionaryValue(placeOfPublicationLabel, valuesProvider.getAll());
    }

    @CacheEvict(value = CACHE_NAME, allEntries = true)
    @Override
    public void clearCache() {

    }
}