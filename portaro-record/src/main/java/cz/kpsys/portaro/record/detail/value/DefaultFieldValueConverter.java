package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultFieldValueConverter implements FieldValueConverter {

    @NonNull List<ConverterRegistration<?>> converters = new ArrayList<>(6);

    public <COMMAND extends FieldValueCommand> DefaultFieldValueConverter register(Class<COMMAND> commandClass, TypedFieldValueConverter<COMMAND, ? extends FieldPayload<?>, ?> converter) {
        register(new ConverterRegistration<COMMAND>(commandClass, converter));
        return this;
    }

    public FieldValueConverter register(List<DefaultFieldValueConverter.ConverterRegistration<?>> registrations) {
        registrations.forEach(this::register);
        return this;
    }

    public FieldValueConverter register(DefaultFieldValueConverter.ConverterRegistration<?> registration) {
        removeConverterWithCommandClass(registration.commandClass());
        converters.add(registration);
        return this;
    }

    @Override
    public <PAYLOAD extends FieldPayload<VH>, VH extends ScalarFieldValue<?>> PAYLOAD convert(@NonNull FieldValueCommand command, @NonNull FieldType fieldType, @NonNull Class<PAYLOAD> desiredType) {
        TypedFieldValueConverter<FieldValueCommand, PAYLOAD, VH> converter = (TypedFieldValueConverter<FieldValueCommand, PAYLOAD, VH>) getConverterForCommand(command);
        PAYLOAD converted = converter.convert(command, fieldType);
        Assert.isInstanceOf(desiredType, converted, () -> String.format("Field value %s converted from %s (of type %s) by %s must be instance of %s (converted for %s)", converted, command, converter.getClass().getSimpleName(), command.getClass().getSimpleName(), desiredType.getSimpleName(), fieldType));
        return converted;
    }

    private TypedFieldValueConverter<FieldValueCommand, ?, ?> getConverterForCommand(FieldValueCommand command) {
        ConverterRegistration<?> registration = ListUtil.findFirstMatching(converters, reg -> isAcceptableConverter(command, reg))
                .orElseThrow(() -> new ItemNotFoundException(
                        ConverterRegistration.class,
                        "command compatible with %s".formatted(command.getClass().getSimpleName()),
                        "There is no field value converter (to convert %s to field value) compatible with %s (all converters for this field are: %s)".formatted(command, command.getClass().getSimpleName(), converters),
                        Texts.ofNative("Cannot find field value converter to convert %s to field value".formatted(command))
                ));
        return ((ConverterRegistration<FieldValueCommand>) registration).converter();
    }

    private boolean isAcceptableConverter(FieldValueCommand command, ConverterRegistration<?> reg) {
        return reg.commandClass().isInstance(command);
    }

    private void removeConverterWithCommandClass(Class<?> commandClass) {
        converters.removeIf(converterRegistration -> converterRegistration.commandClass().equals(commandClass));
    }

    public record ConverterRegistration<COMMAND extends FieldValueCommand>(
            @NonNull Class<COMMAND> commandClass,
            @NonNull TypedFieldValueConverter<COMMAND, ?, ?> converter
    ) {
        @Override
        public String toString() {
            return "ConverterRegistration{for %s by %s}".formatted(commandClass.getSimpleName(), converter);
        }
    }

}
