package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.file.FileViewForm;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.file.*;
import cz.kpsys.portaro.file.directory.BasicDirectory;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreateCommand;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordDescriptor;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.StandardException;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.Objects;

import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4.TABLE;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4;
import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;
import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordWithAttachmentSaverAppserver implements Saver<RecordWithAttachmentSaveCommand, IdentifiedFile> {

    @NonNull Saver<Record, ?> recordHeaderSaver;
    @NonNull Saver<IdentifiedFile, IdentifiedFile> identifiedFileAppserverSaver;
    @NonNull DmlAppserverService service;
    @NonNull Provider<@NonNull FileAccessType> defaultFileAccessTypeProvider;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;

    @Override
    public @NonNull IdentifiedFile save(@NonNull RecordWithAttachmentSaveCommand command) {
        Record record = command.record();
        Integer directoryId = ensureDirectory(record).getId();

        IdentifiedFile resultFile = saveFileIfNecessary(extractIdentifiedFile(command), directoryId);

        if (command.setAsPrimaryFile()) {
            Assert.isTrue(resultFile.hasForm(FileViewForm.IMAGE), () -> "File %s is not image!".formatted(resultFile));

            record.setCover(resultFile);
            recordHeaderSaver.save(record); // Also deletes record from the cache

            TableWrite tableWrite = createRecordTableWrite(resultFile, record, command.currentAuth().getActiveUser());
            service.executeStatement(tableWrite); // Do last. Previous writes can be rolled back, this cannot be
        }

        return resultFile;
    }

    private IdentifiedFile extractIdentifiedFile(@NonNull RecordWithAttachmentSaveCommand command) {
        var file = command.file();
        if (file instanceof LoadedIdentifiedFile loadedFile) {
            return new ByteArrayLoadedIdentifiedFile(IdentifiedFileImpl.newFrom(loadedFile), FileUtils.getBytesOfInputStream(loadedFile.getInputStream()));
        }

        if (file instanceof IdentifiedFile iddFile) {
            return iddFile;
        }
        throw new IllegalArgumentException("Cannot use resource that is not already in DB or does not have data!");
    }

    private IdentifiedFile saveFileIfNecessary(IdentifiedFile identifiedFile, Integer directoryId) {
        if (identifiedFile.getDirectory() != null && (! directoryId.equals(identifiedFile.getDirectory().getId()))) {
            throw new WrongDirectoryException(directoryId, identifiedFile);
        }

        IdentifiedFile savedFile;
        if (identifiedFile.getId() == null || identifiedFile.getDirectory() == null) {
            if (identifiedFile.getDirectory() == null) {
                identifiedFile.setDirectory(BasicDirectory.createOnlyWithId(directoryId));
            }
            savedFile = identifiedFileAppserverSaver.save(identifiedFile); // TODO: možná zbytečně provolává těžkotonážní ukládání souborů
        } else {
            savedFile = identifiedFile;
        }

        Objects.requireNonNull(savedFile.getId());
        return savedFile;
    }

    private TableWrite createRecordTableWrite(Resource file, Record record, BasicUser activeUser) {
        if (record.getType().equals(TYPE_DOCUMENT)) {
            return createDocumentTableWrite(file, record, activeUser);
        }
        if (record.getType().equals(TYPE_AUTHORITY)) {
            return createAuthorityTableWrite(file, record, activeUser);
        }
        throw new RuntimeException("Unknown record type. This should not happen");
    }

    private TableWrite createDocumentTableWrite(Resource file, RecordDescriptor record, BasicUser activeUser) {
        return TableWrite.createUpdate(TABLE)
                .addWhereCol(KAT1_4.RECORD_ID, record.getId())
                .addCol(KAT1_4.FK_FULLTEXT_IMAGE, file)
                .addCol(KAT1_4.FK_UZIV, activeUser.getId())
                .addDateCol(KAT1_4.DATCAS, Instant.now(), true);
    }

    private TableWrite createAuthorityTableWrite(Resource file, RecordDescriptor record, BasicUser activeUser) {
        return TableWrite.createUpdate(KATAUT_4.TABLE)
                .addWhereCol(KATAUT_4.RECORD_ID, record.getId())
                .addCol(KATAUT_4.FK_FULLTEXT_IMAGE, file)
                .addCol(KATAUT_4.FK_UZIV, activeUser.getId())
                .addDateCol(KATAUT_4.DATCAS, Instant.now(), true);
    }

    private @NonNull Directory ensureDirectory(@NonNull Record record) {
        if (record.getDirectoryId() != null && record.getDirectoryId() != 0) {
            return BasicDirectory.createOnlyWithId(record.getDirectoryId());
        }

        String name = StringUtil.limitCharsAndTrimWithEllipsis(
                RecordViewFunctions.documentName(record.getDetail()).orElse("RECORD_ID:" + record.getId()),
                Directory.NAME_MAX_LENGTH, true);
        assert name != null; // NOOP, potlačení varování z IDEY
        var recordDirectory = parentableDirectoryCreator.create(ParentableDirectoryCreateCommand.ofNewTopDirectory(name, defaultFileAccessTypeProvider.get()));
        record.setDirectoryId(recordDirectory.getId());
        recordHeaderSaver.save(record);
        return recordDirectory;
    }

    @StandardException
    public static class WrongDirectoryException extends RuntimeException {

        public WrongDirectoryException(Integer directoryId, IdentifiedFile file) {
            super(String.format("Target record has directory ID \"%s\". Cannot assign file \"%s\" from directory \"%s\"!", directoryId, file, file.getDirectory()));
        }

    }

}
