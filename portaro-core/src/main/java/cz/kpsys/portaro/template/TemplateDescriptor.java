package cz.kpsys.portaro.template;

import cz.kpsys.portaro.commons.object.LabeledIdentified;

/**
 * Descriptor for templates, e.g. "/html/index.html", <br/>
 * it is not exact custom file location - custom file location must contain also department prefix, e.g "/1/html/index.html"
 * <AUTHOR> <PERSON>
 */
public interface TemplateDescriptor extends LabeledIdentified<String> {

    String getType();

    boolean isOfType(String type);

    static String createDepartmentedLocation(Integer departmentId, TemplateDescriptor templateDescriptor) {
        String slashPrefixedDirectoryAndFilename = templateDescriptor.getId();
        return "/" + departmentId + slashPrefixedDirectoryAndFilename;
    }

}
