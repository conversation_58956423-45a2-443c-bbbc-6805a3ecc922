package cz.kpsys.portaro.setting;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.ValueMatchingContextProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiPredicate;

public interface SettingLoader extends SettingKeysRegistry {

    /**
     * Pro zaregistrovani obecneho listeneru na reload nastaveni.
     */
    void registerReloadedListener(SettingReloadedListener listener);

    /**
     * Vrati nastaveni podle daneho klice nebo vyhodi @{@link IniNotSetException}
     * @param <V> data type of ini key
     * @param key ini key
     * @return setting key provider
     */
    <V> Provider<V> getOnRootProvider(SettingKey<V> key);

    <E> List<E> getOnRootProvidedList(SettingKey<List<E>> key);

    /**
     * Vrati nastaveni podle daneho klice nebo vyhodi @{@link IniNotSetException}
     * @param <V> data type of ini key
     * @param key ini key
     * @param department by department
     * @return value of ini key
     */
    <V> V getOn(SettingKey<V> key, Department department) throws IniNotSetException;

    <V> V getOnRoot(SettingKey<V> key);

    @NonNull <V> FondedValues<V> getFondedValuesOn(SettingKey<V> key, Department department) throws IniNotSetException;

    /**
     * Vrati nastaveni podle pujcoven podle daneho klice nebo vyhodi @{@link IniNotSetException}
     * @param <V> data type of ini key
     * @param key ini key
     * @return department provider of ini key
     */
    <V> ContextualProvider<Department, V> getDepartmentedProvider(SettingKey<V> key);

    <V> ContextualProvider<Department, @NonNull FondedValues<V>> getDepartmentedFondedValuesProvider(SettingKey<V> key);

    <V> Map<Department, V> getContextToValueMap(SettingKey<V> key);

    <V> Provider<Collection<Department>> getStaticValueMatchingContextsProvider(SettingKey<@NonNull V> key, @NonNull V matchingValue);

    <V> ValueMatchingContextProvider<Department, V, V> getValueMatchingContextProvider(SettingKey<V> key);

    <CONTEXTUAL_VAL, MATCHED_VAL> ValueMatchingContextProvider<Department, CONTEXTUAL_VAL, MATCHED_VAL> getValueMatchingContextProvider(SettingKey<CONTEXTUAL_VAL> key, @NonNull BiPredicate<@NonNull MATCHED_VAL, @NonNull CONTEXTUAL_VAL> matcherPredicate);

}
