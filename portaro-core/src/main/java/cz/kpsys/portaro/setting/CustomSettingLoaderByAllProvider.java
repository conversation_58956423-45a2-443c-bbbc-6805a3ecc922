package cz.kpsys.portaro.setting;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomSettingLoaderByAllProvider implements AllBySectionsLoader<CustomSetting<String>> {

    @NonNull AllValuesProvider<CustomSetting<String>> allProvider;

    @Override
    public List<CustomSetting<String>> getAllBySections(@NonNull List<String> sections) {
        return ListUtil.filter(allProvider.getAll(), dto -> sections.contains(dto.getId().getSection()));
    }

}
