package cz.kpsys.portaro.setting;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class IniValueLoadException extends RuntimeException implements SeveritedException, UserFriendlyException {

    @Getter
    @NonNull
    SettingTypeId typeId;

    @Getter
    int severity;

    @Getter
    @NonNull
    Text text;

    public IniValueLoadException(@NonNull SettingTypeId typeId, String message, @NonNull Text text, int severity, Throwable e) {
        super(message, e);
        this.typeId = typeId;
        this.text = text;
        this.severity = severity;
    }
}
