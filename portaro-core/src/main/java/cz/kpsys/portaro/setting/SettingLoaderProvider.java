package cz.kpsys.portaro.setting;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Asserter;
import cz.kpsys.portaro.commons.object.NotNullProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.ValidatingProvider;
import cz.kpsys.portaro.department.Department;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SettingLoaderProvider<V> implements Provider<V> {

    @NonNull SettingLoader settingLoader;
    @NonNull SettingKey<V> settingKey;
    @NonNull Provider<Department> rootDepartmentProvider;

    @Override
    public V get() throws IniNotSetException {
        return settingLoader.getOn(settingKey, rootDepartmentProvider.get());
    }

    @Override
    public Provider<@NonNull V> throwingWhenNull() {
        return new NotNullProvider<>(this, () -> new IniNotSetException(settingKey, rootDepartmentProvider.get().getId()));
    }

    @Override
    public Asserter<IniFeatureNotEnabledException> toEnabledAsserter(Predicate<@NonNull V> isEnabledPredicate, @NonNull String featureName, @Nullable Text text) {
        return ValidatingProvider.validWhen(this, isEnabledPredicate, (value) ->
                new IniFeatureNotEnabledException(featureName, text, settingKey, rootDepartmentProvider.get().getId(), value)
        )::get;
    }

    @Override
    public String toString() {
        return "Provider of ini %s on dep %s".formatted(settingKey, rootDepartmentProvider.get().getId());
    }
}
