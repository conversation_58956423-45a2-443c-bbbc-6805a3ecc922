package cz.kpsys.portaro.datatype;

import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.ConversionFailedException;

public interface DatatypableStringConverter {

    Object convertFromString(@Nullable String stringValue, final Datatype datatype) throws ConversionFailedException;

    /**
     * Zkonvertuje napriklad z List Integer na List Building nebo int na Building atp.
     */
    Object convertFromSimpleTypePreservingStructure(Object simpleValue, final ScalarDatatype datatype);

    String convertToString(Object value);

}
