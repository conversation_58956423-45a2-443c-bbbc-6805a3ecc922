package cz.kpsys.portaro.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Locale;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ContextualViewableItemsTypedConverter<SOURCE_ITEM, TARGET_ITEM> implements ViewableItemsTypedConverter<SOURCE_ITEM, TARGET_ITEM> {

    @NonNull ContextualFunction<@NonNull List<? extends SOURCE_ITEM>, Department, @NonNull List<TARGET_ITEM>> listConverter;

    @NonNull
    @Override
    public List<TARGET_ITEM> convert(@NonNull List<SOURCE_ITEM> seekings, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        return listConverter.getOn(seekings, ctx);
    }
}
