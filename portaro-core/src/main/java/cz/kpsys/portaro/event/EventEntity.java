package cz.kpsys.portaro.event;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.LoggingDb.EVENT.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class EventEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = CODE)
    @NonNull
    String code;

    @Column(name = CREATE_DATE)
    @NonNull
    Instant createDate;

    @Column(name = SESSION_ID)
    @Nullable
    String sessionId;

    @Column(name = INITIATOR_USER_ID)
    @Nullable
    Integer initiatorUserId;

    @Column(name = DEPARTMENT_ID)
    @NonNull
    Integer departmentId;

    @Column(name = DATA)
    @Size(min = 1, max = Event.DATA_MAX_LENGTH)
    @NullableNotBlank
    String data;

    @Column(name = SUBJECT_ID)
    @Nullable
    UUID subjectId;
}
