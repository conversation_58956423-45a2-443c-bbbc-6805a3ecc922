package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.StaticCacheFactory;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.database.JpaAllValuesProvider;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BasicCodebookLoaderBuilder implements CodebookLoaderBuilder {

    @NonNull EntityManager entityManager;
    @NonNull StaticCacheFactory staticCacheFactory;
    @NonNull CacheService cacheService;
    @NonNull CacheManager cacheManager;

    @Override
    public <ITEM extends Identified<ID>, ID> @NonNull AllValueProvidedCacheableCodebookLoaderBuilder<ITEM, ID> providedBy(
            @NonNull AllValuesProvider<ITEM> allValuesProvider) {
        return new AllValueProvidedCacheableCodebookLoaderBuilder<>(
                staticCacheFactory,
                cacheService,
                cacheManager,
                allValuesProvider
        );
    }

    @Override
    public <ITEM extends Identified<ID>, ID> @NonNull AllValueProvidedCacheableCodebookLoaderBuilder<ITEM, ID> providedByJpa(
            @NonNull Class<ITEM> dtoClass) {
        return providedBy(getJpaAllValuesProvider(dtoClass, null));
    }

    @Override
    public <ITEM extends Identified<ID>, ID> @NonNull AllValueProvidedCacheableCodebookLoaderBuilder<ITEM, ID> providedByJpa(
            @NonNull Class<ITEM> dtoClass, @NonNull String sortProperty) {
        return providedBy(getJpaAllValuesProvider(dtoClass, Sort.by(sortProperty)));
    }

    private <ITEM extends Identified<ID>, ID> JpaAllValuesProvider<ITEM, ID> getJpaAllValuesProvider(
            @NonNull Class<ITEM> dtoClass, @Nullable Sort sort) {
        JpaAllValuesProvider<ITEM, ID> jpaAllValuesProvider = new JpaAllValuesProvider<>(new SimpleJpaRepository<>(dtoClass, entityManager));
        if (sort != null) {
            jpaAllValuesProvider.withSorting(sort);
        }
        return jpaAllValuesProvider;
    }

}
