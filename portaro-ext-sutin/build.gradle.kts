dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.hamcrest:hamcrest-library:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-exemplar-import"))
    implementation(project(":portaro-exemplar"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-properties"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-erp"))
    implementation(project(":portaro-ext-synchronizer"))
    implementation(project(":portaro-loan"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")
    implementation("org.springframework.data:spring-data-jpa:3.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("com.zaxxer:HikariCP:6.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("com.googlecode.libphonenumber:libphonenumber:8.+")
    implementation("org.mariadb.jdbc:mariadb-java-client:3.4.+")
}
