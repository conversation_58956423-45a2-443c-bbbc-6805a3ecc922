package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.erp.ErpSettingsKeys;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.companies.SutinCompany;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinCompanyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.companies.Spolecnost;
import cz.kpsys.portaro.ext.sutin.recordeditation.CompanyRecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingId;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.ext.sutin.SutinContants.*;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinCompanyLoaderAndSaver {

    @NonNull SutinDataLoader databaseLoader;
    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull CompanyRecordEditationCreator companyRecordEditationCreator;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull AllValuesProvider<Department> departmentLoader;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull Saver<CustomSetting<String>, ?> customSettingSaver;

    @Transactional
    public void syncCompaniesWithSutin(Department ctx, UserAuthentication currentAuth) {

        Department department = departmentAccessor.getById(DEPARTMENT_ID_FOR_COMPANY_DIRECTORY);
        if (!departmentAccessor.getTheseAndParentChains(List.of(department)).contains(ctx)) {
            throw new IllegalStateException("Cannot call synchronization function in current context (%s), worst context should be (%s)".formatted(ctx.getName(), department.getName()));
        }

        List<SutinCompanyResponse> directory = databaseLoader.loadSutinCompanies(department);
        directory.addAll(getCustomCompanies());

        List<SutinCompany> allSutinCompanies = ListUtil.convertStrict(directory, company -> SutinCompany.mapNewCompany(company, tryToFindRecordIdOfExistingRecordByElementId(company).orElse(null), department));

        List<RecordEditationWithMetadata> recordEditations = ListUtil.convertStrict(allSutinCompanies, company -> {
            if (company.recordId().isPresent()) {
                return new RecordEditationWithMetadata(companyRecordEditationCreator.ofExistingRecord(company, List.of(department), ctx, currentAuth), company.linkedDivisionElementId(), company.workReportEnabled());
            }
            return new RecordEditationWithMetadata(companyRecordEditationCreator.ofNewRecord(company, List.of(department), ctx, currentAuth), company.linkedDivisionElementId(), company.workReportEnabled());
        });

        recordEditations.stream().map(RecordEditationWithMetadata::recordEditation).forEach(recordEditation -> {
            if (recordEditation.isDraft()) {
                recordEditation.publish(ctx, currentAuth);
            } else {
                recordEditation.saveIfModified(ctx, currentAuth);
            }
            log.info("Company: {} was saved.", recordEditation.getRecord());
        });

        recordEditations.stream().filter(recordEditationWithMetadata -> recordEditationWithMetadata.linkedDivisionElementId() != null).forEach(
                recordEditationWithMetadata -> {
                    Optional<? extends Department> division = ListUtil.findSingleMatching(
                            departmentLoader.getAll(),
                            companyDepartment -> Objects.equals(companyDepartment.getSyncId(), recordEditationWithMetadata.linkedDivisionElementId()),
                            Department.class,
                            "SyncId(Division elementId)=" + recordEditationWithMetadata.linkedDivisionElementId()
                    );
                    if (division.isEmpty()) {
                        log.error("Non existing division for linked division {}. Company: {} was not found in existing division.", recordEditationWithMetadata.linkedDivisionElementId(), recordEditationWithMetadata.recordEditation().getRecord().getName());
                        return;
                    }
                    Department divisionDepartment = division.get();
                    divisionDepartment.setRid(recordEditationWithMetadata.recordEditation().getRecord().getId());
                    departmentSaver.save(divisionDepartment);
                    log.info("Division {} linked to company: {}", recordEditationWithMetadata.linkedDivisionElementId(), recordEditationWithMetadata.recordEditation().getRecord().getName());

                    if (recordEditationWithMetadata.workReportEnabled()) {
                        CustomSettingId customSettingId = new CustomSettingId(ErpSettingsKeys.WORK_REPORT_ENABLED, divisionDepartment.getId(), null);
                        var loadedCustomSetting = customSettingLoader.getByCompleteIdOrCreateEmpty(customSettingId);
                        var updatedCustomSetting = new CustomSetting<>(customSettingId, "true", null, loadedCustomSetting.getUuid());
                        customSettingSaver.save(updatedCustomSetting);
                        log.info("Work report enabled for company: {}", recordEditationWithMetadata.recordEditation().getRecord().getName());
                    }

                });
    }

    private @NonNull List<SutinCompanyResponse> getCustomCompanies() {
        return departmentLoader.getAll().stream().filter(companyDepartment -> EXPLICIT_DIVISION_SYNC_ID_WITH_NEW_RECORD.contains(companyDepartment.getSyncId())).map(companyDepartment -> {
            Spolecnost company = new Spolecnost(SUTOR_CUSTOM_RECORDS_ELEMENT_ID_PREFIX + companyDepartment.getSyncId(), companyDepartment.getName(), null, null, companyDepartment.getSyncId(), null);
            return new SutinCompanyResponse(company, List.of(), List.of());
        }).toList();
    }

    private Optional<UUID> tryToFindRecordIdOfExistingRecordByElementId(SutinCompanyResponse company) {
        return sutinRecordDataLoader.getRecordIdByExternalId(company.companyData().elementId(), SutinContants.ELEMENT_ID_FIELD_CODE);
    }

    record RecordEditationWithMetadata(@NonNull RecordEditation recordEditation, @Nullable String linkedDivisionElementId, @NonNull Boolean workReportEnabled) {
    }
}
