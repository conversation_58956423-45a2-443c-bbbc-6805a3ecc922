package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum PracSmlouvaTypKat implements Identified<Integer> {

    NOT_DEFINED(1, "---"),
    HPP_NEURCITA(3, "HPP Neurčitá"),
    HPP_URCITA(4, "HPP Určitá"),
    ZIVNOST(5, "Živnost"),
    SUBDODAVATEL(6, "subdodavatel"),
    TMA(7, "Tma"),
    DPP(8, "DPP"),
    DPC(9, "DPČ"),
    PRAXE(10, "Praxe");

    public static final Codebook<PracSmlouvaTypKat, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer sutorId;
    @NonNull String value;

    public @NonNull Integer getId() {
        return sutorId;
    }

    public @NonNull String portaroVal() {
        return sutorId.toString();
    }

}
