package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentEntity;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompanyDepartmentLinker {

    @NonNull Saver<Department, DepartmentEntity> departmentSaver;

    public void link(LinkCompanyToDepartmentCommand command) {
        command.department().setRid(command.company().getId());
        departmentSaver.save(command.department());
    }
}
