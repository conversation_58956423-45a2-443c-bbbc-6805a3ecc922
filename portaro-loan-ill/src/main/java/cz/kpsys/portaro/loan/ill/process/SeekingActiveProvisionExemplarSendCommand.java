package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.Seeking;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.Instant;

@With
public record SeekingActiveProvisionExemplarSendCommand(

        @NonNull
        Seeking seeking,

        @NonNull
        ProvisionWay provisionWay,

        @NonNull
        Instant date,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        @Nullable Boolean ignoreUserHasDebt,
        @Nullable Boolean ignoreUserHasNotValidRegistration,
        @Nullable Boolean ignoreDocumentWasLoanedInPast,
        @Nullable Boolean ignoreSpecialInformationOnLocation,
        @Nullable Boolean ignoreSpecialInformationOnStatus,
        @Nullable Boolean ignoreSpecialInformationOnCategory,
        @Nullable Boolean ignoreLendingOnDifferentDepartment,
        @Nullable Boolean ignoreLendingOnDifferentLocation,
        @Nullable Boolean ignoreSpecialInformationOnHelperField,
        @Nullable Boolean ignoreDocumentHasAttachment,
        @Nullable Boolean ignoreOverdueNotices,
        @Nullable Boolean ignoreBlockedTransactions,
        @Nullable Boolean ignoreOtherUserUnprocessedOrder,
        @Nullable Boolean ignoreOtherUserUnsentReservation,
        @Nullable Boolean ignoreExceededLoanLimit,
        @Nullable Boolean ignoreExceededLoanLimitInCategory,
        @Nullable Boolean ignoreDocumentIsAlreadyLentBySameUser,
        @Nullable Boolean ignoreReaderHasReservationOnAnotherItem,
        @Nullable Boolean ignoreDocumentInBackwardCataloging

) {}
