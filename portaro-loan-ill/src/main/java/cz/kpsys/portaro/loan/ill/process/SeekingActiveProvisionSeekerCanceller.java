package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.business.cancel.Canceller;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.loan.ill.Seeking;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekingActiveProvisionSeekerCanceller implements Canceller<SeekingActiveProvisionSeekerCancelCommand> {

    @NonNull Saver<Seeking, ?> seekingSaver;

    @Transactional
    @Override
    public void cancel(@NonNull SeekingActiveProvisionSeekerCancelCommand command) throws DataAccessException {
        command.seeking().canCancelActiveProvision().throwIfCannot();

        Seeking modified = command.seeking()
                .cancelActiveProvisionBySeeker(command.date())
                .cancelActiveProvisionByProvider(command.date());
        seekingSaver.save(modified);
    }

}
