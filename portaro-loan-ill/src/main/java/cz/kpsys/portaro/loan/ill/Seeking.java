package cz.kpsys.portaro.loan.ill;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.scan.Scannable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

import static cz.kpsys.portaro.security.PermissionResult.allow;
import static cz.kpsys.portaro.security.PermissionResult.disallowedState;

@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@With
@FieldNameConstants
public final class Seeking implements LabeledIdentified<Integer> {

    public static final String SCHEMA_EXAMPLE_ID = "5435";

    @EqualsAndHashCode.Include
    @NonNull
    Integer id;

    @JsonIgnore
    @Nullable
    Integer realizationId;

    @JsonIgnore
    boolean providersView;

    /**
     * Uživatel (typicky čtenář), který inicioval MVS
     */
    @JsonIgnore
    @Setter
    @Nullable
    BasicUser requester;

    /**
     * Knihovna, která zprostředkovává MVS
     */
    @JsonIgnore
    @Setter
    @Nullable
    BasicUser seeker;

    @JsonIgnore
    @Nullable
    Loan loan;

    @JsonIgnore
    @Setter
    @NonNull
    SeekingDesiredExemplar desiredExemplar;

    @JsonIgnore
    @Setter
    boolean eventuallyPhotocopyable;

    @JsonIgnore
    @Setter
    boolean eventuallyReservable;

    @JsonIgnore
    @Setter
    boolean eventuallyOnSiteLendable;

    @JsonIgnore
    @Setter
    boolean eventuallyAbroadDeliverable;

    @JsonIgnore
    @Setter
    @NullableNotBlank
    String note;

    @JsonIgnore
    @Setter
    @NullableNotBlank
    String seekerReferenceId;

    @JsonIgnore
    @Setter
    @Nullable
    Instant requesterObtainDeadlineDate;

    @JsonIgnore
    @Setter
    @Nullable
    Department department;

    @JsonIgnore
    @Setter
    @NonNull
    Instant createDate;

    @JsonIgnore
    @Setter
    @Nullable
    Instant commenceDate;

    @JsonIgnore
    @Setter
    @NonNull
    Boolean cancelled;

    @JsonIgnore
    @Setter
    @NullableNotBlank
    String ziskejTicketId;

    @JsonIgnore
    @NonNull
    IllSeekingProvisionList provisionList;


    @JsonIgnore
    public @NonNull BasicUser knownRequester() {
        return Objects.requireNonNull(requester, () -> "Requester is not known of ILL seeking (id %s, active %s)".formatted(id, isProvidersView()));
    }

    @JsonIgnore
    public @NonNull BasicUser knownSeeker() {
        return Objects.requireNonNull(seeker, () -> "Seeker is not known of ILL seeking (id %s, active %s)".formatted(id, isProvidersView()));
    }

    @JsonIgnore
    public @NonNull Department knownDepartment() {
        return Objects.requireNonNull(department, () -> "Department is not known of ILL seeking (id %s, active %s)".formatted(id, isProvidersView()));
    }

    @JsonIgnore
    public boolean isSeekersView() {
        return !providersView;
    }

    @JsonIgnore
    public boolean isProvidersView() {
        return providersView;
    }

    @JsonIgnore
    public SeekingState state() {
        if (getActiveProvision() != null) {
            SeekingProvision activeProvision = getExistingActiveProvision();
            if (activeProvision.isProviderReceived()) {
                return SeekingState.RECEIVED_BACK_BY_PROVIDER;
            }
            if (activeProvision.isSeekerSent()) {
                return SeekingState.SENT_BACK_TO_PROVIDER;
            }
            if (hasLoan() && loan.getState() == LoanState.CANCELLED_RESERVATION) {
                return SeekingState.NOT_PICKED_BY_REQUESTER;
            }
            if (hasLoan() && loan.getState() == LoanState.RETURNED) {
                return SeekingState.RETURNED_BY_REQUESTER;
            }
            if (hasLoan() && loan.getState() == LoanState.LENT) {
                return SeekingState.LENT;
            }
            if (activeProvision.isSeekerReceived()) {
                return SeekingState.RECEIVED_PROM_PROVIDER;
            }
            if (activeProvision.isProviderSent()) {
                return SeekingState.SENT_BY_PROVIDER;
            }
            if (activeProvision.isProviderAccepted()) {
                return SeekingState.ACCEPTED_BY_PROVIDER;
            }
            return SeekingState.ACTIVATED;
        }
        if (isCommenced()) {
            return SeekingState.COMMENCED;
        }
        return SeekingState.REQUESTED;
    }

    @NonNull
    @Override
    public Text getText() {
        return MultiText.ofTexts("Seeking for {}: {}", getDesiredExemplar().document().getText(), state().getText());
    }

    @JsonIgnore
    @NonNull
    public Boolean isCommenced() {
        return commenceDate != null;
    }

    public boolean hasLoan() {
        return loan != null;
    }

    @JsonIgnore
    public boolean wasLent() {
        return hasLoan() && loan.getLendDate().isPresent();
    }

    @JsonIgnore
    public boolean isReturned() {
        return hasLoan() && loan.getReturnDate().isPresent();
    }

    @JsonIgnore
    @NonNull
    public SeekingProvision getExistingActiveProvision() {
        return provisionList.existingActiveProvision();
    }

    @JsonIgnore
    @NonNull
    public Boolean hasActiveProvision() {
        return provisionList.hasActiveProvision();
    }

    @JsonIgnore
    @Nullable
    public SeekingProvision getActiveProvision() {
        return provisionList.activeProvision();
    }

    @JsonIgnore
    public boolean isManagedByZiskej() {
        if (isSeekersView()) {
            return hasZiskejTicketId();
        }
        return getExistingActiveProvision().hasZiskejSubticketId();
    }

    @JsonIgnore
    public boolean hasZiskejTicketId() {
        return ziskejTicketId != null;
    }

    @JsonIgnore
    @NonNull
    public Boolean isManagedExternally() {
        return hasZiskejTicketId();
    }



    public Seeking touchActiveProvision(Function<SeekingProvision, SeekingProvision> modifier) {
        provisionList.touchActiveProvision(modifier);
        return this;
    }

    public Seeking touchProvisionList(Function<IllSeekingProvisionList, IllSeekingProvisionList> modifier) {
        provisionList = modifier.apply(provisionList);
        return this;
    }

    public Seeking cancel(Instant date) {
        cancelled = true;
        return this;
    }

    public Seeking createZiskejLink(String ziskejTicketId, Instant date) {
        setZiskejTicketId(ziskejTicketId);
        return this;
    }

    public Seeking commence(Department department, @NullableNotBlank String seekerReferenceId, Instant date) {
        setDepartment(department);
        setSeekerReferenceId(seekerReferenceId);
        setCommenceDate(date);
        return this;
    }

    public Seeking activateUpcomingProvision(Instant date) {
        Assert.state(!hasActiveProvision(), "Cannot activate provision, another one is already active");
        provisionList.shiftUpcomingProvision(date);
        if (!isManagedExternally()) {
            touchActiveProvision(prov -> prov.withSeekerAcceptDate(date));
            touchActiveProvision(prov -> prov.withProviderAcceptDate(date));
        }
        return this;
    }

    public Seeking acceptProvisionByProvider(Department department, Instant date) {
        touchActiveProvision(prov -> prov.withDepartment(department));
        touchActiveProvision(prov -> prov.withProviderAcceptDate(date));
        touchActiveProvision(prov -> prov.withSeekerAcceptDate(date)); // seeker accept date nastavujeme bezohledu na to, jestli je managedExternally - pokud nemame podminku, seeker proste vzdy souhlasi
        return this;
    }

    public Seeking conditionallyAcceptProvisionByProvider(Department department, String acceptCondition, Instant date) {
        touchActiveProvision(prov -> prov.withDepartment(department));
        touchActiveProvision(prov -> prov.withProviderAcceptDate(date));
        touchActiveProvision(prov -> prov.withProviderAcceptCondition(acceptCondition));
        if (!isManagedExternally()) {
            touchActiveProvision(prov -> prov.withSeekerAcceptDate(date));
        }
        return this;
    }

    public Seeking acceptProvisionConditionBySeeker(Instant date) {
        touchActiveProvision(prov -> prov.withProviderAcceptDate(ObjectUtil.firstNotNull(prov.providerAcceptDate(), date))); // this is for sure - when seeker accept exemplar, provision should be accepted by provider
        touchActiveProvision(prov -> prov.withSeekerAcceptDate(date));
        return this;
    }

    public Seeking sendProvisionPhysicalExemplar(@NonNull Exemplar exemplar, @NonNull Loan loan, Instant date) {
        touchActiveProvision(prov -> prov.withProviderAcceptDate(ObjectUtil.firstNotNull(prov.providerAcceptDate(), date))); // this is for sure - when we send exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withSeekerAcceptDate(ObjectUtil.firstNotNull(prov.seekerAcceptDate(), date))); // this is for sure - when we send exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withProviderSendDate(date));
        touchActiveProvision(prov -> prov.withExemplarIdentifiers(ListUtil.notEmptyCollection(exemplar.getIdentifiers())));
        touchActiveProvision(prov -> prov.withLoan(loan));
        if (!isManagedExternally()) {
            touchActiveProvision(prov -> prov.withSeekerReceiveDate(date));
        }
        return this;
    }

    public Seeking sendProvisionPhotocopiedExemplar(Instant date) {
        touchActiveProvision(prov -> prov.withProviderAcceptDate(ObjectUtil.firstNotNull(prov.providerAcceptDate(), date))); // this is for sure - when we send exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withSeekerAcceptDate(ObjectUtil.firstNotNull(prov.seekerAcceptDate(), date))); // this is for sure - when we send exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withProviderSendDate(date));
        if (!isManagedExternally()) {
            touchActiveProvision(prov -> prov.withSeekerReceiveDate(date));
        } else {
            throw new IllegalStateException("Photocopied exemplar sending is not supported on externally managed (Získej) seeking");
        }
        return this;
    }

    public Seeking receiveProvisionExemplar(@NonNull Scannable exemplarIdentifier, @NonNull Loan loan, Instant date) {
        touchActiveProvision(prov -> prov.withSeekerActivateDate(ObjectUtil.firstNotNull(prov.seekerActivateDate(), date))); // this is for sure - when we receive exemplar, provision should be activated
        touchActiveProvision(prov -> prov.withProviderAcceptDate(ObjectUtil.firstNotNull(prov.providerAcceptDate(), date))); // this is for sure - when we receive exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withSeekerAcceptDate(ObjectUtil.firstNotNull(prov.seekerAcceptDate(), date))); // this is for sure - when we receive exemplar, provision should be accepted
        touchActiveProvision(prov -> prov.withProviderSendDate(ObjectUtil.firstNotNull(prov.providerSendDate(), date)));
        touchActiveProvision(prov -> prov.withSeekerReceiveDate(date));
        touchActiveProvision(prov -> prov.withExemplarIdentifiers(Set.of(exemplarIdentifier)));
        this.loan = loan;
        return this;
    }

    public Seeking cancelActiveProvisionBySeeker(Instant date) {
        provisionList.cancelActiveBySeeker(isSeekersView(), date);
        cancelWholeSeekingWhenNotSeekersViewAndProvisionFullyCancelled(date);
        return this;
    }

    public Seeking cancelActiveProvisionByProvider(Instant date) {
        provisionList.cancelActiveByProvider(isSeekersView(), date);
        cancelWholeSeekingWhenNotSeekersViewAndProvisionFullyCancelled(date);
        return this;
    }

    private void cancelWholeSeekingWhenNotSeekersViewAndProvisionFullyCancelled(Instant date) {
        if (!isSeekersView() && getExistingActiveProvision().isSeekerCancelled() && getExistingActiveProvision().isProviderCancelled()) {
            cancel(date);
        }
    }

    public Seeking sendBackBySeeker(Loan returnedSeekingLoan, Instant date) {
        this.loan = returnedSeekingLoan;
        touchActiveProvision(prov -> prov.withSeekerSendDate(date));
        if (!isManagedExternally()) {
            touchActiveProvision(prov -> prov.withProviderReceiveDate(date));
        }
        return this;
    }

    public Seeking receiveBack(Loan returnedProvisionLoan, Instant date) {
        touchActiveProvision(prov -> prov.withProviderReceiveDate(date));
        touchActiveProvision(prov -> prov.withLoan(returnedProvisionLoan));
        return this;
    }





    public PermissionResult canCancel() {
        if (getCancelled()) {
            return disallowedState(Texts.ofNative("Seeking is already cancelled (seeking id %s)".formatted(id)));
        }
        return switch (state()) {
            case RECEIVED_BACK_BY_PROVIDER, SENT_BACK_TO_PROVIDER, RETURNED_BY_REQUESTER, LENT, NOT_PICKED_BY_REQUESTER, RECEIVED_PROM_PROVIDER ->
                    disallowedState(Texts.ofNative("Seeking cannot be cancelled when state is %s (seeking id %s)".formatted(state(), id)));
            case SENT_BY_PROVIDER, ACCEPTED_BY_PROVIDER -> {
                if (isManagedExternally()) {
                    yield disallowedState(Texts.ofNative("Seeking cannot be cancelled when externally managed (Získej) and state is %s (seeking id %s)".formatted(state(), id)));
                }
                yield allow();
            }
            default -> allow();
        };
    }

    private PermissionResult canNotCancelledAction() {
        if (getCancelled()) {
            return disallowedState(Texts.ofNative("Seeking is cancelled (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    private PermissionResult canCommencedAction() {
        if (!isCommenced()) {
            return disallowedState(Texts.ofNative("Seeking is not commenced yet (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    private PermissionResult canExistingActiveNotFinishedProvisionAction() {
        if (!hasActiveProvision()) {
            return disallowedState(Texts.ofNative("Given seeking has not any active provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isProviderReceived()) {
            return disallowedState(Texts.ofNative("Given seeking active provision is already finished (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    private PermissionResult canProvidedExistingActiveProvisionAction() {
        PermissionResult canCommencedAction = canCommencedAction();
        if (canCommencedAction.forbidden()) {
            return canCommencedAction;
        }
        if (!isProvidersView()) {
            return disallowedState(Texts.ofNative("Given seeking is not for provider view (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canCreateLink() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canCommencedAction = canCommencedAction();
        if (canCommencedAction.forbidden()) {
            return canCommencedAction;
        }
        if (isManagedExternally()) {
            return disallowedState(Texts.ofNative("Cannot link of already linked seeking (seeking id %s)".formatted(id)));
        }
        if (hasActiveProvision()) {
            return disallowedState(Texts.ofNative("Cannot link of already activated seeking (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canEdit() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canCommencedAction = canCommencedAction();
        if (canCommencedAction.forbidden()) {
            return canCommencedAction;
        }
        return allow();
    }

    public PermissionResult canEditSeekingData() {
        PermissionResult canEdit = canEdit();
        if (canEdit.forbidden()) {
            return canEdit;
        }
        if (isManagedExternally()) {
            return disallowedState(Texts.ofNative("Cannot edit externally-managed seeking (seeking id %s)".formatted(id)));
        }
        if (hasActiveProvision() && getExistingActiveProvision().isProviderSent()) {
            return disallowedState(Texts.ofNative("Cannot edit already sent provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canCommence() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        if (isCommenced()) {
            return disallowedState(Texts.ofNative("Seeking is already commenced (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canCreateProvision() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canCommencedAction = canCommencedAction();
        if (canCommencedAction.forbidden()) {
            return canCommencedAction;
        }
        if (hasActiveProvision()) {
            return disallowedState(Texts.ofNative("Given seeking has already activated provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canAutoSearchProvisions() {
        PermissionResult canCreateProvision = canCreateProvision();
        if (canCreateProvision.forbidden()) {
            return canCreateProvision;
        }
        if (!isManagedExternally()) {
            return disallowedState(Texts.ofNative("Automatic search is allowed only to externally-managed seeking (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canEditProvisionData(SeekingProvision provision) {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canEdit = canEdit();
        if (canEdit.forbidden()) {
            return canEdit;
        }
        if (isManagedExternally() && provision.isActivated()) {
            return disallowedState(Texts.ofNative("Cannot edit already activated, ziskej-managed provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canCancelProvision(SeekingProvision provision) {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canEdit = canEdit();
        if (canEdit.forbidden()) {
            return canEdit;
        }
        if (provision.isActivated()) {
            return disallowedState(Texts.ofNative("Cannot edit already activated, ziskej-managed provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canEditActiveProvisionData() {
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        return canEditProvisionData(getExistingActiveProvision());
    }

    public PermissionResult canActivateUpcomingProvision() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        if (hasActiveProvision()) {
            return disallowedState(Texts.ofNative("Given seeking has already activated provision (seeking id %s)".formatted(id)));
        }
        if (!provisionList.hasAnyPendingProvision()) {
            return disallowedState(Texts.ofNative("Given seeking has not any pending provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canSendActiveProvisionMessage() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        if (isManagedExternally()) {
            return disallowedState(Texts.ofNative("Cannot send message to externally-managed seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canAcceptActiveProvision() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canProvidedExistingActiveProvisionAction = canProvidedExistingActiveProvisionAction();
        if (canProvidedExistingActiveProvisionAction.forbidden()) {
            return canProvidedExistingActiveProvisionAction;
        }
        if (getExistingActiveProvision().isProviderAccepted()) {
            return disallowedState(Texts.ofNative("Cannot accept already accepted seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canAcceptActiveProvisionCondition() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        if (!getExistingActiveProvision().hasProviderAcceptCondition()) {
            return disallowedState(Texts.ofNative("Cannot accept condition of not-conditioned seeking provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isSeekerAccepted()) {
            return disallowedState(Texts.ofNative("Cannot accept condition of already accepted seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canSendActiveProvisionExemplar() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canProvidedExistingActiveProvisionAction = canProvidedExistingActiveProvisionAction();
        if (canProvidedExistingActiveProvisionAction.forbidden()) {
            return canProvidedExistingActiveProvisionAction;
        }
        if (!getExistingActiveProvision().isProviderAccepted()) {
            return disallowedState(Texts.ofNative("Cannot send exemplar of not-by-provider-accepted seeking provision (seeking id %s)".formatted(id)));
        }
        if (isManagedExternally() && !getExistingActiveProvision().isSeekerAccepted()) {
            return disallowedState(Texts.ofNative("Cannot send exemplar of not-by-seeker-accepted seeking provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isProviderSent()) {
            return disallowedState(Texts.ofNative("Cannot send exemplar of already sent seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canReceiveActiveProvisionExemplar() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        if (isManagedExternally() && !getExistingActiveProvision().isProviderAccepted()) {
            return disallowedState(Texts.ofNative("Cannot receive exemplar of not-by-provider-accepted seeking provision (seeking id %s)".formatted(id)));
        }
        if (isManagedExternally() && !getExistingActiveProvision().isSeekerAccepted()) {
            return disallowedState(Texts.ofNative("Cannot receive exemplar of not-by-requester-accepted seeking provision (seeking id %s)".formatted(id)));
        }
        if (isManagedExternally() && !getExistingActiveProvision().isProviderSent()) {
            return disallowedState(Texts.ofNative("Cannot receive exemplar of not yet sent (from provider) provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isSeekerReceived()) {
            return disallowedState(Texts.ofNative("Cannot receive exemplar of already received seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canSendBackActiveProvisionExemplar() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        if (!getExistingActiveProvision().isSeekerReceived()) {
            return disallowedState(Texts.ofNative("Cannot send back exemplar of not-yet-received seeking provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isSeekerSent()) {
            return disallowedState(Texts.ofNative("Cannot send back exemplar of already sent back seeking provision (seeking id %s)".formatted(id)));
        }
        if (!hasLoan()) {
            return disallowedState(Texts.ofNative("Processed seeking provision exemplar is received (from counterparty), but seeking does not have a loan assigned (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canReceiveBackActiveProvisionExemplar() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canProvidedExistingActiveProvisionAction = canProvidedExistingActiveProvisionAction();
        if (canProvidedExistingActiveProvisionAction.forbidden()) {
            return canProvidedExistingActiveProvisionAction;
        }
        if (isManagedExternally() && !getExistingActiveProvision().isSeekerSent()) {
            return disallowedState(Texts.ofNative("Cannot receive back exemplar of not-yet sent back (by seeker) seeking provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isProviderReceived()) {
            return disallowedState(Texts.ofNative("Cannot receive back exemplar of already received seeking provision (seeking id %s)".formatted(id)));
        }
        if (!getExistingActiveProvision().hasActiveLoan()) {
            return disallowedState(Texts.ofNative("Seeking provision exemplar is sent (from counterparty), but active provision does not have a loan assigned (seeking id %s)".formatted(id)));
        }
        return allow();
    }

    public PermissionResult canCancelActiveProvision() {
        PermissionResult canNotCancelledAction = canNotCancelledAction();
        if (canNotCancelledAction.forbidden()) {
            return canNotCancelledAction;
        }
        PermissionResult canExistingActiveProvisionAction = canExistingActiveNotFinishedProvisionAction();
        if (canExistingActiveProvisionAction.forbidden()) {
            return canExistingActiveProvisionAction;
        }
        if (getExistingActiveProvision().isSeekerCancelled()) {
            return disallowedState(Texts.ofNative("Cannot cancel already cancelled (or cancel-requested) seeking provision (seeking id %s)".formatted(id)));
        }
        if (isManagedExternally() && getExistingActiveProvision().isProviderAccepted()) {
            return disallowedState(Texts.ofNative("Cannot cancel already accepted, externally managed (Získej) seeking provision (seeking id %s)".formatted(id)));
        }
        if (getExistingActiveProvision().isSeekerReceived()) {
            return disallowedState(Texts.ofNative("Cannot cancel already received seeking provision (seeking id %s)".formatted(id)));
        }
        return allow();
    }

}
