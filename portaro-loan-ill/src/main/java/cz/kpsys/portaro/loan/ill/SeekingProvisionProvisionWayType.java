package cz.kpsys.portaro.loan.ill;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum SeekingProvisionProvisionWayType implements LabeledIdentified<String> {

    ORIGINAL_EXEMPLAR("original-exemplar", Texts.ofNative("Originální exemplář")),
    PHOTOCOPY("photocopy", Texts.ofNative("Fotokopie"));

    public static final Codebook<SeekingProvisionProvisionWayType, String> CODEBOOK = new StaticCodebook<>(values());

    public static final String SCHEMA_EXAMPLE_ID = "original-exemplar";

    @NonNull String id;
    @NonNull Text text;

}
