package cz.kpsys.portaro.loan.ill.api;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.loan.ill.Seeking;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.With;

@Form(id = "seekingActiveProvisionConditionAcceptCancel")
@FormSubmit(path = SeekingApiController.ACCEPT_ACTIVE_PROVISION_CONDITION_PATH)
@With
public record SeekingActiveProvisionConditionAcceptRequest(

        @Schema(implementation = String.class, example = Seeking.SCHEMA_EXAMPLE_ID)
        @NotNull
        Seeking id

) {}
