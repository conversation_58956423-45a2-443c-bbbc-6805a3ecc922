package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.loan.ill.IllSeekingProvisionList;
import cz.kpsys.portaro.loan.ill.SeekingProvision;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekingProvisionEditor implements Consumer<SeekingProvisionEditationCommand> {

    @NonNull Saver<IllSeekingProvisionList, IllSeekingProvisionList> seekingProvisionListSaver;

    @Transactional
    @Override
    public void accept(@NonNull SeekingProvisionEditationCommand command) throws DataAccessException {
        command.seeking().canEditProvisionData(command.provision()).throwIfCannot();

        SeekingProvision savingProvision = command.provision();

        savingProvision = savingProvision.withDeliveryChannel(command.deliveryChannel());
        savingProvision = savingProvision.withPrice(command.price());

        IllSeekingProvisionList provisionList = command.seeking().getProvisionList();
        provisionList.removeAndSetPendingTo(savingProvision, command.order());

        seekingProvisionListSaver.save(provisionList);
    }

}
