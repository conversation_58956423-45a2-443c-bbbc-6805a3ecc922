package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.user.User;

import java.util.List;

public interface IllSeekingsLoader {

    List<Seeking> getSeekingsByRequester(User lender, RangePaging paging, Department currentDepartment, UserAuthentication currentAuth);

    int getSeekingsByRequesterSize(User lender, Department currentDepartment, UserAuthentication currentAuth);

    List<Seeking> getSeekingsByProvider(User lender, RangePaging paging, Department currentDepartment, UserAuthentication currentAuth);

    int getSeekingsByProviderSize(User lender, Department currentDepartment, UserAuthentication currentAuth);

    List<Seeking> getProvidedSeekingsBySeeker(User seekerUser, RangePaging paging, Department currentDepartment, UserAuthentication currentAuth);

    int getProvidedSeekingsBySeekerSize(User seekerUser, Department currentDepartment, UserAuthentication currentAuth);
    
}
