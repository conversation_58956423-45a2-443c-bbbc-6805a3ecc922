package cz.kpsys.portaro.user.edit.command;

import lombok.With;

import java.util.List;

@With
public record SimpleEditableList<V, MODE>(List<V> list, MODE editationMode) implements EditableList<V, MODE> {

    public static <V, MODE> SimpleEditableList<V, MODE> of(V value, MODE editationMode) {
        return new SimpleEditableList<>(List.of(value), editationMode);
    }

    public static <V, MODE> SimpleEditableList<V, MODE> of(List<V> value, MODE editationMode) {
        return new SimpleEditableList<>(value, editationMode);
    }
}
