package cz.kpsys.portaro.user.category;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.discount.DiscountApprovalState;
import cz.kpsys.portaro.user.discount.UserHasApprovalInProgress;
import cz.kpsys.portaro.user.prop.UserServicePropertyHelper;
import cz.kpsys.portaro.user.registration.AutoExtendRegistrationPeriodCommand;
import cz.kpsys.portaro.user.registration.BeforeGettingRegistrationPeriodListener;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

import static cz.kpsys.portaro.user.discount.DiscountApprovalUserServicePropertiesConstants.APPROVAL_STATE;
import static cz.kpsys.portaro.user.discount.DiscountApprovalUserServicePropertiesConstants.DISCOUNT_SERVICE;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DiscountApprovelInProgressBeforeGettingRegistrationPeriodListener implements BeforeGettingRegistrationPeriodListener {

    @NonNull ContextualProvider<Department, @NonNull Boolean> discountRequestEnabledProvider;
    @NonNull UserServicePropertyHelper userServicePropertyHelper;
    @NonNull UserByBasicUserLoader userLoader;

    @Override
    public void handle(AutoExtendRegistrationPeriodCommand command, AutoExtendRegistrationPeriodCommand extension) {
        User user = userLoader.getUser(command.extendedUser());
        Optional<DiscountApprovalState> discountApprovalState = userServicePropertyHelper.searchForPropValueOfType(user, DISCOUNT_SERVICE, APPROVAL_STATE, DiscountApprovalState.class);

        if (discountRequestEnabledProvider.getOn(command.ctx()) && (discountApprovalState.isPresent() && discountApprovalState.get() == DiscountApprovalState.IN_PROGRESS)) {
            throw new UserHasApprovalInProgress();
        }
    }
}
