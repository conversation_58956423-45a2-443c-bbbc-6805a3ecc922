package cz.kpsys.portaro.user.registration;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.time.ZoneId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RegistrationExtensionHelper {

    @NonNull RegistrationExtensionProcessor registrationExtensionProcessor;
    @NonNull Provider<@NonNull ZoneId> timeZoneProvider;

    public boolean userRegistrationIsExtensible(final User user, @NonNull Department ctx) {
        RegistrationPeriodExtension registrationPeriodExtension = registrationExtensionProcessor.registrationPeriodExtension(user, ctx);
        LocalDate userRegistrationExpiration = user.getReaderAccounts().stream()
                .findFirst()
                .flatMap(ReaderRole::getRegistrationExpirationDate)
                .orElse(null);
        return !LocalDate.ofInstant(registrationPeriodExtension.newPeriod().toDate(), timeZoneProvider.get()).equals(userRegistrationExpiration);
    }
}
