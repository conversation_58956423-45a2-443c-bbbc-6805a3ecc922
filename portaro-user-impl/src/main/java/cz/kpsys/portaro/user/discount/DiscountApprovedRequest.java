package cz.kpsys.portaro.user.discount;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

import java.time.Instant;

public record DiscountApprovedRequest(

        @NotNull
        User user,

        boolean ztp,

        @NotNull
        Instant ztpValidityEndDate,

        boolean student,

        @NotNull
        Instant studentValidityEndDate,

        boolean teacher,

        @NotNull
        Instant teacherValidityEndDate,

        boolean blind,

        @NotNull
        Instant blindValidityEndDate,

        boolean retiree,

        @NotNull
        Instant retireeValidityEndDate
) {
        public DiscountApprovedCommand toCommand(@NonNull Department ctx,
                                                       @NonNull UserAuthentication currentAuth) {
                return new DiscountApprovedCommand(user(), ctx, currentAuth, ztp(), ztpValidityEndDate(), student(), studentValidityEndDate(), teacher(), teacherValidityEndDate(), blind(), blindValidityEndDate(), retiree(), retireeValidityEndDate());
        }

}