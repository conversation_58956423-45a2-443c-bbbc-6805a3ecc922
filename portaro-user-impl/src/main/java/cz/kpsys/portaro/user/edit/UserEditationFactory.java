package cz.kpsys.portaro.user.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.record.deletion.RecordDeleter;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.edit.command.UserSaveCommand;
import cz.kpsys.portaro.user.edit.handler.CompositeAfterSaveHandler;
import cz.kpsys.portaro.user.edit.modifier.CompositeContextualBeforeSaveModifier;
import cz.kpsys.portaro.user.edit.validation.UserValidator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserEditationFactory {

    @NonNull UserEditationCommandConverter userEditationCommandConverter;
    @NonNull UserEditationRequestApplier userEditationRequestApplier;
    @NonNull Saver<UserSaveCommand<?>, User> userSaver;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull Eventer eventer;
    @NonNull PasswordEncoder passwordEncoder;
    @NonNull UserValidator userValidator;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull CompositeContextualBeforeSaveModifier<Department> compositeContextualBeforeSaveModifier;
    @NonNull CompositeAfterSaveHandler compositeAfterSaveHandler;
    @NonNull RecordDeleter recordDeleter;

    public UserEditation<PersonEditationCommand, Person> ofNewPerson(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(Person.createNew(prettyUserNameGenerator), currentDepartment, currentAuth);
    }

    public UserEditation<PersonEditationCommand, Person> ofExistingPerson(@NonNull Person person, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(person, currentDepartment, currentAuth);
    }

    public UserEditation<InstitutionEditationCommand, Institution> ofNewInstitution(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth, @NonNull InstitutionType institutionType) {
        return build(Institution.createNew(prettyUserNameGenerator, institutionType), currentDepartment, currentAuth);
    }

    public UserEditation<SoftwareEditationCommand, Software> ofNewSoftware(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(Software.createNew(), currentDepartment, currentAuth);
    }

    public UserEditation<FamilyEditationCommand, Family> ofNewFamily(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(Family.createNew(prettyUserNameGenerator), currentDepartment, currentAuth);
    }

    public UserEditation<LibraryEditationCommand, Library> ofNewLibrary(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(Library.createNew(prettyUserNameGenerator), currentDepartment, currentAuth);
    }

    public <UEC extends UserEditationCommand, USER extends User> UserEditation<UEC, USER> ofExistingUser(@NonNull USER user, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return build(user, currentDepartment, currentAuth);
    }

    private <UEC extends UserEditationCommand, USER extends User> UserEditation<UEC, USER> build(@NonNull USER user, Department currentDepartment, UserAuthentication currentAuth) {
        return new UserEditation<>(
                user,
                currentDepartment,
                currentAuth,
                eventer,
                userSaver,
                recordHoldingUpserter,
                passwordEncoder,
                userEditationCommandConverter,
                userEditationRequestApplier,
                userValidator,
                compositeContextualBeforeSaveModifier,
                compositeAfterSaveHandler,
                recordDeleter);
    }

}
