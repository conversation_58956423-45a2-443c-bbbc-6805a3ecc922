package cz.kpsys.portaro.user.edit.request;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FamilyEditationRequest extends InstitutionEditationRequest {

    @Override
    public String getKind() {
        return "FAMILY";
    }
}
