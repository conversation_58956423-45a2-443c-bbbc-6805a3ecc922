package cz.kpsys.portaro.user.deletion;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.conversation.FieldEnablingExceptionContinuation;
import cz.kpsys.portaro.exception.AdditionalFieldsException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Map;

import static cz.kpsys.portaro.conversation.ConversationConstants.CONTINUATION_FIELD_NAME;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserIsMemberOfGroupException extends RuntimeException implements SeveritedException, AdditionalFieldsException {

    int severity = SEVERITY_WARNING;

    public UserIsMemberOfGroupException(String message) {
        super(message);
    }

    @Override
    public Map<String, Object> getAdditionalFields() {
        return Map.of(CONTINUATION_FIELD_NAME, new FieldEnablingExceptionContinuation(Texts.ofMessageCoded("user.deletion.ReallyDeleteGroupMember"), UserDeletionRequest.Fields.ignoreUserIsMemberOfGroup));
    }
}