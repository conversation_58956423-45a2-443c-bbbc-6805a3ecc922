package cz.kpsys.portaro.user.edit.validation;

import cz.kpsys.portaro.commons.validation.Validity;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.remotevalidation.DefaultRemoteValidationRequest;
import cz.kpsys.portaro.user.edit.command.UserSaveCommand;
import jakarta.validation.Valid;

public interface UserValidator {

    void validate(@Valid UserSaveCommand<?> userSaveCommand);

    /**
     * Validate user name. simpleValidationDto should contains in static parameters "id" of edited user
     */
    Validity validateUsername(Department department, DefaultRemoteValidationRequest<String> remoteValidationRequest);

    /**
     * Validate RFID. simpleValidationDto should contains in static parameters "id" of edited user
     */
    Validity validateRfid(Department department, DefaultRemoteValidationRequest<String> remoteValidationRequest);

    /**
     * Validate bar code. simpleValidationDto should contains in static parameters "id" of edited user
     */
    Validity validateBarCode(Department department, DefaultRemoteValidationRequest<String> remoteValidationRequest);

    /**
     * Validate card number. simpleValidationDto should contains in static parameters "id" of edited user
     */
    Validity validateCardNumber(Department department, DefaultRemoteValidationRequest<String> remoteValidationRequest);

    /**
     * Validate email. simpleValidationDto should contains in static parameters "id" of edited user
     */
    Validity validateEmail(Department department, DefaultRemoteValidationRequest<String> remoteValidationRequest);
}
