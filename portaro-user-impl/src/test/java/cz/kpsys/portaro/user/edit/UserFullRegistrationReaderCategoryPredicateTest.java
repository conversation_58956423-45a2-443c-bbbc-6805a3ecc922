package cz.kpsys.portaro.user.edit;

import cz.kpsys.portaro.auth.Authenticity;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

import static cz.kpsys.portaro.user.prop.BankIDUserServicePropertiesConstants.BANKID_ID;
import static cz.kpsys.portaro.user.prop.BankIDUserServicePropertiesConstants.BANKID_SERVICE;
import static cz.kpsys.portaro.user.prop.MojeIDUserServicePropertiesConstants.MOJEID_ID;
import static cz.kpsys.portaro.user.prop.MojeIDUserServicePropertiesConstants.MOJEID_SERVICE;

@Tag("ci")
@Tag("unit")
class UserFullRegistrationReaderCategoryPredicateTest {

    /*
        Nový uživatel kterého si vytvořil uživatel sám například pomocí Předregistrace
        by MĚL získat UserFullRegistrationReaderCategory
     */
    @Test
    void newUserShouldReturnTrueOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person user = Person.testingReader(1234);
        CurrentAuth currentAuth = CurrentAuth.createForTesting(user, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertTrue(predicate.test(currentAuth));
    }

    /*
        Nový uživatel kterého vytvořil knihovník by NEMĚl získat UserFullRegistrationReaderCategory
     */
    @Test
    void newUserCreatedByLibrarianShouldReturnFalseOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person librarian = Person.testingLibrarian(1234);
        CurrentAuth currentAuth = CurrentAuth.createForTesting(librarian, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertFalse(predicate.test(currentAuth));
    }

    /*
        Nový uživatel kterého si vytvořil uživatel sám pomocí starého MojeID
        by MĚL získat UserFullRegistrationReaderCategory pokud je MojeID NEVALIDNÍ
     */
    @Test
    void newUserWithNotValidOldMojeIDShouldReturnTrueOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person oldMojeIDUser = Person.testingReader(1234);
        oldMojeIDUser.setMojeIdValid(false);
        oldMojeIDUser.setOpenidId("openid");
        CurrentAuth currentAuth = CurrentAuth.createForTesting(oldMojeIDUser, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertTrue(predicate.test(currentAuth));
        Assertions.assertTrue(MojeIDUserHelper.isOldMojeIdUser(oldMojeIDUser));
        Assertions.assertFalse(MojeIDUserHelper.isValidMojeIDUser(oldMojeIDUser));
    }

    /*
        Nový uživatel kterého si vytvořil uživatel sám pomocí nového MojeID
        by MĚL získat UserFullRegistrationReaderCategory pokud je MojeID NEVALIDNÍ
     */
    @Test
    void newUserWithNotValidNewMojeIDShouldReturnFalseOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person newMojeIDUser = Person.testingReader(1234);
        newMojeIDUser.setMojeIdValid(false);
        newMojeIDUser.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), newMojeIDUser.getId(), MOJEID_SERVICE, MOJEID_ID, "123456")));
        CurrentAuth currentAuth = CurrentAuth.createForTesting(newMojeIDUser, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertTrue(predicate.test(currentAuth));
        Assertions.assertTrue(MojeIDUserHelper.isNewMojeIdUser(newMojeIDUser));
        Assertions.assertFalse(MojeIDUserHelper.isValidMojeIDUser(newMojeIDUser));
    }

    /*
     Nový uživatel kterého si vytvořil uživatel sám pomocí starého MojeID
     by NEMĚL získat UserFullRegistrationReaderCategory pokud je MojeID VALIDNÍ
  */
    @Test
    void newUserWithValidOldMojeIDShouldReturnFalseOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person oldMojeIDUser = Person.testingReader(1234);
        oldMojeIDUser.setMojeIdValid(true);
        oldMojeIDUser.setOpenidId("openid");
        CurrentAuth currentAuth = CurrentAuth.createForTesting(oldMojeIDUser, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertFalse(predicate.test(currentAuth));
        Assertions.assertTrue(MojeIDUserHelper.isOldMojeIdUser(oldMojeIDUser));
        Assertions.assertTrue(MojeIDUserHelper.isValidMojeIDUser(oldMojeIDUser));
    }

    /*
        Nový uživatel kterého si vytvořil uživatel sám pomocí starého MojeID
        by NEMĚL získat UserFullRegistrationReaderCategory pokud je MojeID VALIDNÍ
     */
    @Test
    void newUserWithValidNewMojeIDShouldReturnFalseOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person newMojeIDUser = Person.testingReader(1234);
        newMojeIDUser.setMojeIdValid(true);
        newMojeIDUser.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), newMojeIDUser.getId(), MOJEID_SERVICE, MOJEID_ID, "123456")));
        CurrentAuth currentAuth = CurrentAuth.createForTesting(newMojeIDUser, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertFalse(predicate.test(currentAuth));
        Assertions.assertTrue(MojeIDUserHelper.isNewMojeIdUser(newMojeIDUser));
        Assertions.assertTrue(MojeIDUserHelper.isValidMojeIDUser(newMojeIDUser));
    }

    /*
        Nový uživatel kterého si vytvořil uživatel sám pomocí BankID
        by NEMĚL získat UserFullRegistrationReaderCategory
     */
    @Test
    void newBankIDUserShouldReturnFalseOnUserFullRegistrationReaderCategoryPredicate() {
        UserFullRegistrationReaderCategoryPredicate predicate = new UserFullRegistrationReaderCategoryPredicate();
        Person bankIDUser = Person.testingReader(1234);
        bankIDUser.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), bankIDUser.getId(), BANKID_SERVICE, BANKID_ID, "123456")));
        CurrentAuth currentAuth = CurrentAuth.createForTesting(bankIDUser, Authenticity.OF_NEWLY_REGISTERED);

        Assertions.assertFalse(predicate.test(currentAuth));
        Assertions.assertTrue(BankIDUserHelper.isBankIDUser(bankIDUser));
    }
}