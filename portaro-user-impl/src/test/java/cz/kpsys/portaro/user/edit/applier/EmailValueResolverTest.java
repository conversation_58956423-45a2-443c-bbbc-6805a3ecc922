package cz.kpsys.portaro.user.edit.applier;

import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.edit.command.SourcedEditableList;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

import static cz.kpsys.portaro.user.edit.command.SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE;
import static cz.kpsys.portaro.user.edit.command.SourcedEditableListMode.SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
class EmailValueResolverTest {

    @Test
    void whenUserHaveSameEmailAndSetOverwriteOriginalEmailShouldBeOverwrite() {

        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(1, newContacts.size());
        Contact first = newContacts.getFirst();
        assertEquals("<EMAIL>", first.value());
        assertEquals(SourceOfData.EXTERNAL_BANKID, first.source());
    }

    @Test
    void whenUserHaveSameEmailAndSetSkipOriginalEmailShouldBeSame() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));


        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(1, newContacts.size());
        Contact first = newContacts.getFirst();
        assertEquals("<EMAIL>", first.value());
        assertEquals(SourceOfData.INTERNAL, first.source());
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetOverwriteNewEmailShouldBeAddToOriginalEmails() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(4, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetSkipNewEmailShouldBeAddToOriginalEmails() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(4, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndOneHasSameValueAsNewAndSetOverwriteOriginalEmailShouldBeOverwrite() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(3, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndOneHasSameValueAsNewAndSetSkipOriginalEmailShouldBeSame() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL), Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(3, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfSameSourceAndSetOverwriteAllOriginalEmailShouldBeReplaceByNew() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(1, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfSameSourceAndSetOverwriteAndNewEmailIsEmptyAllEmailShouldBeRemove() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(List.of(), OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.EXTERNAL_BANKID));

        assertEquals(0, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of());
    }

    @Test
    void whenUserHaveMultipleEmailsOfSameSourceAndSetSkipAndNewEmailIsEmptyAllEmailShouldBeRemove() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(List.of(), SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.EXTERNAL_BANKID));

        assertEquals(0, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of());
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetOverwriteOnlyOriginalEmailWithSameSourceAsNewShouldBeReplaceByNew() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        UserEmailEditationCommand userEmailEditationCommand = new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID);

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(3, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }


    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetOverwriteAndNewEmailAreSameAsOriginalNothingShouldChange() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        List<UserEmailEditationCommand> userEmailEditationCommand = List.of(new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.INTERNAL));

        assertEquals(3, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetSkipAndNewEmailAreSameAsOriginalNothingShouldChange() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        List<UserEmailEditationCommand> userEmailEditationCommand = List.of(new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(userEmailEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.INTERNAL));

        assertEquals(3, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetOverwriteAndNewEmailsHaveMoreEmailsThanOriginalAdditionalEmailsShouldBeAdded() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        List<UserEmailEditationCommand> userEmailEditationCommand = List.of(new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(userEmailEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.INTERNAL));

        assertEquals(4, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));
    }

    @Test
    void whenUserHaveMultipleEmailsOfDifferentSourceAndSetSkipAndNewEmailsHaveMoreEmailsThanOriginalAdditionalEmailsShouldBeAdded() {
        Person testing = Person.testing(152);
        testing.setEmails(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID)));

        List<UserEmailEditationCommand> userEmailEditationCommand = List.of(new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                new UserEmailEditationCommand("<EMAIL>", SourceOfData.INTERNAL));

        EmailValueResolver emailValueResolver = new EmailValueResolver();
        List<Contact> newContacts = emailValueResolver.apply(testing, SourcedEditableList.of(userEmailEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.INTERNAL));

        assertEquals(4, newContacts.size());
        assertThat(newContacts).hasSameElementsAs(List.of(Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.EXTERNAL_BANKID),
                Contact.createNewEmail(152, "<EMAIL>", SourceOfData.INTERNAL)));
    }
}