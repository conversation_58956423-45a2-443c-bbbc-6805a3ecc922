package cz.kpsys.portaro.user.category.resolvers;

import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.object.MissingValueException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.TrustedUserAndSourcePredicate;
import cz.kpsys.portaro.user.category.CurrentReaderCategory;
import cz.kpsys.portaro.user.category.NewReaderCategory;
import cz.kpsys.portaro.user.category.ResolvedReaderCategory;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.edit.BankIDUserHelper;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;

import static cz.kpsys.portaro.user.prop.BankIDUserServicePropertiesConstants.BANKID_ID;
import static cz.kpsys.portaro.user.prop.BankIDUserServicePropertiesConstants.BANKID_SERVICE;
import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
class RetireeAutomaticReaderCategoryResolverTest {

    private final TrustedUserAndSourcePredicate trustedUserAndSourcePredicate = new TrustedUserAndSourcePredicate(department -> true);

    @Test
    void shouldReturnNewReaderCategoryWhenAllConditionsAreMet() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(65));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("RETIREE"));
        assertTrue(BankIDUserHelper.isBankIDUser(person));
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnSameReaderCategoryWhenUserIsNotOldEnough() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(64));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertTrue(BankIDUserHelper.isBankIDUser(person));
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnSameReaderCategoryWhenUserDoesNotHaveTrustedSourceOfLifeDate() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                new TrustedUserAndSourcePredicate(_ -> false)
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertTrue(BankIDUserHelper.isBankIDUser(person));
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnNewReaderCategoryWhenUserHaveInternalSourceOfLifeDateAndInternalSourceIsSetAsTrusted() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                new TrustedUserAndSourcePredicate(_ -> true)
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("RETIREE"));
        assertTrue(BankIDUserHelper.isBankIDUser(person));
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnNewReaderCategoryWhenSourceOfDateIsTrusted() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.INTERNAL);

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("RETIREE"));
        assertFalse(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnSameReaderCategoryWhenRetireeAgeLimitIsNotProvided() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> null,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldReturnSameReaderCategoryWhenRetireeAgeLimitIsNotProvidedAndRetireeReaderCategoryIsNull() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> null,
                ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = resolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertTrue(trustedUserAndSourcePredicate.isTrustedUser(person));
    }

    @Test
    void shouldThrowExceptionWhenRetireeAgeLimitIsProvidedButRetireeReaderCategoryIsNull() {
        RetireeAutomaticReaderCategoryResolver resolver = new RetireeAutomaticReaderCategoryResolver(
                _ -> 65,
                ContextIgnoringContextualProvider.<Department, String>ofNull().andThen(ReaderCategory::testing),  // this is done this way to simulate real configuration of this class
                trustedUserAndSourcePredicate
        );

        Person person = Person.testingReader(1234);
        person.setBirthDate(LocalDate.now().minusYears(80));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.setUserServiceProperties(List.of(UserServiceProperty.withoutValidity(UuidGenerator.forIdentifier(), person.getId(), BANKID_SERVICE, BANKID_ID, "123456")));

        Department ctx = Department.testing(14);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        assertThrows(MissingValueException.class, () -> resolver.resolve(ctx, person));
    }
}