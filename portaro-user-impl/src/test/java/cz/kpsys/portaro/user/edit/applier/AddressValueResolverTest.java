package cz.kpsys.portaro.user.edit.applier;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.contact.Address;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.contact.UserAddress;
import cz.kpsys.portaro.user.edit.command.SourcedEditableList;
import cz.kpsys.portaro.user.edit.command.UserAddressEditationCommand;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

import static cz.kpsys.portaro.user.edit.command.SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE;
import static cz.kpsys.portaro.user.edit.command.SourcedEditableListMode.SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("ci")
@Tag("unit")
class AddressValueResolverTest {

    @Test
    void whenUserHaveSameAddressAsNewOriginalEmailShouldBeOverwrite() {
        Person testing = Person.testing(152);
        UserAddress testingAddress = new UserAddress(15745, new Address(79200, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        testing.setAddresses(List.of(testingAddress));

        UserAddressEditationCommand userAddressEditationCommand = new UserAddressEditationCommand(true, true, SourceOfData.EXTERNAL_BANKID, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);

        AddressValueResolver addressValueResolver = new AddressValueResolver();
        List<UserAddress> newAddress = addressValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userAddressEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(1, newAddress.size());
        UserAddress first = newAddress.getFirst();
        assertTrue(first.isProbablySame(testingAddress));
        assertEquals(first.source(), SourceOfData.EXTERNAL_BANKID);
    }

    @Test
    void whenUserHaveSameAddressAsNewAndSetSkipOriginalEmailShouldBeTheSame() {
        Person testing = Person.testing(152);
        UserAddress testingAddress = new UserAddress(15745, new Address(79200, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        testing.setAddresses(List.of(testingAddress));

        UserAddressEditationCommand userAddressEditationCommand = new UserAddressEditationCommand(true, true, SourceOfData.EXTERNAL_BANKID, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);

        AddressValueResolver addressValueResolver = new AddressValueResolver();
        List<UserAddress> newAddress = addressValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userAddressEditationCommand, SKIP_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(1, newAddress.size());
        UserAddress first = newAddress.getFirst();
        assertTrue(first.isProbablySame(testingAddress));
        assertEquals(first.source(), SourceOfData.INTERNAL);
    }

    @Test
    void whenUserHaveMultipleAddressOfDifferentSourceNewAddressShouldBeAddToOriginalAddresses() {
        Person testing = Person.testing(152);
        UserAddress testingAddress = new UserAddress(15745, new Address(79200, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress2 = new UserAddress(15746, new Address(79200, "Císaře Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress3 = new UserAddress(15747, new Address(79200, "Bezdomovce Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        testing.setAddresses(List.of(testingAddress, testingAddress2, testingAddress3));

        UserAddressEditationCommand userAddressEditationCommand = new UserAddressEditationCommand(true, true, SourceOfData.EXTERNAL_BANKID, "Učitele  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);

        AddressValueResolver addressValueResolver = new AddressValueResolver();
        List<UserAddress> newAddress = addressValueResolver.apply(testing, SourcedEditableList.ofSingleSource(userAddressEditationCommand, OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(4, newAddress.size());
    }

    @Test
    void whenUserHaveMultipleAddressesOfDifferentSourceAndNewAddressesAreSameAsOriginalNothingShouldChange() {

        Person testing = Person.testing(152);
        UserAddress testingAddress = new UserAddress(15745, new Address(79200, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress2 = new UserAddress(15746, new Address(79200, "Císaře Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress3 = new UserAddress(15747, new Address(79200, "Bezdomovce Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        testing.setAddresses(List.of(testingAddress, testingAddress2, testingAddress3));

        UserAddressEditationCommand userAddressEditationCommand = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);
        UserAddressEditationCommand userAddressEditationCommand2 = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Císaře Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);
        UserAddressEditationCommand userAddressEditationCommand3 = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Bezdomovce Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);

        AddressValueResolver addressValueResolver = new AddressValueResolver();
        List<UserAddress> newAddress = addressValueResolver.apply(testing,
                SourcedEditableList.ofSingleSource(List.of(userAddressEditationCommand, userAddressEditationCommand2, userAddressEditationCommand3), OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE));

        assertEquals(3, newAddress.size());
        assertTrue(newAddress.stream().allMatch(userAddress -> userAddress.source() == SourceOfData.INTERNAL));
    }

    @Test
    void whenUserHaveMultipleAddressesOfDifferentSourceAndNewAddressesHaveMoreAddressesThanOriginalAdditionalAddressesShouldBeAdded() {

        Person testing = Person.testing(152);
        UserAddress testingAddress = new UserAddress(15745, new Address(79200, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress2 = new UserAddress(15746, new Address(79200, "Císaře Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        UserAddress testingAddress3 = new UserAddress(15747, new Address(79200, "Bezdomovce Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE), true, true, SourceOfData.INTERNAL);
        testing.setAddresses(List.of(testingAddress, testingAddress2, testingAddress3));

        UserAddressEditationCommand userAddressEditationCommand = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Krále  Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);
        UserAddressEditationCommand userAddressEditationCommand2 = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Císaře Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);
        UserAddressEditationCommand userAddressEditationCommand3 = new UserAddressEditationCommand(true, true, SourceOfData.INTERNAL, "Bezdomovce Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);
        UserAddressEditationCommand userAddressEditationCommand4 = new UserAddressEditationCommand(true, true, SourceOfData.EXTERNAL_BANKID, "BankID Jiřího z Poděbrad 842", "Strakonice", "386 01", Country.CZE);

        AddressValueResolver addressValueResolver = new AddressValueResolver();
        List<UserAddress> newAddress = addressValueResolver.apply(testing,
                SourcedEditableList.of(List.of(userAddressEditationCommand, userAddressEditationCommand2, userAddressEditationCommand3, userAddressEditationCommand4), OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, SourceOfData.INTERNAL));

        assertEquals(4, newAddress.size());

    }
}