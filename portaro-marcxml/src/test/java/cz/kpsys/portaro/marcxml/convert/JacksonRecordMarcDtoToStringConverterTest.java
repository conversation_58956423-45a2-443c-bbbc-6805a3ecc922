package cz.kpsys.portaro.marcxml.convert;

import cz.kpsys.portaro.marcxml.model.StrictControlfieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictDatafieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictSubfieldMarcDto;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.xmlunit.matchers.CompareMatcher;

import java.util.List;

@Tag("ci")
@Tag("unit")
class JacksonRecordMarcDtoToStringConverterTest {

    private static final String xmlMarc21Record = """
            <record xmlns="http://www.loc.gov/MARC21/slim">
              <leader>-----nam-a22-----1a-4500</leader>
              <controlfield tag="001">kpm01304368</controlfield>
              <controlfield tag="007">ta</controlfield>
              <datafield tag="022" ind1="#" ind2="#">
                <subfield code="a">80-85955-30-X (váz.)</subfield>
              </datafield>
              <datafield tag="245" ind1="1" ind2="0">
                <subfield code="a">Almanach českých šlechtických a rytířských rodů [2005] /</subfield>
                <subfield code="c">[Milan M. Buben, Karel Vavřínek ; ilustrovali Milan M. Buben a Julie Bubnová-Mühlová]</subfield>
              </datafield>
            </record>
            """;

    @Test
    void shouldConvertToXml() {
        JacksonRecordMarcDtoToStringConverter converter = new JacksonRecordMarcDtoToStringConverter();
        Object actual = converter.convert(createSimpleRecord());
        String expected = xmlMarc21Record;
        MatcherAssert.assertThat(actual, CompareMatcher.isSimilarTo(expected).normalizeWhitespace().ignoreComments());
    }

    private static StrictRecordMarcDto createSimpleRecord() {
        return new StrictRecordMarcDto(
                "-----nam-a22-----1a-4500",
                List.of(
                        new StrictControlfieldMarcDto("001", "kpm01304368"),
                        new StrictControlfieldMarcDto("007", "ta")
                ),
                List.of(
                        new StrictDatafieldMarcDto("022", "#", "#", List.of(
                                StrictSubfieldMarcDto.ofWithoutRecord("a", "80-85955-30-X (váz.)")
                        )),
                        new StrictDatafieldMarcDto("245", "1", "0", List.of(
                                StrictSubfieldMarcDto.ofWithoutRecord("a", "Almanach českých šlechtických a rytířských rodů [2005] /"),
                                StrictSubfieldMarcDto.ofWithoutRecord("c", "[Milan M. Buben, Karel Vavřínek ; ilustrovali Milan M. Buben a Julie Bubnová-Mühlová]")
                        ))
                )
        );
    }
}