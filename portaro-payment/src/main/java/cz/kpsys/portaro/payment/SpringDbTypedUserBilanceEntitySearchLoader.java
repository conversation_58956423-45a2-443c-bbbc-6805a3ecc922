package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import static cz.kpsys.portaro.commons.db.QueryUtils.AS;
import static cz.kpsys.portaro.commons.db.QueryUtils.SUM;
import static cz.kpsys.portaro.databasestructure.TransactionDb.PLATBY.*;
import static cz.kpsys.portaro.payment.DepartmentedUserTypedBilanceEntityRowMapper.SUM_COLUMN;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbTypedUserBilanceEntitySearchLoader
        extends AbstractSpringDbSearchLoader<MapBackedParams, DepartmentedUserTypedBilanceEntity, RangePaging>
        implements PageSearchLoader<MapBackedParams, DepartmentedUserTypedBilanceEntity, RangePaging> {


    @NonNull RowMapper<DepartmentedUserTypedBilanceEntity> rowMapper;

    public SpringDbTypedUserBilanceEntitySearchLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory);
        this.rowMapper = new DepartmentedUserTypedBilanceEntityRowMapper();
    }


    @Override
    protected ResultSetExtractor<Chunk<DepartmentedUserTypedBilanceEntity, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(rowMapper, paging);
    }


    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.select(FK_UZIV_OWNER, FK_POPL, AS(SUM(CASTKA), SUM_COLUMN), TARGET_DEPARTMENT_ID);
    }


    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(PLATBY);

        if (p.hasNotNull(PaymentConstants.SearchParams.OWNER)) {
            if (!p.hasLength(PaymentConstants.SearchParams.OWNER)) {
                return false;
            }
            p.get(PaymentConstants.SearchParams.OWNER).forEach(u -> Assert.state(u.isEvided(), "User is not evided"));
            sq.where().and()
                    .in(FK_UZIV_OWNER, ListUtil.getListOfIds(p.get(PaymentConstants.SearchParams.OWNER)))
                    .and()
                    .isNull(VRACENO);
        }

        if (p.hasNotNull(PaymentConstants.SearchParams.AMOUNT_TYPE)) {
            if (!p.hasLength(PaymentConstants.SearchParams.AMOUNT_TYPE)) {
                return false;
            }
            sq.where().and().in(FK_POPL, ListUtil.getListOfIds(p.get(PaymentConstants.SearchParams.AMOUNT_TYPE)));
        }

        if (p.hasLength(PaymentConstants.SearchParams.EXCLUDED_AMOUNT_TYPE)) {
            sq.where().and().notIn(FK_POPL, ListUtil.getListOfIds(p.get(PaymentConstants.SearchParams.EXCLUDED_AMOUNT_TYPE)));
        }

        if (p.hasNotNull(PaymentConstants.SearchParams.TARGET_DEPARTMENT)) {
            if (!p.hasLength(PaymentConstants.SearchParams.TARGET_DEPARTMENT)) {
                return false;
            }
            sq.where().and().in(TARGET_DEPARTMENT_ID, ListUtil.getListOfIds(p.get(PaymentConstants.SearchParams.TARGET_DEPARTMENT)));
        }

        sq.groupBy(FK_UZIV_OWNER, FK_POPL, TARGET_DEPARTMENT_ID);

        return true;
    }

}
