package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_POLOZKY.*;

@Entity
@Table(name = PLACENI_POLOZKY)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class PaymentItemEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @NonNull
    UUID id;

    @Column(name = FK_PLATBY_PLACENI)
    @NonNull
    Integer paymentId;

    @Column(name = FK_POPL)
    @NonNull
    Integer typeId;

    @Column(name = FK_UZIV_OWNER)
    @NonNull
    Integer ownerId;

    @Column(name = CASTKA)
    @NonNull
    BigDecimal sum;
}
