package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CorrectedProviderPayCommand extends ProxyPayCommand {

    @NonNull String correctedProvider;

    public CorrectedProviderPayCommand(@NonNull PayCommand target, @NonNull String correctedProvider) {
        super(target);
        this.correctedProvider = Objects.requireNonNull(StringUtil.notBlankTrimmedString(correctedProvider));
    }

    @NonNull
    @Override
    public String provider() {
        return correctedProvider;
    }
}
