dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+") // added because of lombok-related compilation error "error: cannot access MappedSuperclass"

}
