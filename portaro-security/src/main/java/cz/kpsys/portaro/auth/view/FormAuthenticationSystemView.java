package cz.kpsys.portaro.auth.view;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FormAuthenticationSystemView implements AuthenticationSystemView {

    @NonNull String name;
    @NonFinal @NonNull Boolean secondFieldEnabled = true;
    @NonFinal @NonNull Boolean showingInMainLogin = true;
    @NonFinal @NonNull Boolean forAdministrationOnly = false;

    public FormAuthenticationSystemView notShowInMainLogin() {
        this.showingInMainLogin = false;
        return this;
    }

    public FormAuthenticationSystemView withShowInMainLogin(@NonNull Boolean showingInMainLoginProvider) {
        this.showingInMainLogin = showingInMainLoginProvider;
        return this;
    }

    public FormAuthenticationSystemView withForAdministrationOnlyProvider(@NonNull Boolean forAdministrationOnly) {
        this.forAdministrationOnly = forAdministrationOnly;
        return this;
    }

    public FormAuthenticationSystemView withFirstFieldOnly(@NonNull Boolean firstFieldOnly) {
        this.secondFieldEnabled = !firstFieldOnly;
        return this;
    }

    @Override
    public String getId() {
        return name;
    }

    @Override
    public boolean isSecondFieldEnabled() {
        return secondFieldEnabled;
    }

    @Override
    public boolean isShowingInMainLogin() {
        return showingInMainLogin;
    }

    @Override
    public boolean isForAdministrationOnly() {
        return forAdministrationOnly;
    }
}
