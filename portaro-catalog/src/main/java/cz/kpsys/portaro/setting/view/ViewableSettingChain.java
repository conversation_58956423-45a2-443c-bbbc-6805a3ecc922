package cz.kpsys.portaro.setting.view;

import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.setting.SettingTypeDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Getter
public class ViewableSettingChain {

    @NonNull SettingTypeDto<Object> type;
    @NonNull List<ViewableSettingValue> chain;
    Object effectiveSettingValue;
    @NonNull ValueEditor<?, ?, ?> editor;

    public String getId() {
        return type.getId().getValue();
    }

    public Object getDefaultValue() {
        return type.getValue();
    }

    public String getName() {
        return type.getId().getName();
    }

    public String getSection() {
        return type.getId().getSection();
    }

    public String getDescription() {
        return type.getDescription();
    }

    public Datatype getDatatype() {
        return type.getDatatype();
    }
}
