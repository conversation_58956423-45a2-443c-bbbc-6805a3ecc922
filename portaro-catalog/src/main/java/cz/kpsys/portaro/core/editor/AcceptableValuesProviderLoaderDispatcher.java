package cz.kpsys.portaro.core.editor;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.LabeledValuesGroup;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.Optional;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;

/**
 * Podporuje jak klasicke datove typy (FOND_DOK, LOKACE, STATUS_DOK, ..), tak pic (VAL64K, ISBN apod.)
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AcceptableValuesProviderLoaderDispatcher implements ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> {

    @NonNull DatatypedAcceptableValuesRegistry registry;
    @NonNull ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> defValAcceptableValuesGroupLoader;
    @NonNull ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> iniAcceptableValuesGroupLoader;

    @Override
    public @Nullable AllValuesProvider<? extends LabeledIdentified<?>> getById(@NonNull ScalarDatatype scalarDatatype) {

        Optional<AllValuesProvider<? extends LabeledIdentified<?>>> foundProvider = registry.getProvider(scalarDatatype);
        if (foundProvider.isPresent()) {
            return foundProvider.get();
        }

        // VALxxx = validace z validačního slovníku, bez možnosti zadání hodnoty která v něm není
        Optional<String> val = scalarDatatype.unprefixedName(DATATYPE_PREFIX_VAL);
        if (val.isPresent()) {
            return defValAcceptableValuesGroupLoader.getById(val.get());
        }

        // EDVxxx = validace z validačního slovníku, s možnosti zadání libovolné hodnoty
        Optional<String> edVal = scalarDatatype.unprefixedName(DATATYPE_PREFIX_EDV);
        if (edVal.isPresent()) {
            return defValAcceptableValuesGroupLoader.getById(edVal.get());
        }

        // EXTxxxx = validace z externího slovníku, v ini musí být definována stejnojmenná sekce
        Optional<String> ext = scalarDatatype.unprefixedName("EXT");
        if (ext.isPresent()) {
            // nepodporujeme
        }

        //INIVALxxx = vyber z INI_VAL
        Optional<String> iniGroupIdString = scalarDatatype.unprefixedName(DATATYPE_PREFIX_INIVAL);
        if (iniGroupIdString.isPresent()) {
            int iniGroupId = Integer.parseInt(iniGroupIdString.get());
            return iniAcceptableValuesGroupLoader.getById(iniGroupId);
        }

        return null;
    }

}
