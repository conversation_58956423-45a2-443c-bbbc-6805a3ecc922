package cz.kpsys.portaro.centralindex;

import cz.kpsys.portaro.loan.availability.PlainAvailability;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.client.RestOperations;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ForeignDocumentAvailabilityServiceImpl implements ForeignDocumentAvailabilityService {

    @NonNull RestOperations rest;

    @Override
    public PlainAvailability getDocumentAvailability(@NonNull ForeignOccurrence foreignOccurrence) {
        Optional<String> verbisAvailabilityUrl = foreignOccurrence.verbisAvailabilityUrl();
        if (verbisAvailabilityUrl.isEmpty()) {
            return ForeignDocumentAvailability.unknown();
        }
        return rest.getForObject(verbisAvailabilityUrl.get(), ForeignDocumentAvailability.class);
    }

}
