package cz.kpsys.portaro.stats.model;

import lombok.Value;

@Value
public class ZClientStatisticsItem {
    
    int year;
    int month;
    String serverConnectionString;
    int searchCount;
    int successSearchCount;
    int usedRecordsCount;

    public int getSuccessSearchPercentageCount() {
        return (int) Math.round(100 * successSearchCount / (double)searchCount);
    }
    
    public int getUsedRecordsPercentageCount() {
        return (int) Math.round(100 * usedRecordsCount / (double)searchCount);
    }

}
