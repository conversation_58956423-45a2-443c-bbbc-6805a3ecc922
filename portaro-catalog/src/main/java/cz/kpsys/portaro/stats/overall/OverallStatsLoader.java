package cz.kpsys.portaro.stats.overall;

import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.stats.model.MultipleCountsStat;

import java.util.Map;

public interface OverallStatsLoader {

    Map<String, Integer> get(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarIncreasesByAcqWaysStats(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarIncreasesByFondsStats(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarIncreasesByThematicGroupStats(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarDecreasesByAcqWaysStats(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarDecreasesByFondsStats(MapBackedParams p);

    MultipleCountsStat<Long> getExemplarDecreasesByThematicGroupStats(MapBackedParams p);
}
