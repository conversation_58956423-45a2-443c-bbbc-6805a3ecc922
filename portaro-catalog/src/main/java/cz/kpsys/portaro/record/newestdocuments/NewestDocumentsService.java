package cz.kpsys.portaro.record.newestdocuments;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadableByIdLoaderAdapter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class NewestDocumentsService implements CacheCleaner {

    @NonNull AllByIdsLoadable<Record, UUID> recordLoader;
    @NonNull PageSearchLoader<MapBackedParams, UUID, RangePaging> newestDocumentsLoader;
    @NonNull ContextualProvider<Department, @NonNull Boolean> recordsWithCoverPreferringEnabledProvider;
    @NonNull ContextualProvider<Department, @NonNull Integer> presearchedRecordsCountProvider;
    @NonNull ContextualProvider<Department, @NonNull Integer> shownRecordsCountProvider;
    @NonNull ContextualProvider<Department, @NonNull Boolean> includePeriodicalFondsEnabledProvider;
    @NonNull ContextualProvider<Department, @NonNull List<Fond>> showableFondsDepartmentedProvider;
    @NonNull List<UUID> forbiddenRecordIds;
    @NonNull List<Integer> forbiddenRecordStatuses;
    @NonNull List<Integer> exemplarStatusesOfNews;
    @NonNull ByIdLoadable<RecordStatus, Integer> recordStatusLoader;
    @NonNull ByIdLoadable<ExemplarStatus, Integer> exemplarStatusLoader;
    @NonNull ContextualProvider<Department, List<Integer>> allowedDepartmentsProvider;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonFinal volatile Cache<Department, List<Record>> newsOnBuildingsCache;

    @SneakyThrows
    public List<Record> getAll(@NonNull final Department ctx) {
        if (newsOnBuildingsCache == null) {
            newsOnBuildingsCache = CacheBuilder.newBuilder()
                    .expireAfterWrite(4, TimeUnit.HOURS)
                    .maximumSize(500)
                    .build();
        }
        return newsOnBuildingsCache.get(ctx, () -> load(ctx));
    }

    private List<Record> load(Department ctx) {
        int pageSize = createPageSize(ctx);
        if (pageSize <= 0) {
            return List.of();
        }

        MapBackedParams params = createParams(ctx);

        List<UUID> nejnovejsiDokumentyId = newestDocumentsLoader.getPage(RangePaging.forFirstPage(pageSize), params).getItems();

        List<Record> nejnovejsiDokumenty = new ArrayList<>(recordLoader.getAllByIds(nejnovejsiDokumentyId));


        //pokud je v ini nastaveno, ze mame zobrazovat jen dokumenty ktere maji obal, z techto zaznamu vyber jen prvnich x, ktere maji obal
        if (recordsWithCoverPreferringEnabledProvider.getOn(ctx)) {
            List<Record> recordsWithCover = new ArrayList<>();
            List<Record> recordsWithoutCover = new ArrayList<>();

            //do seznamu nejnovejsiDokumentySObalem budem postupne pridavat dokumenty s obalem, do nejnovejsiDokumentyBezObalu ty bez obalu
            for (Record nejnovejsiDokument : nejnovejsiDokumenty) {
                if (nejnovejsiDokument.getCover() != null) {
                    recordsWithCover.add(nejnovejsiDokument);
                } else {
                    recordsWithoutCover.add(nejnovejsiDokument);
                }
            }

            //spojime seznamy (-> na zacatku budou nejnovejsiDokumentySObalem, na konci nejnovejsiDokumentyBezObalu)
            recordsWithCover.addAll(recordsWithoutCover);

            //nakonec odsekneme na getPocetZobrazovanychNovinek. zjistime tedy index, ve kterem se ma seznam odseknout. pokud je getPocetZobrazovanychNovinek() vetsi nez velikost seznamu, zobrazime tolik, kolik jich v tom seznamu je
            int toIndex = shownRecordsCountProvider.getOn(ctx) >= recordsWithCover.size() ? recordsWithCover.size() : shownRecordsCountProvider.getOn(ctx);
            nejnovejsiDokumenty = recordsWithCover.subList(0, toIndex);
        }

        return nejnovejsiDokumenty;
    }

    private MapBackedParams createParams(Department ctx) {
        return MapBackedParams.build(p -> {
            p.set(ExemplarConstants.SearchParams.EXEMPLAR_STATUS, new AllByIdsLoadableByIdLoaderAdapter<>(exemplarStatusLoader).getAllByIds(exemplarStatusesOfNews));
            p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, ListUtil.convert(forbiddenRecordStatuses, new IdToObjectConverter<>(recordStatusLoader)));
            p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, forbiddenRecordIds);
            p.set(RecordConstants.SearchParams.ROOT_FOND, getSearchedFonds(ctx));

            List<Department> departments = contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE);
            if (ListUtil.hasLength(allowedDepartmentsProvider.getOn(ctx))) {
                departments = departmentAccessor.getRootFilteredDepartments(ctx, ListUtil.convert(allowedDepartmentsProvider.getOn(ctx), departmentLoader::getById));
            }
            p.set(CoreSearchParams.DEPARTMENT, departments);
        });
    }

    private List<Fond> getSearchedFonds(Department ctx) {
        List<Fond> showableFonds = Fond.filterDocumentFonds(showableFondsDepartmentedProvider.getOn(ctx));
        if (!includePeriodicalFondsEnabledProvider.getOn(ctx)) {
            showableFonds = Fond.filterNonperiodical(showableFonds);
        }
        return showableFonds;
    }

    private int createPageSize(Department ctx) {
        if (recordsWithCoverPreferringEnabledProvider.getOn(ctx)) {
            //pokud je v ini nastaveno, ze mame zobrazovat jen dokumenty ktere maji obal, nastav pocet predvyhledanych zaznamu na hodnotu z ini
            return presearchedRecordsCountProvider.getOn(ctx);
        }
        //jinak normalni pocet zobrazovanych novinek
        return shownRecordsCountProvider.getOn(ctx);
    }


    @Override
    public void clearCache() {
        if (newsOnBuildingsCache != null) {
            newsOnBuildingsCache.invalidateAll();
        }
    }
}
