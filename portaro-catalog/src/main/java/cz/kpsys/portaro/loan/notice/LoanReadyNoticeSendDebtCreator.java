package cz.kpsys.portaro.loan.notice;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.finance.*;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.ReaderCatLoanCatSettingLoader;
import cz.kpsys.portaro.loan.UserCatLoanCatSetting;
import cz.kpsys.portaro.payment.TransactionCreateCommand;
import cz.kpsys.portaro.payment.TransactionCreator;
import cz.kpsys.portaro.payment.UserTypedBilance;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * Trida ukladajici dluh po odeslani informace o pripravene rezervaci nebo objednavce
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LoanReadyNoticeSendDebtCreator {

    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull ReaderCatLoanCatSettingLoader readerCatLoanCatSettingLoader;
    @NonNull AmountTypeLoader amountTypeLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull AuthenticatedContextualProvider<Department, List<Department>> editableDepartmentsAuthenticatedContextualProvider;
    @NonNull Provider<@NonNull Currency> defaultCurrencyProvider;
    @NonNull TransactionCreator transactionCreator;

    public DebtCreationResult addDebt(@NonNull LoanReadyNotice notice) {
        return readwriteTransactionTemplate.execute(_ -> addDebtInTransaction(notice));
    }

    private @NonNull DebtCreationResult addDebtInTransaction(LoanReadyNotice notice) {
        User waitingUser = notice.waitingUser();
        ReaderCategory rc = waitingUser.roleStreamOn(ReaderRole.class, notice.ctx()).map(ReaderRole::getReaderCategory).findFirst().orElseThrow();
        LoanCategory lc = notice.waitingLoan().getExemplar().getLoanCategory();
        UserCatLoanCatSetting setting = readerCatLoanCatSettingLoader.getByUserCatAndLoanCat(rc, lc);

        Double fee;
        if (LoanState.UNPROCESSED_ORDER.equals(notice.waitingLoan().getState())) {
            fee = setting.getOrderingFee();
        } else if (LoanState.UNSENT_RESERVATION.equals(notice.waitingLoan().getState())) {
            fee = setting.getReservationFee();
        } else {
            throw new IllegalStateException("Illegal state of loan: %s".formatted(notice.waitingLoan().getState()));
        }

        if (fee > 0) {
            addReservationFeeDebt(waitingUser, fee, notice.currentAuth(), notice.ctx());
        }

        return new DebtCreationResult(waitingUser, new Price(BigDecimal.valueOf(fee), defaultCurrencyProvider.get()));
    }

    private void addReservationFeeDebt(BasicUser user, double fee, @NonNull UserAuthentication currentAuth, Department ctx) {
        List<AmountType> allTypesOfReservationCategory = amountTypeLoader.getAllByCategory(AmountTypeCategory.RESERVATION_ID);
        Assert.notEmpty(allTypesOfReservationCategory, "Neexistuje žádný typ poplatku pro rezervace!");
        AmountType reservationType = allTypesOfReservationCategory.getFirst();
        Department department = departmentAccessor.getMostTopDepartment(editableDepartmentsAuthenticatedContextualProvider.getOn(currentAuth, ctx));

        TransactionCreateCommand transactionCreateCommand = new TransactionCreateCommand(
                department,
                new UserTypedBilance(user, BigDecimal.valueOf(fee).negate(), reservationType),
                Instant.now(),
                currentAuth.getActiveUser(),
                department,
                null
        );

        transactionCreator.createWithoutSecurityCheck(List.of(transactionCreateCommand), department);
    }

    public record DebtCreationResult(
            BasicUser user,
            Price debt
    ) {}

}
