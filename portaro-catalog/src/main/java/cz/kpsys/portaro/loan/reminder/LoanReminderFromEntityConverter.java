package cz.kpsys.portaro.loan.reminder;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Map;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class LoanReminderFromEntityConverter implements Converter<List<? extends LoanReminderEntity>, List<LoanReminder>> {

    @NonNull AllByIdsLoadable<Loan, LoanId> loanLoader;

    @Override
    public List<LoanReminder> convert(@NonNull List<? extends LoanReminderEntity> entities) {
        Map<? extends LoanReminderEntity, @NonNull Loan> entityToLoanMap = BatchFiller.of(loanLoader).load(entities, entity -> LoanId.ofActive(entity.getId()));

        return ListUtil.convert(entities, entity -> new LoanReminder(
                entity.getId(),
                LoanReminderType.CODEBOOK.getById(entity.getNumber()),
                entityToLoanMap.get(entity),
                entity.getGenerationDate(),
                entity.getSentDate()
        ));
    }
}
