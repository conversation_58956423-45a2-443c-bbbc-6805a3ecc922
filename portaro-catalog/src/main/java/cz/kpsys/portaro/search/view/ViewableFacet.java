package cz.kpsys.portaro.search.view;

import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.form.editor.ValueEditorProvider;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.search.lucene.Facet;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ViewableFacet extends LabeledId<String> implements ValueEditorProvider, Serializable {

    @NonNull ValueEditor<?, ?, ?> valueEditor;

    public ViewableFacet(@NonNull Facet facet, @NonNull ValueEditor<?, ?, ?> valueEditor) {
        super(facet.getId(), facet.getText());
        this.valueEditor = valueEditor;
    }

    @Override
    public ValueEditor<?, ?, ?> getEditor() {
        return valueEditor;
    }

    @Override
    public String toString() {
        return "ViewableFacet:[id=" + getId() + " editor=" + valueEditor + "]";
    }
}