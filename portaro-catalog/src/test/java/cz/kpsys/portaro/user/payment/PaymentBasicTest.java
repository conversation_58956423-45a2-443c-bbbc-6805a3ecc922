package cz.kpsys.portaro.user.payment;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.AmountGroup;
import cz.kpsys.portaro.payment.PaymentBasic;
import cz.kpsys.portaro.payment.PaymentState;
import cz.kpsys.portaro.user.Person;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;

@Tag("ci")
@Tag("unit")
public class PaymentBasicTest {

    private static PaymentBasic createDefaultPayment() {
        return new PaymentBasic(
                "test",
                Person.testing(123),
                Person.testing(123),
                Department.testingRoot(),
                AmountGroup.empty(),
                Instant.now()
        );
    }

    @Test
    public void stateShouldBeCreatedAfterObjectCreation() {
        PaymentBasic p = createDefaultPayment();
        Assertions.assertEquals(PaymentState.CREATED, p.getState());
    }


    @Test
    public void settingPayDateShouldResultAsPaidState() {
        PaymentBasic p = createDefaultPayment();

        p.setPayDate(Instant.now());

        Assertions.assertEquals(PaymentState.PAID, p.getState());
    }


    @Test
    public void settingRefundDateOnNonPaidPaymentShouldThrow() {
        PaymentBasic p = createDefaultPayment();

        Assertions.assertThrows(IllegalStateException.class, () -> p.setNewState(PaymentState.REFUNDED));
    }


    @Test
    public void setNewPrecendentStateShouldThrow() {
        PaymentBasic p = createDefaultPayment();

        p.setPayDate(Instant.now());
        //stav je ted PAID

        Assertions.assertThrows(IllegalStateException.class, () -> p.setNewState(PaymentState.CANCELED));
    }


    @Test
    public void settingPayAndDateRefundStateShouldResultAsRefundState() {
        PaymentBasic p = createDefaultPayment();

        p.setPayDate(Instant.now());
        p.setNewState(PaymentState.REFUNDED);

        Assertions.assertNotNull(p.getRefundDate());
        Assertions.assertEquals(PaymentState.REFUNDED, p.getState());
    }

}