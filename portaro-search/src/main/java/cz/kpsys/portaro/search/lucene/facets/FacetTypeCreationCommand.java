package cz.kpsys.portaro.search.lucene.facets;

import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;
import org.springframework.lang.Nullable;

public record FacetTypeCreationCommand(
    @NonNull String name,
    @NonNull Integer order,
    @Nullable String definition,
    @NonNull FacetDefinitionType definitionType,
    @NonNull Boolean exemplarType,
    @NonNull Boolean enabled,
    @NonNull SortingItem sorting,
    @NonNull FacetScope scope,
    @Nullable String datatype
){}
