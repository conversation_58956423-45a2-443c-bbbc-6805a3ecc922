package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ResultConvertingPageSearchLoader<PARAMS extends SearchParams, INTERMEDIATE, TARGET, PAGING extends Paging> implements PageSearchLoader<PARAMS, TARGET, PAGING> {

    @NonNull PageSearchLoader<PARAMS, INTERMEDIATE, PAGING> pureLoader;
    @NonNull Converter<List<INTERMEDIATE>, List<? extends TARGET>> postConverter;

    public static <PARAMS extends SearchParams, ID, TARGET extends Identified<ID>, PAGING extends Paging> ResultConvertingPageSearchLoader<PARAMS, ID, TARGET, PAGING> createConvertingFromIds(@NonNull PageSearchLoader<PARAMS, ID, PAGING> pureLoader,
                                                                                                                                                                @NonNull AllByIdsLoadable<TARGET, ID> objectsByIdsLoader) {
        return new ResultConvertingPageSearchLoader<>(pureLoader, objectsByIdsLoader::getAllByIds);
    }

    @Override
    public Chunk<TARGET, PAGING> getPage(PAGING paging, SortingItem customSorting, PARAMS params) {
        Chunk<INTERMEDIATE, PAGING> originalContent = pureLoader.getPage(paging, customSorting, params);
        List<? extends TARGET> convertedContent = Objects.requireNonNull(postConverter.convert(originalContent.getItems()));

        Assert.state(originalContent.size() == convertedContent.size(), () -> "Size of converted search items (%s) is not same as original (%s)".formatted(convertedContent.size(), originalContent.size()));

        return originalContent.withItems((List<TARGET>) convertedContent);
    }

    @Override
    public int getTotalElements(PARAMS params) {
        return pureLoader.getTotalElements(params);
    }

}
