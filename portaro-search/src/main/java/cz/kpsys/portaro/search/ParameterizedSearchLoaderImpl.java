package cz.kpsys.portaro.search;

import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ParameterizedSearchLoaderImpl<P extends SearchParams, ITEM> implements ParameterizedSearchLoader<P, ITEM> {

    @NonNull Supplier<P> newParamsCreator;
    @NonNull PageSearchLoader<P, ITEM, RangePaging> delegate;

    @Override
    public List<ITEM> getContent(RangePaging paging, SortingItem customSorting, Consumer<P> paramsModifier) {
        P p = newParamsCreator.get();
        paramsModifier.accept(p);
        return getPage(paging, customSorting, p).getItems();
    }

    @Override
    public int getTotalElements(Consumer<P> paramsModifier) {
        P p = newParamsCreator.get();
        paramsModifier.accept(p);
        return getTotalElements(p);
    }

    @Override
    public Chunk<ITEM, RangePaging> getPage(RangePaging paging, SortingItem customSorting, P params) {
        return delegate.getPage(paging, customSorting, params);
    }

    @Override
    public int getTotalElements(P params) {
        return delegate.getTotalElements(params);
    }
}
