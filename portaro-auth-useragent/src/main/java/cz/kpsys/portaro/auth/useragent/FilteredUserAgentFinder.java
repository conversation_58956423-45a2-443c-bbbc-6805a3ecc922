package cz.kpsys.portaro.auth.useragent;

import cz.kpsys.portaro.auth.process.TokenFinder;
import cz.kpsys.portaro.matcher.Matcher;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FilteredUserAgentFinder implements TokenFinder<String> {

    @NonNull TokenFinder<String> delegate;
    @NonNull Matcher<String> anyUserAgentMatcher;

    @Override
    public Optional<String> getTokenFromRequest(HttpServletRequest request) {
        return delegate.getTokenFromRequest(request)
                .filter(anyUserAgentMatcher::matches);
    }
}
