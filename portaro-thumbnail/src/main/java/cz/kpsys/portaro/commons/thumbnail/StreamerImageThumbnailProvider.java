package cz.kpsys.portaro.commons.thumbnail;

import cz.kpsys.portaro.commons.image.ImageData;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.io.StreamInfo;
import cz.kpsys.portaro.file.IdentifiedFile;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.io.InputStream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StreamerImageThumbnailProvider implements ThumbnailProvider {

    @NonNull FileDataStreamer fileDataStreamer;

    @Override
    public Thumbnail getThumbnail(IdentifiedFile file) {
        ThumbnailStreamConsumer streamConsumer = new ThumbnailStreamConsumer();
        fileDataStreamer.streamData(file.getId(), null, streamConsumer);
        return streamConsumer.thumbnail;
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ThumbnailStreamConsumer implements FileStreamConsumer {

        @NonFinal Thumbnail thumbnail;

        @Override
        public void consume(@NonNull StreamInfo info, @NonNull InputStream inputStream) {
            Long size = info.size();
            ImageData image = size != null
                    ? ImageData.createFromInputStream(inputStream, size)
                    : ImageData.createFromInputStream(inputStream);
            thumbnail = Thumbnail.ofCustomName(image, info.filename());
        }
    }
}
