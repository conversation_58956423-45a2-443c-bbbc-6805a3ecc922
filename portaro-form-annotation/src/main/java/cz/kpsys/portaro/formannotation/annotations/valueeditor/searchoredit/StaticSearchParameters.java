package cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface StaticSearchParameters {

    String kind() default "";

    String subkind() default "";

    String type() default "";

    String datasource() default "";

    String datasourceGroup() default "";

    String fondType() default "";
}

