package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.validation.Validity;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.remotevalidation.DefaultRemoteValidationRequest;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.user.PhoneNumberEditationCommand;
import cz.kpsys.portaro.user.UserApiConstants;
import cz.kpsys.portaro.user.edit.validation.UserValidator;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Tag(name = "user-validation", description = "Endpoints for validation information related to user.")
@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserValidationApiController extends GenericApiController {

    @NonNull UserValidator userValidator;
    @NonNull PhoneNumberValidator phoneNumberValidator;

    @PostMapping(UserApiConstants.UNIQUE_USERNAME_VALIDATION_URL)
    public Validity isUsernameUnique(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<String> remoteValidationRequest,
                                     @CurrentDepartment Department ctx) {
        return userValidator.validateUsername(ctx, remoteValidationRequest);
    }

    @PostMapping(UserApiConstants.UNIQUE_RFID_VALIDATION_URL)
    public Validity isRfidUnique(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<String> remoteValidationRequest,
                                 @CurrentDepartment Department ctx) {
        return userValidator.validateRfid(ctx, remoteValidationRequest);
    }

    @PostMapping(UserApiConstants.UNIQUE_BAR_CODE_VALIDATION_URL)
    public Validity isBarCodeUnique(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<String> remoteValidationRequest,
                                    @CurrentDepartment Department ctx) {
        return userValidator.validateBarCode(ctx, remoteValidationRequest);
    }

    @PostMapping(UserApiConstants.UNIQUE_CARD_NUMBER_VALIDATION_URL)
    public Validity isCardNumberUnique(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<String> remoteValidationRequest,
                                       @CurrentDepartment Department ctx) {
        return userValidator.validateCardNumber(ctx, remoteValidationRequest);
    }

    @PostMapping(UserApiConstants.UNIQUE_EMAIL_VALIDATION_URL)
    public Validity isEmailUnique(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<String> remoteValidationRequest,
                                  @CurrentDepartment Department ctx) {
        return userValidator.validateEmail(ctx, remoteValidationRequest);
    }

    @PostMapping(UserApiConstants.TELEPHONE_NUMBER_VALIDATION_URL)
    public Validity isTelephoneNumberValid(@ValidFormObject @RequestBody DefaultRemoteValidationRequest<PhoneNumberEditationCommand> remoteValidationRequest) {
        if (remoteValidationRequest.value() == null || !remoteValidationRequest.value().smsCapable()) {
            return new Validity(
                    remoteValidationRequest.fieldName(),
                    Validity.TYPE_FORMAT,
                    true,
                    Texts.ofMessageCoded("validation.WrongFormat")
            );
        }

        return phoneNumberValidator.validate(
                remoteValidationRequest.fieldName(),
                remoteValidationRequest.value().value()
        );
    }
}