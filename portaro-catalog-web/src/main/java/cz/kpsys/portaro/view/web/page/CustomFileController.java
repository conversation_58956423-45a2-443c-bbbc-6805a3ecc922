package cz.kpsys.portaro.view.web.page;

import cz.kpsys.portaro.commons.web.DownloadFileStreamConsumer;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.custom.CustomFile;
import cz.kpsys.portaro.view.domain.CurrentPage;
import cz.kpsys.portaro.web.GenericController;
import cz.kpsys.portaro.web.page.CurrentPageFactory;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;

import java.util.List;
import java.util.Locale;

@RequestMapping
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomFileController extends GenericController {

    private static final List<String> TEMPLATEABLE_EXTENSIONS = List.of("html", "vtl", "vm");

    @NonNull ByIdLoadable<CustomFile, String> hierarchyTraversingCustomFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull CurrentPageFactory<CurrentPage> currentPageFactory;
    @NonNull LocaleResolver localeResolver;

    @GetMapping("/custom/{directoryName}/{filenameWithoutExt}.{extension}")
    @ResponseBody
    public String getFile(@PathVariable("directoryName") String directoryName,
                          @PathVariable("filenameWithoutExt") String filenameWithoutExt,
                          @PathVariable("extension") String extension,
                          @RequestParam(value = "raw", required = false, defaultValue = "false") boolean skipTemplateRendering,
                          @CurrentDepartment Department currentDepartment,
                          HttpServletRequest request,
                          HttpServletResponse response) {

        if (TEMPLATEABLE_EXTENSIONS.contains(extension) && !skipTemplateRendering) {
            Locale locale = localeResolver.resolveLocale(request);
            return currentPageFactory.create(currentDepartment, locale, request)
                    .renderCustomFile(directoryName, filenameWithoutExt, extension);
        }

        String customFileId = CustomFile.createId(currentDepartment.getId(), directoryName, filenameWithoutExt + "." + extension);
        CustomFile customFile = hierarchyTraversingCustomFileLoader.getById(customFileId);

        fileDataStreamer.streamData(customFile.getFileId(), null, new DownloadFileStreamConsumer(response));
        return null;
    }

}
