package cz.kpsys.portaro.view.web.page;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoansProviderService;
import cz.kpsys.portaro.loan.export.LoanExportDto;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.web.GenericPageController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


@RequestMapping("users")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LoanConfirmationPrintPageController extends GenericPageController {

    @NonNull TemplateEngine templateEngine;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;
    @NonNull LoansProviderService loansProviderService;
    @NonNull Converter<List<? extends Loan>, List<LoanExportDto>> loansToLoanExportDtosConverter;


    @RequestMapping("{id}/print-loan-confirmation")
    public ModelAndView printLoanConfirmation(@PathVariable("id") User user,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth,
                                              Locale locale,
                                              HttpServletRequest request) {
        var loans = loansProviderService.getActiveLoans(user, Range.forAll(), ctx, currentAuth);
        var loanExportDtos = loansToLoanExportDtosConverter.convert(loans);

        Map<String, Object> velModel = new HashMap<>();
        velModel.put("loans", loanExportDtos);
        String pageContent = templateEngine.build(LoanConstants.Template.TEMPLATE_LOAN_CONFIRMATION_PRINT, currentAuth, ctx, velModel, locale);

        Map<String, Object> model = Map.of(
                "pageTitle", Texts.ofMessageCoded("user.LoanConfirmation"),
                "content", pageContent
        );
        return modelAndPageViewFactory.pageView("model-content-print", ctx, request, model, List.of());
    }
}
