package cz.kpsys.portaro.view.web.rest.permission;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.security.permission.UserPermissionEntity;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;

@RequestMapping("/api/user-permissions")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserPermissionApiController extends GenericApiController {

    @NonNull AllValuesProvider<UserPermissionEntity> userPermissionEntityLoader;

    @GetMapping
    public List<UserPermissionEntity> getAll(@RequestParam("user") Optional<BasicUser> user,
                                             @RequestParam("action") Optional<Integer> actionId) {
        List<UserPermissionEntity> result = userPermissionEntityLoader.getAll();
        if (user.isPresent()) {
            result = ListUtil.filter(result, userPermissionDto -> userPermissionDto.getUserId().equals(user.get().getId()));
        }
        if (actionId.isPresent()) {
            result = ListUtil.filter(result, userPermissionDto -> userPermissionDto.getActionId().equals(actionId.get()));
        }
        return result;
    }
}
