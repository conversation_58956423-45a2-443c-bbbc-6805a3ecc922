package cz.kpsys.portaro.view.web.rest.record;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.ActiveUser;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.Valuable;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.RecordBySubfieldValueComparator;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.copy.RecordCopyRequest;
import cz.kpsys.portaro.record.copy.TransactionalRecordCopier;
import cz.kpsys.portaro.record.deletion.RecordHoldingsSubtreeDeletionCommand;
import cz.kpsys.portaro.record.deletion.RecordHoldingsSubtreeDeletionRequest;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import cz.kpsys.portaro.record.document.RecordRelatedRecordsLoader;
import cz.kpsys.portaro.record.edit.*;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.SetFieldValueRequest;
import cz.kpsys.portaro.record.merge.RecordMergeCommand;
import cz.kpsys.portaro.record.merge.RecordMergeRequest;
import cz.kpsys.portaro.record.merge.RecordMerger;
import cz.kpsys.portaro.record.operation.RecordEditLevelProvider;
import cz.kpsys.portaro.record.view.RelatedRecordsCountLoader;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;
import java.util.UUID;

import static cz.kpsys.portaro.record.RecordWellKnownFields.OtherRelationship;

@Tag(name = "record", description = "Endpoints for managing records")
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.RECORDS_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordApiController extends GenericApiController {

    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull ByIdLoadable<@NonNull Record, String> recordStringIdPrefixDispatchingLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordMerger recordMerger;
    @NonNull RecordEditLevelProvider recordEditLevelProvider;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull RelatedRecordsCountLoader relatedRecordsCountLoader;
    @NonNull RecordRelatedRecordsLoader recordArticlesLoader;
    @NonNull RecordRelatedRecordsLoader recordPartsLoader;
    @NonNull ContextualProvider<Department, @NonNull List<Integer>> partsContainingFieldNumbersProvider;
    @NonNull ContextualProvider<Department, @NonNull List<RecordStatus>> forbiddenRecordStatusesProvider;
    @NonNull ContextualProvider<Department, @NonNull List<UUID>> forbiddenRecordIdsProvider;
    @NonNull ContextualProvider<Department, @NonNull Boolean> documentFondsDisabledProvider;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordsConverter;
    @NonNull TransactionalRecordCopier recordCopier;
    @NonNull Deleter<RecordHoldingsSubtreeDeletionCommand> recordHoldingsSubtreeDeleter;
    @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader;

    /// http://localhost/api/records?format=json&id=010e1bf6-1ba2-40a2-93d3-915dc43b2319,8dc5499c-4055-4124-883a-b6c34b85c7fc
    @GetMapping(params = "id")
    public List<ViewableRecord> getAllByIds(@RequestParam(value = "id", defaultValue = "") List<UUID> ids,
                                            @CurrentDepartment Department ctx,
                                            UserAuthentication currentAuth,
                                            Locale locale) {
        List<Record> records = recordLoader.getAllByIds(ids);
        return recordsToViewableRecordsConverter.convert(records, currentAuth, ctx, locale);
    }

    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @GetMapping("{id}")
    public ViewableRecord get(@PathVariable("id") String id,
                              @CurrentDepartment Department ctx,
                              UserAuthentication currentAuth,
                              Locale locale) {
        Record record = recordStringIdPrefixDispatchingLoader.getById(id);
        return recordsToViewableRecordsConverter.convertSingle(record, currentAuth, ctx, locale);
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @GetMapping("{id}/edit-level")
    public Valuable<Integer> getEditLevel(@PathVariable("id") Record record) {
        return recordEditLevelProvider.getRecordEditLevel(record);
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @GetMapping(value = "{id}/related-records-count")
    @RateLimited("RecordApiController.getRelatedRecordsCount")
    public Valuable<Integer> getRelatedRecordsCount(@PathVariable("id") RecordIdFondPair recordHeader,
                                                    @CurrentDepartment Department ctx,
                                                    UserAuthentication currentAuth) {
        if (documentFondsDisabledProvider.getOn(ctx)) {
            return Valuable.fromValue(null);
        }
        return Valuable.fromValue(relatedRecordsCountLoader.getByAuthorityAndDepartment(recordHeader, currentAuth, ctx));
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @GetMapping("{id}/articles")
    @RateLimited("RecordApiController.getArticles")
    public List<ViewableRecord> getArticles(@PathVariable("id") Record record,
                                            @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1") int pageNumber,
                                            @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, defaultValue = "100") int pageSize,
                                            @CurrentDepartment Department ctx,
                                            UserAuthentication currentAuth,
                                            Locale locale) {
        List<Record> unsortedRecords = recordArticlesLoader.getAll(
                record.getId(),
                partsContainingFieldNumbersProvider.getOn(ctx),
                forbiddenRecordStatusesProvider.getOn(ctx),
                forbiddenRecordIdsProvider.getOn(ctx),
                Range.of(pageNumber, pageSize)
        );
        List<Record> records = unsortedRecords
                .stream()
                .sorted(RecordBySubfieldValueComparator.byFirstRepetition(OtherRelationship.CODE, OtherRelationship.RelatedParts.CODE))
                .toList();
        return recordsToViewableRecordsConverter.convert(records, currentAuth, ctx, locale);
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @GetMapping("{id}/parts")
    @RateLimited("RecordApiController.getParts")
    public List<ViewableRecord> getParts(@PathVariable("id") Record record,
                                         @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1") int pageNumber,
                                         @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, defaultValue = "100") int pageSize,
                                         @CurrentDepartment Department ctx,
                                         UserAuthentication currentAuth,
                                         Locale locale) {
        List<Record> records = recordPartsLoader.getAll(
                record.getId(),
                partsContainingFieldNumbersProvider.getOn(ctx),
                forbiddenRecordStatusesProvider.getOn(ctx),
                forbiddenRecordIdsProvider.getOn(ctx),
                Range.of(pageNumber, pageSize)
        );
        return recordsToViewableRecordsConverter.convert(records, currentAuth, ctx, locale);
    }


    @Operation(summary = "Change catalogization phase of record")
    @PostMapping("/phase")
    public ActionResponse setCatalogizationPhase(@RequestBody @ValidFormObject RecordEditationPhaseRequest request,
                                                 @CurrentDepartment Department ctx,
                                                 UserAuthentication currentAuth) {
        createExistingRecordEditation(request.record(), ctx, currentAuth)
                .changeCatalogizationPhase(request.phase(), ctx)
                .saveIfModified(ctx, currentAuth);
        return FinishedActionResponse.ok();
    }


    @Operation(summary = "Create, edit or delete record field's value")
    @PostMapping("/v2/{record}/fields/{fieldId}")
    public ActionResponse setRecordFieldValue(@PathVariable Record record,
                                              @PathVariable @NonNull FieldId fieldId,
                                              @RequestBody(required = false) SetFieldValueRequest request,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth) {
        RecordEditation editation = createExistingRecordEditation(record, ctx, currentAuth);

        if (request == null) { // pokud rucne (klavesnici) mazeme hodnotu u fieldu, hodnota bude null
            FieldDeletionCommand command = FieldDeletionCommand.of(editation.getRecord(), fieldId)
                    .clearOnly()
                    .ignoreMissingHierarchy()
                    .preferInfluencerFieldDeletion()
                    .notDeleteAlsoEmptyHierarchy();
            recordFieldEditor.deleteField(editation, command);
            editation.saveIfModified(ctx, currentAuth);
            return FinishedActionResponse.ok();
        } else {
            FieldType fieldType = fieldTypeLoader.getById(fieldId.getFieldTypeId());
            FieldValueCommand valueCommand = fieldValueCommandResolver.resolveCommand(request, fieldType, ctx, currentAuth);
            FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), fieldId, valueCommand)
                    .createMissingHierarchy()
                    .preferInfluencerFieldEditation();
            recordFieldEditor.editField(editation, command);
            editation.saveIfModified(ctx, currentAuth);
            return FinishedSaveResponse.saved(editation.getFirstFieldRecursive(By.fieldId(fieldId)).orElseThrow());
        }
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @PostMapping("/merge")
    public ActionResponse mergeRecords(@RequestBody @ValidFormObject RecordMergeRequest mergeRequest,
                                       @CurrentDepartment Department ctx,
                                       UserAuthentication currentAuth) {
        recordMerger.merge(new RecordMergeCommand(
                ctx,
                currentAuth,
                mergeRequest.target(),
                mergeRequest.sources(),
                mergeRequest.mergeDetail()
        ));
        return FinishedActionResponse.ok();
    }


    @Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
    @PostMapping("/copy")
    public ActionResponse copyRecord(@RequestBody @ValidFormObject RecordCopyRequest request,
                                     @CurrentDepartment Department ctx,
                                     @ActiveUser User activeUser,
                                     UserAuthentication currentAuth) {

        var savedRecord = recordCopier.copyRecord(request.toCommand(ctx, currentAuth));
        return FinishedSaveResponse.saved(savedRecord);
    }


    @Operation(summary = "Delete record in whole department subtree if there are no holdings in parent departments")
    @PostMapping("/delete-subtree-holdings")
    public ActionResponse deleteSubtreeHoldings(@RequestBody @ValidFormObject RecordHoldingsSubtreeDeletionRequest request,
                                                @CurrentDepartment Department ctx,
                                                UserAuthentication currentAuth) {
        recordHoldingsSubtreeDeleter.delete(new RecordHoldingsSubtreeDeletionCommand(request.record(), ctx, currentAuth));
        return FinishedActionResponse.ok();
    }


    private RecordEditation createExistingRecordEditation(Record record, Department ctx, UserAuthentication currentAuth) {
        return recordEditationFactory
                .on(ctx)
                .ofExisting(record)
                .build(currentAuth);
    }
}
