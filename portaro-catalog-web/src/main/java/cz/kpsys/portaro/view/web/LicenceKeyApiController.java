package cz.kpsys.portaro.view.web;

import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LicenceKeyApiController extends GenericApiController {

    @NonNull ContextualProvider<Department, String> licenceKeyDepartmentedProvider;

    @RequestMapping("/api/licence-key")
    public String get(@CurrentDepartment Department currentDepartment) {
        return licenceKeyDepartmentedProvider.getOn(currentDepartment);
    }

    
}
