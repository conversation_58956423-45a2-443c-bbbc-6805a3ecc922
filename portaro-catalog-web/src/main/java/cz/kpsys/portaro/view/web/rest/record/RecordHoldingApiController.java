package cz.kpsys.portaro.view.web.rest.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.record.deletion.RecordHoldingDeletionCommand;
import cz.kpsys.portaro.record.deletion.RecordHoldingDeletionRequest;
import cz.kpsys.portaro.record.discardation.RecordHoldingDiscardationCommand;
import cz.kpsys.portaro.record.discardation.RecordHoldingDiscardationRequest;
import cz.kpsys.portaro.record.discardation.RecordHoldingDiscarder;
import cz.kpsys.portaro.record.holding.*;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
@RequestMapping("/api/record-holdings")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordHoldingApiController extends GenericApiController {

    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull Deleter<RecordHoldingDeletionCommand> recordHoldingDeleter;
    @NonNull RecordHoldingDiscarder recordHoldingDiscarder;

    @GetMapping
    public List<RecordHolding> getRecordHoldings(@RequestParam("record") UUID recordId,
                                                 @RequestParam(value = "rootDepartment", required = false) Department rootDepartment) {
        if (rootDepartment == null) {
            return recordHoldingLoader.getAllByRecordId(recordId);
        }
        return recordHoldingLoader.getAllByRecordIdAndRootDepartment(recordId, rootDepartment);
    }


    @PostMapping
    public ActionResponse createRecordHolding(@RequestBody @ValidFormObject RecordHoldingCreationRequest request,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth) {
        RecordHoldingUpsertResult result = recordHoldingUpserter.upsert(new RecordHoldingUpsertCommand(request.record(), request.department(), false, ctx, currentAuth));
        return FinishedSaveResponse.saved(result.savedHolding());
    }

    @PostMapping("/delete")
    public ActionResponse deleteRecordHolding(@RequestBody @ValidFormObject RecordHoldingDeletionRequest recordHoldingDeletionRequest,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth) {
        recordHoldingDeleter.delete(new RecordHoldingDeletionCommand(
                recordHoldingDeletionRequest.recordHolding(),
                ctx,
                currentAuth,
                ObjectUtil.firstNotNull(recordHoldingDeletionRequest.acceptingExemplarsDeletion(), false),
                ObjectUtil.firstNotNull(recordHoldingDeletionRequest.acceptingToDeletableExemplarStatusChange(), false),
                true,
                true));
        return FinishedActionResponse.ok();
    }


    @PostMapping("/discard")
    public ActionResponse discardRecordHolding(@RequestBody @ValidFormObject RecordHoldingDiscardationRequest recordHoldingDiscardationRequest,
                                              @CurrentDepartment Department ctx,
                                              UserAuthentication currentAuth) {
        recordHoldingDiscarder.discard(new RecordHoldingDiscardationCommand(
                recordHoldingDiscardationRequest.recordHolding(),
                ctx,
                currentAuth));
        return FinishedActionResponse.ok();
    }
}
