package cz.kpsys.portaro.view.web;

import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.web.DownloadFileStreamConsumer;
import cz.kpsys.portaro.file.FileConstants;
import cz.kpsys.portaro.web.GenericController;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import static cz.kpsys.portaro.view.web.FileDataRepresentation.*;

@RequestMapping(FileConstants.Web.FILE_DOWNLOAD_PATH_PREFIX)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FileDownloadController extends GenericController {

    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull FileDataStreamer fileThumbnailDataStreamer;
    @NonNull FileDataStreamer fileTextDataStreamer;

    @GetMapping("/{file}")
    public void download(@PathVariable("file") Long id,
                         @RequestParam(value = "width", required = false) Integer width,
                         @RequestParam(value = "height", required = false) Integer height,
                         @RequestParam(value = "inline", required = false) Boolean forcedInlining,
                         @Nullable BytesRange range,
                         HttpServletResponse response) {
        download(id, null, width, height, forcedInlining, range, response);
    }

    @GetMapping("/{file}/thumbnail")
    public void downloadThumbnail(@PathVariable("file") Long id,
                                  @RequestParam(value = "inline", required = false) Boolean forcedInlining,
                                  HttpServletResponse response) {
        download(id, THUMBNAIL, null, null, forcedInlining, null, response);
    }

    @GetMapping("/{file}/text")
    public void downloadText(@PathVariable("file") Long id,
                             @RequestParam(value = "inline", required = false) Boolean forcedInlining,
                             HttpServletResponse response) {
        download(id, TEXTUAL, null, null, forcedInlining, null, response);
    }

    // TODO: zabezpečit? A co /api/files?
    private void download(@NonNull Long id,
                          @Nullable FileDataRepresentation representation,
                          @Nullable Integer width,
                          @Nullable Integer height,
                          @Nullable Boolean forcedInlining,
                          @Nullable BytesRange range,
                          @NonNull HttpServletResponse response) {
        DownloadFileStreamConsumer downloadStreamConsumer = new DownloadFileStreamConsumer(response);

        if (ObjectUtil.isTrue(forcedInlining)) {
            downloadStreamConsumer.forceInlineContentDisposition();
        } else if (ObjectUtil.isFalse(forcedInlining)) {
            downloadStreamConsumer.forceAttachmentContentDisposition();
        }

        FileDataStreamer loader = resolveLoader(representation, width, height);
        loader.streamData(id, range, downloadStreamConsumer);
    }

    private FileDataStreamer resolveLoader(@Nullable FileDataRepresentation representation, @Nullable Integer width, @Nullable Integer height) {
        FileDataRepresentation finalRepresentation;
        if (representation != null) {
            finalRepresentation = representation;
        } else if (width != null || height != null) {
            finalRepresentation = THUMBNAIL;
        } else {
            finalRepresentation = ORIGINAL;
        }
        return switch (finalRepresentation) {
            case ORIGINAL -> fileDataStreamer;
            case THUMBNAIL -> fileThumbnailDataStreamer;
            case TEXTUAL -> fileTextDataStreamer;
        };
    }

}
