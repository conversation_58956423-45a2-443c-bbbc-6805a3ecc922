package cz.kpsys.portaro.record.collection.treelist;

import cz.kpsys.portaro.commons.object.TreeGraphElement;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;

public class TreeListToRecordCollectionAcceptableEditorOptionsConverter implements Converter<@NonNull RecordCollectionsTreeList, @NonNull List<RecordCollectionAcceptableEditorOption>> {

    @Override
    public @NonNull List<RecordCollectionAcceptableEditorOption> convert(@NonNull RecordCollectionsTreeList source) {
        return flattenTreeList(source);
    }

    private List<RecordCollectionAcceptableEditorOption> flattenTreeList(RecordCollectionsTreeList treeList) {
        List<RecordCollectionAcceptableEditorOption> flattenedList = new ArrayList<>();
        for (RecordCollectionsTreeListNode node : treeList.nodes()) {
            flattenedList.addAll(flattenTreeNode(node, new ArrayList<>()));
        }
        return flattenedList;
    }

    private List<RecordCollectionAcceptableEditorOption> flattenTreeNode(RecordCollectionsTreeListNode node, List<TreeGraphElement> treeGraphElements) {
        List<RecordCollectionAcceptableEditorOption> flattenedList = new ArrayList<>();

        flattenedList.add(RecordCollectionAcceptableEditorOption.from(node.recordCollection(), node.depth(), updateTreeGraphElements(treeGraphElements)));
        @NonNull List<RecordCollectionsTreeListNode> nodes = node.nodes();

        for (int i = 0, nodesSize = nodes.size(); i < nodesSize; i++) {
            RecordCollectionsTreeListNode child = nodes.get(i);
            var isLast = i == nodesSize - 1;
            flattenedList.addAll(flattenTreeNode(child, ListUtil.createNewListAppending(treeGraphElements, isLast ? TreeGraphElement.SPACE : TreeGraphElement.LINE)));
        }
        return flattenedList;
    }

    private List<TreeGraphElement> updateTreeGraphElements(List<TreeGraphElement> treeGraphElements) {
        if (treeGraphElements.isEmpty()) {
            return treeGraphElements;
        }

        var lastElement = treeGraphElements.getLast();
        List<TreeGraphElement> result;
        switch (lastElement) {
            case TreeGraphElement.SPACE:
                result = new ArrayList<>(treeGraphElements.subList(0, treeGraphElements.size() - 1));
                result.add(TreeGraphElement.L_JUNCTION);
                return result;
            case TreeGraphElement.LINE:
                result = new ArrayList<>(treeGraphElements.subList(0, treeGraphElements.size() - 1));
                result.add(TreeGraphElement.T_JUNCTION);
                return result;
            default:
                return treeGraphElements;
        }
    }
}
