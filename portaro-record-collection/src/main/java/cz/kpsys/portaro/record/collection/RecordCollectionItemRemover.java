package cz.kpsys.portaro.record.collection;

import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.Repository;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.record.collection.recordcollectionitem.RecordCollectionItem;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.SearchableRepository;
import cz.kpsys.portaro.search.StaticParamsModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordCollectionItemRemover {
    @NonNull SearchableRepository<MapBackedParams, RecordCollectionItem, UUID> recordCollectionItemSearchableRepository;
    @NonNull Repository<RecordCollection, UUID> recordCollectionRepository;
    @NonNull Eventer eventer;

    public void removeSingleCollectionItem(RecordCollectionItem recordCollectionItem, RecordCollection recordCollection, Department currentDepartment, BasicUserAuthentication currentAuth) {
        recordCollectionItemSearchableRepository.delete(recordCollectionItem);
        Event itemRemovalEvent = eventer.save(Event.Codes.RECORD_COLLECTION_ITEM_REMOVAL, currentAuth, currentDepartment, recordCollectionItem.getRecord().getId().toString(), recordCollection.getId());
        RecordCollection updatedRecordCollection = recordCollection.withLastUpdateEventId(itemRemovalEvent.getId());
        recordCollectionRepository.save(updatedRecordCollection);
    }

    public void removeAllItemsFromCollection(List<RecordCollection> toBeClearedCollections, Department currentDepartment, BasicUserAuthentication currentAuth) {
        var toBeDeletedItems = DataUtils.loadInChunksWithoutPostSorting(
                ListUtil.convert(toBeClearedCollections, Identified::getId),
                250,
                ids -> recordCollectionItemSearchableRepository.getContent(RangePaging.forAll(), StaticParamsModifier.of(RecordCollectionConstants.SearchParams.RECORD_COLLECTION, ids)));

        toBeDeletedItems.forEach(recordCollectionItemSearchableRepository::delete);
        toBeClearedCollections.forEach(collection -> saveItemRemovalEvent(collection, currentDepartment, currentAuth));
    }

    private void saveItemRemovalEvent(RecordCollection recordCollection, Department currentDepartment, BasicUserAuthentication currentAuth) {
        Event itemRemovalEvent = eventer.save(Event.Codes.RECORD_COLLECTION_ITEM_REMOVAL, currentAuth, currentDepartment, "REMOVED ALL", recordCollection.getId());
        RecordCollection updatedRecordCollection = recordCollection.withLastUpdateEventId(itemRemovalEvent.getId());
        recordCollectionRepository.save(updatedRecordCollection);
    }
}
