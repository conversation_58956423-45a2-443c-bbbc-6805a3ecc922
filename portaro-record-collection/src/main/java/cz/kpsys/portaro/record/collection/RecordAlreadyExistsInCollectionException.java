package cz.kpsys.portaro.record.collection;

import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordAlreadyExistsInCollectionException extends RuntimeException implements SeveritedException {

    public RecordAlreadyExistsInCollectionException(@NonNull String message) {
        super(message);
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}
