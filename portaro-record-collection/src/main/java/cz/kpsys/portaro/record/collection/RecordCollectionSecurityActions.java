package cz.kpsys.portaro.record.collection;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.collection.api.RecordCollectionCreationRequest;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.record.collection.utils.RecordCollectionsSubtree;
import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.user.BasicUser;

public class RecordCollectionSecurityActions {
    public static final Action<BasicUser> RECORD_COLLECTIONS_SHOW_USERS_COLLECTIONS = Action.withSubject("RecordCollectionsShowUsersCollections", BasicUser.class);
    public static final Action<RecordCollection> RECORD_COLLECTION_SHOW_CONTENT = Action.withSubject("RecordCollectionShowContent", RecordCollection.class);
    public static final Action<RecordCollection> RECORD_COLLECTION_EDIT = Action.withSubject("RecordCollectionEdit", RecordCollection.class);
    public static final Action<RecordCollection> RECORD_COLLECTION_INSERT = Action.withSubject("RecordCollectionInsert", RecordCollection.class);
    public static final Action<RecordCollection> RECORD_COLLECTION_DELETE = Action.withSubject("RecordCollectionDelete", RecordCollection.class);
    public static final Action<RecordCollectionsSubtree> RECORD_COLLECTION_SUBTREE_DELETE = Action.withSubject("RecordCollectionSubtreeDelete", RecordCollectionsSubtree.class);
    public static final Action<RecordCollectionCreationRequest> RECORD_COLLECTION_CREATE = Action.withSubject("RecordCollectionCreate", RecordCollectionCreationRequest.class);
    public static final Action<RecordCollection> RECORD_COLLECTION_CREATE_SUBCOLLECTION = Action.withSubject("RecordCollectionCreateSubcollection", RecordCollection.class);
    public static final Action<Void> FAVOURITES_PERSIST = Action.withoutSubject("FavouritesPersist");
    public static final Action<Record> COLLECTIONS_INSERT_RECORD = Action.withSubject("CollectionsInsertRecord", Record.class);
}