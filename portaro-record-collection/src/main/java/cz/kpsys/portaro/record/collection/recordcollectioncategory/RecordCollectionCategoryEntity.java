package cz.kpsys.portaro.record.collection.recordcollectioncategory;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordCollectionDb.RECORD_COLLECTION_CATEGORY.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordCollectionCategoryEntity implements Serializable, Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = TYPE)
    @NonNull
    String type;

    @Column(name = DESCRIPTION)
    @NonNull
    String description;
}
