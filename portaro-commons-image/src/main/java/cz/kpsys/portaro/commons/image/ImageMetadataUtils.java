package cz.kpsys.portaro.commons.image;

import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.MetadataException;
import com.drew.metadata.bmp.BmpHeaderDirectory;
import com.drew.metadata.eps.EpsDirectory;
import com.drew.metadata.exif.ExifIFD0Directory;
import com.drew.metadata.exif.ExifImageDirectory;
import com.drew.metadata.gif.GifHeaderDirectory;
import com.drew.metadata.heif.HeifDirectory;
import com.drew.metadata.ico.IcoDirectory;
import com.drew.metadata.jpeg.JpegDirectory;
import com.drew.metadata.png.PngDirectory;
import com.drew.metadata.webp.WebpDirectory;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.StandardException;
import lombok.extern.slf4j.Slf4j;
import org.imgscalr.Scalr;

import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ImageMetadataUtils {

    public static Optional<Dimensions> getDimensions(InputStream original)
            throws ImageProcessingException, IOException {
        try {
            return getDimensionsFromMetadata(ImageMetadataReader.readMetadata(original));
        } catch (com.drew.imaging.ImageProcessingException e) {
            throw new ImageProcessingException(e);
        }
    }

    // Šlo by udělat bez generování zbytečných Dimensions přes výstupní parametr s polem
    static Optional<Dimensions> getDimensionsFromMetadata(Metadata metadata)
            throws ImageProcessingException {

        Optional<Integer> exifOrientation = getExifOrientationFromMetadata(metadata, ExifIFD0Directory.class, ExifIFD0Directory.TAG_ORIENTATION)
                .or(() -> getExifOrientationFromMetadata(metadata, ExifImageDirectory.class, ExifImageDirectory.TAG_ORIENTATION));

        MetadataException lastError = null;
        for (Directory dir : metadata.getDirectories()) {
            try {
                Optional<Dimensions> maybeDimensions = dimensionsOf(dir);

                if (maybeDimensions.isPresent()) {
                    Dimensions dimensions = maybeDimensions.get();

                    if (exifOrientation.isPresent() && shouldSwapDimensions(exifOrientation.get())) {
                        return Optional.of(new Dimensions(dimensions.height(), dimensions.width()));
                    }

                    return Optional.of(dimensions);
                }
            } catch (MetadataException e) {
                lastError = e;
            }
        }

        if (lastError != null) {
            throw new ImageProcessingException(lastError);
        }

        return Optional.empty();
    }

    private static Optional<Dimensions> dimensionsOf(Directory directory) throws MetadataException {
        return switch (directory) {
            case ExifIFD0Directory exif ->
                    dimensionsOf(exif, ExifIFD0Directory.TAG_IMAGE_WIDTH, ExifIFD0Directory.TAG_IMAGE_HEIGHT);
            case JpegDirectory jpeg ->
                    dimensionsOf(jpeg, JpegDirectory.TAG_IMAGE_WIDTH, JpegDirectory.TAG_IMAGE_HEIGHT);
            case PngDirectory png ->
                    dimensionsOf(png, PngDirectory.TAG_IMAGE_WIDTH, PngDirectory.TAG_IMAGE_HEIGHT);
            case GifHeaderDirectory gif ->
                    dimensionsOf(gif, GifHeaderDirectory.TAG_IMAGE_WIDTH, GifHeaderDirectory.TAG_IMAGE_HEIGHT);
            case WebpDirectory webp ->
                    dimensionsOf(webp, WebpDirectory.TAG_IMAGE_WIDTH, WebpDirectory.TAG_IMAGE_HEIGHT);
            case BmpHeaderDirectory bmp ->
                    dimensionsOf(bmp, BmpHeaderDirectory.TAG_IMAGE_WIDTH, BmpHeaderDirectory.TAG_IMAGE_HEIGHT);
            case HeifDirectory heif ->
                    dimensionsOf(heif, HeifDirectory.TAG_IMAGE_WIDTH, HeifDirectory.TAG_IMAGE_HEIGHT);
            case EpsDirectory eps ->
                    dimensionsOf(eps, EpsDirectory.TAG_IMAGE_WIDTH, EpsDirectory.TAG_IMAGE_HEIGHT);
            case IcoDirectory ico ->
                    dimensionsOf(ico, IcoDirectory.TAG_IMAGE_WIDTH, IcoDirectory.TAG_IMAGE_HEIGHT);
            default -> Optional.empty();
        };
    }

    /**
     * Získání výšky a šířky obrázku z metadat.
     *
     * @param dir metadata
     * @param widthTag id šířky v metadatech
     * @param heightTag id výšky v metadatech
     *
     * @return výška a šířka.
     *
     * @throws MetadataException v případě, že je v metadatech brajgl
     */
    private static Optional<Dimensions> dimensionsOf(Directory dir, int widthTag, int heightTag) throws MetadataException {
        if (dir.containsTag(widthTag) && dir.containsTag(heightTag)) {
            int width = dir.getInt(widthTag);
            int height = dir.getInt(heightTag);
            return Optional.of(new Dimensions(width, height));
        }
        return Optional.empty();
    }


    public static class ExifOrientations {
        public static final int NORMAL = 1;
        public static final int MIRROR_HORIZONTAL = 2;
        public static final int ROTATE_180 = 3;
        public static final int MIRROR_VERTICAL = 4;
        public static final int MIRROR_HORIZONTAL_AND_ROTATE_270_CW = 5;
        public static final int ROTATE_90_CW = 6;
        public static final int MIRROR_HORIZONTAL_AND_ROTATE_90_CW = 7;
        public static final int ROTATE_270_CW = 8;
    }

    public static Optional<Scalr.Rotation> getRotation(ImageData original)
            throws ImageProcessingException, IOException {

        Optional<Integer> exifOrientation = getExifOrientation(original); // exif orientation is fetched from original image, not from resized!
        if (exifOrientation.isEmpty()) {
            return Optional.empty();
        }
        return getRotationAccordingToExifOrientation(exifOrientation.get());
    }

    private static Optional<Integer> getExifOrientation(ImageData original)
            throws ImageProcessingException, IOException {

        try {
            Metadata metadata = ImageMetadataReader.readMetadata(original.createInputStream());
            return getExifOrientationFromMetadata(metadata, ExifIFD0Directory.class, ExifIFD0Directory.TAG_ORIENTATION)
                    .or(() -> getExifOrientationFromMetadata(metadata, ExifImageDirectory.class, ExifImageDirectory.TAG_ORIENTATION));
        } catch (com.drew.imaging.ImageProcessingException e) {
            throw new ImageProcessingException(e);
        }
    }

    private static <T extends Directory> Optional<Integer> getExifOrientationFromMetadata(
            Metadata metadata, Class<T> directoryType, int tagType) {

        T directory = metadata.getFirstDirectoryOfType(directoryType);
        if (directory == null) {
            return Optional.empty();
        }
        try {
            int value = directory.getInt(tagType); // throws MetadataException if no value exists for tagType or if it cannot be converted to an int.
            return Optional.of(value);
        } catch (MetadataException e) {
            return Optional.empty();
        }
    }

    private static Optional<Scalr.Rotation> getRotationAccordingToExifOrientation(int exifOrientation) {
        return switch (exifOrientation) {
            case ExifOrientations.NORMAL -> Optional.empty(); // [Exif IFD0] Orientation - Top, left side (Horizontal / normal)
            case ExifOrientations.MIRROR_HORIZONTAL -> Optional.empty();
            case ExifOrientations.ROTATE_180 -> Optional.of(Scalr.Rotation.CW_180); // [Exif IFD0] Orientation - Bottom, right side (Rotate 180)
            case ExifOrientations.MIRROR_VERTICAL -> Optional.empty();
            case ExifOrientations.MIRROR_HORIZONTAL_AND_ROTATE_270_CW -> Optional.of(Scalr.Rotation.CW_270);
            case ExifOrientations.ROTATE_90_CW -> Optional.of(Scalr.Rotation.CW_90); // [Exif IFD0] Orientation - Right side, top (Rotate 90 CW)
            case ExifOrientations.MIRROR_HORIZONTAL_AND_ROTATE_90_CW -> Optional.of(Scalr.Rotation.CW_90);
            case ExifOrientations.ROTATE_270_CW -> Optional.of(Scalr.Rotation.CW_270); // [Exif IFD0] Orientation - Left side, bottom (Rotate 270 CW)
            default -> {
                log.warn("Unknown image EXIF orientation value {}, returning no-rotation", exifOrientation);
                yield Optional.empty();
            }
        };
    }

    private static boolean shouldSwapDimensions(int exifOrientation) {
        return switch (exifOrientation) {
            case ExifOrientations.MIRROR_HORIZONTAL_AND_ROTATE_270_CW,
                 ExifOrientations.ROTATE_90_CW,
                 ExifOrientations.MIRROR_HORIZONTAL_AND_ROTATE_90_CW,
                 ExifOrientations.ROTATE_270_CW -> true;
            default -> false;
        };
    }

    @StandardException
    public static class ImageProcessingException extends Exception {
    }

}