package cz.kpsys.portaro.commons.image;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.core.io.ClassPathResource;

import java.awt.image.BufferedImage;
import java.io.IOException;

@Tag("ci")
@Tag("unit")
public class CommonsImagingImageDataLoaderTest {

    @ParameterizedTest
    @ValueSource(strings = {
            "test-logo-google.png"
    })
    void shouldLoadAndSaveImageOfAllTypes(String imageClasspathPath) throws IOException {
        ClassPathResource imageResource = new ClassPathResource(imageClasspathPath);
        BufferedImage bufferedImage = new CommonsImagingImageDataLoader().inputStreamToImage(imageResource.getInputStream());
        Assertions.assertTrue(bufferedImage.getHeight() > 100);
    }

}