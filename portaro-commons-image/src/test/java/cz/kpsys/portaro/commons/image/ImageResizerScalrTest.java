package cz.kpsys.portaro.commons.image;

import cz.kpsys.portaro.commons.util.TimeMeter;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("ci")
@Tag("unit")
public class ImageResizerScalrTest {

    @Test
    public void shouldResizeImageWithComputedHeight() {
        ImageResizerScalr resizer = new ImageResizerScalr();
        ImageData image = ImageData.createFromResource(new ClassPathResource("test-logo-google.png"));
        final long originalSize = image.getDataSize();
        final int originalWidth = image.getWidth();
        final int originalHeight = image.getHeight();

        final int expectedWidth = (int) Math.round(originalWidth / 10.0);
        final int expectedHeight = (int) Math.round(originalHeight / 10.0);

        TimeMeter tm = TimeMeter.start();
        ImageData resizedImage = resizer.resize(image, ResizeParams.ofFixedWidth(expectedWidth));
        System.out.println("compression took " + tm.elapsedTimeString());

        assertTrue(resizedImage.getDataSize() < originalSize);
        assertEquals(expectedWidth, resizedImage.getWidth());
        assertEquals(expectedHeight, resizedImage.getHeight());
    }

}
