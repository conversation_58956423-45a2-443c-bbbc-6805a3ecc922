package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class GeneralDbConstants {

    public static class TASK_QUEUE {
        public static final String TABLE = "task_queue";
        public static final String RECORD_ID = "record_id";
        public static final String TYPE = "typ";

        public static class TYPE_VALUES {
            public static final short TYP_REINDEX = 2;
            public static final short TYP_REULOZENI = 4;
        }
    }

    public static class LOG_DEBUG_VYPUC {
        public static final String TABLE = "log_debbug_vypuc";
    }

    public static class LOG_VYPUC {
        public static final String TABLE = "log_vypuc";
    }

    public static class LOG_CTEN {
        public static final String TABLE = "log_cten";
    }

    public static class KARANTENA {
        public static final String TABLE = "karantena";
    }

    public static class POL_EXPED {
        public static final String TABLE = "pol_exped";
    }

    public static class HL_FAK {
        public static final String TABLE = "hl_fak";
    }

    public static class LOG_EXEMP {
        public static final String TABLE = "log_exemp";
    }

    public static class LOG_AUTOMAT {
        public static final String TABLE = "log_automat";
    }

    public static class LOG_AKCE_PRAVA {
        public static final String TABLE = "log_akce_prava";
    }

    public static class LOG_AKTUALIZACE {
        public static final String TABLE = "log_aktualizace";
    }

    public static class LOG_ERRORS {
        public static final String TABLE = "log_errors";
    }

    public static class LOG_TISK {
        public static final String TABLE = "log_tisk";
    }

    public static class LOG_UZIV_DIALOGS {
        public static final String TABLE = "log_uziv_dialogs";
    }
}
