package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class SearchDb {

    public static class DEF_REZ {
        public static final String TABLE = "def_rez";
        public static final String ID_REZ = "id_rez";
        public static final String NAZEV = "nazev";
        public static final String PORADI = "poradi";
        public static final String DEFINICE = "definice";
        public static final String EXEMP = "exemp";
        public static final String INDEXOVAT = "indexovat";
        public static final String TYP = "typ";
        public static final String MNOZINA = "mnozina";
        public static final String DATATYPE = "datatype";
        public static final String RAZENI = "razeni";
    }

    public static class OPAC_SEARCH_KEYS {
        public static final String TABLE = "opac_search_keys";
        public static final String NAZEV = "nazev";
        public static final String LUCENE_KEYS = "klic";
        public static final String DATATYPE = "datatype";
    }

    public static class OPAC_HLEDANE_VYRAZY {
        public static final String TABLE = "opac_hledane_vyrazy";
        public static final String VYRAZ = "vyraz";
        public static final String VYRAZ_FLAT = "vyraz_flat";
        public static final String POCET = "pocet";
        public static final int VYRAZ_FLAT_MAX_LENGTH = 512;
    }

}
