package cz.kpsys.portaro.platform;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.NonNull;
import org.apache.commons.lang3.SystemProperties;

import java.util.Optional;

public class PlatformUtils {

    private static final JavaVersion JAVA_SPECIFICATION_VERSION_AS_ENUM = JavaVersion.get(SystemProperties.getJavaSpecificationVersion());

    // copy of org.apache.commons.lang3.SystemUtils.isJavaVersionAtLeast
    public static boolean isJavaVersionAtLeast(JavaVersion requiredVersion) {
        return JAVA_SPECIFICATION_VERSION_AS_ENUM.atLeast(requiredVersion);
    }

    public static OsCheck.OSType getOperatingSystemType() {
        return OsCheck.getOperatingSystemType();
    }

    public static int getPID() {
        try {
            String processName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
            return Integer.parseInt(processName.split("@")[0]);
        } catch (Exception e) {
            return -1;
        }
    }

    @NonNull
    public static String getEnvOrDefault(@NonNull String env, @NonNull String dflt) {
        return Optional.ofNullable(System.getenv(env)).orElse(dflt);
    }

    @NonNull
    public static String getEnv(@NonNull String env) {
        String val = System.getenv(env);
        if (StringUtil.isNullOrEmpty(val)) {
            throw new IllegalArgumentException("Environment variable %s is required".formatted(env));
        }
        return val;
    }
}
