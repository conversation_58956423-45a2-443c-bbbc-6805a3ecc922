package cz.kpsys.portaro.commons.io;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.time.InstantSource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class ExternalServiceFailoverer implements Failoverer {

    @NonNull InstantSource instantSource;
    @NonNull Pattern replacement;
    @NonNull List<Source> sources = new ArrayList<>();

    public ExternalServiceFailoverer(@NonNull String replacementPattern, @NonNull List<SourceSpec> sourceSpecs) {
        this(InstantSource.system(), replacementPattern, sourceSpecs);
    }

    public ExternalServiceFailoverer(@NonNull Pattern replacementPattern, @NonNull List<SourceSpec> sourceSpecs) {
        this(InstantSource.system(), replacementPattern, sourceSpecs);
    }

    public ExternalServiceFailoverer(@NonNull InstantSource instantSource, @NonNull String replacementPattern,
                                     @NonNull List<SourceSpec> sourceSpecs) {
        this(instantSource, Pattern.compile(replacementPattern), sourceSpecs);
    }

    public ExternalServiceFailoverer(@NonNull InstantSource instantSource, @NonNull Pattern replacementPattern,
                                     @NonNull List<SourceSpec> sourceSpecs) {
        this(instantSource, replacementPattern);
        for (var srcSpec : sourceSpecs) {
            sources.add(new Source(srcSpec));
        }
    }

    /**
     * Failover is triggered on reception of {@link ExternalServiceAccessException} exception from {@code action}.
     * Any other exception is passed to the caller.
     *
     * @param url requested resource
     * @param action action that downloads the resource
     * @param <R> return type
     *
     * @return resource if there is no error.
     *
     * @throws ExternalServiceAccessException if there is {@link ExternalServiceAccessException} on last active mirror
     * @throws ExternalServiceException if there is {@link ExternalServiceException} on current mirror
     * @throws RuntimeException if there is {@link RuntimeException} on current mirror
     */
    public <R> R call(@NonNull String url, @NonNull ExternalServiceCaller<R> action)
            throws ExternalServiceException {

        Instant now = instantSource.instant();
        Matcher matcher = replacement.matcher(url); // Když sedí nahrazení url za mirror

        ExternalServiceAccessException lastException = null;
        for (Source src: sources) {
            // Není ideální synchronizace mezi vlákny, ale nám to snad stačit bude
            // Přinejhorším dvě vlákna najednou zjistí, že mirror není použitelný a nastaví stejnou flagu
            if (! src.isNowUsable(now)) {
                continue;
            }
            matcher.reset();
            String resultUrl = matcher.replaceFirst(src.url); // A použijeme ho k dotazu
            try {
                return action.call(resultUrl);
            } catch (ExternalServiceAccessException e) {
                log.debug("Cover download connection problem for {} (mirror {}). Trying another source..", url, resultUrl, e);
                src.invalidate(now);
                lastException = e;
            }
        }

        if (lastException != null) {
            throw new ExternalServiceAccessException("Last failover mirror failed", lastException);
        } else {
            throw new NoFailoverMirrorException(String.format("No candidate mirror is active to download \"%s\"!", url));
        }
    }

    @Override
    public boolean isReady() {
        Instant now = instantSource.instant();
        for (Source src: sources) {
            if (src.isNowUsable(now)) {
                return true;
            }
        }
        return false;
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private static class Source {
        @NonNull String url;
        @NonNull Duration tryAfter;
        @NonFinal @NonNull volatile Instant lastFailed;

        public Source(@NonNull SourceSpec sourceSpec) {
            this.url = sourceSpec.url();
            this.tryAfter = sourceSpec.tryAfter();
            this.lastFailed = Instant.MIN;
        }

        public boolean isNowUsable(Instant now) {
            return Duration.between(lastFailed, now)
                    .minus(tryAfter)
                    .isPositive();
        }

        public void invalidate(Instant now) {
            lastFailed = now;
        }

    }

}
