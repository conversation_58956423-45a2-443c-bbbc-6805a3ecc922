package cz.kpsys.portaro.commons.io;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.io.input.TeeInputStream;
import org.springframework.util.Assert;

import java.io.InputStream;

/**
 * Proxy pro vytvoreni branchu/kopie/odbocky, do ktere se bude pri cteni targetu zapisovat.
 * Created by <PERSON> on 01.09.2015.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BranchingFileStreamConsumer implements FileStreamConsumer {

    @NonNull FileStreamConsumer target;
    @NonNull WriteStreamProvider branch;
    @NonFinal Long branchingFileSizeLimit = null;
    @NonFinal boolean wasBranched = false;

    public BranchingFileStreamConsumer branchingFileSizeLimit(long size) {
        this.branchingFileSizeLimit = size;
        return this;
    }

    @NonNull
    protected WriteStreamProvider getBranch() {
        Assert.state(wasBranched(), "Cannot call getBranch() on not branched stream reader");
        return branch;
    }

    public boolean wasBranched() {
        return wasBranched;
    }

    @Override
    public void consume(@NonNull StreamInfo info, @NonNull InputStream inputStream) {
        Long size = info.size();
        if (branchingFileSizeLimit != null && (size == null || size >= branchingFileSizeLimit)) {
            target.consume(info, inputStream);
            return;
        }

        branch.setFilename(info.filename());
        TeeInputStream teeInputStream = new TeeInputStream(inputStream, branch.getStream(), true);
        target.consume(info, teeInputStream);
        wasBranched = true;
    }
}
