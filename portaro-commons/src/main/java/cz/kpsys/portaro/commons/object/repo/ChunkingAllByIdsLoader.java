package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.convert.EToEConverter;
import cz.kpsys.portaro.commons.convert.ItemToIdConverter;
import cz.kpsys.portaro.commons.convert.ItemToRidConverter;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.RIdentified;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ChunkingAllByIdsLoader<E, BY, SORT> implements AllByIdsLoadable<E, BY> {

    private static final int FIREBIRD_MAX_IN_STATEMENT_SIZE = 1500;

    @NonNull AllByIdsLoadable<E, BY> target;
    @NonNull Converter<E, SORT> entityToSorterConverter;
    @NonNull Converter<BY, SORT> byToSorterConverter;
    @NonFinal int chunkSize = FIREBIRD_MAX_IN_STATEMENT_SIZE;

    public static <E, BY, ID> ChunkingAllByIdsLoader<E, BY, ID> of(@NonNull AllByIdsLoadable<E, BY> target,
                                                                   @NonNull Converter<E, ID> entityToSorterConverter,
                                                                   @NonNull Converter<BY, ID> byToSorterConverter) {
        return new ChunkingAllByIdsLoader<E, BY, ID>(target, entityToSorterConverter, byToSorterConverter);
    }

    public static <E, BY> ChunkingAllByIdsLoader<E, BY, BY> of(@NonNull AllByIdsLoadable<E, BY> target,
                                                               @NonNull Converter<E, BY> entityToIdConverter) {
        return new ChunkingAllByIdsLoader<E, BY, BY>(target, entityToIdConverter, new EToEConverter<>());
    }

    public static <E extends Identified<ID>, ID> ChunkingAllByIdsLoader<E, ID, ID> ofIdentified(@NonNull AllByIdsLoadable<E, ID> target) {
        return of(target, new ItemToIdConverter<>());
    }

    public static <E extends RIdentified<ID>, ID> ChunkingAllByIdsLoader<E, ID, ID> ofRIdentified(@NonNull AllByIdsLoadable<E, ID> target) {
        return of(target, new ItemToRidConverter<>());
    }

    public ChunkingAllByIdsLoader<E, BY, SORT> withChunkSize(int chunkSize) {
        this.chunkSize = chunkSize;
        return this;
    }

    @Override
    public List<E> getAllByIds(@NonNull List<BY> bys) {
        return DataUtils.loadInChunksWithSorting(bys, chunkSize, target::getAllByIds, createSorter(bys));
    }

    private @NonNull ComparatorForExplicitIdSorting<E, SORT> createSorter(@NonNull List<BY> bys) {
        List<SORT> sorters = ListUtil.convert(bys, byToSorterConverter);
        return new ComparatorForExplicitIdSorting<>(sorters, entityToSorterConverter);
    }
}
