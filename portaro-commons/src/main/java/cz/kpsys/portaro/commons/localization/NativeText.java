package cz.kpsys.portaro.commons.localization;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Locale;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class NativeText implements NoTranslatorSupportingText {

    @Getter
    @NonNull
    String value;

    public static NativeText create(String value) {
        return new NativeText(value);
    }

    @Override
    public String toString() {
        return "NativeText " + value;
    }

    @Override
    public boolean isEmpty() {
        return value.isEmpty();
    }

    @Override
    public <CTX> Optional<String> tryLocalize(Translator<CTX> translator, CTX ctx, Locale locale) {
        return Optional.of(value);
    }

    @Override
    public String getNoTranslatorFallback() {
        return value;
    }
}
