package cz.kpsys.portaro.commons.util;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PrefixedNumberAwareStringComparator implements Comparator<String> {

    @NonNull List<Character> prefixes;

    @Override
    public int compare(String s1, String s2) {
        if (s1 != null && s2 != null && s1.length() >= 2 && s2.length() >= 2) {
            if (s1.equals(s2)) {
                return 0;
            }

            char firstChar1 = s1.charAt(0);
            char firstChar2 = s2.charAt(0);

            int indexOfFirstCharOfS1InPrefixes = prefixes.indexOf(firstChar1);
            int indexOfFirstCharOfS2InPrefixes = prefixes.indexOf(firstChar2);

            if (indexOfFirstCharOfS1InPrefixes != -1 && indexOfFirstCharOfS2InPrefixes != -1) {
                String num1 = s1.substring(1);
                String num2 = s2.substring(1);
                if (StringUtils.isNumeric(num1) && StringUtils.isNumeric(num2)) {
                    int prefixComparison = Integer.compare(indexOfFirstCharOfS1InPrefixes, indexOfFirstCharOfS2InPrefixes);
                    if (prefixComparison != 0) {
                        return prefixComparison;
                    }
                    return Integer.compare(Integer.parseInt(num1), Integer.parseInt(num2));
                }
            }
        }

        return 0;
    }
}
