package cz.kpsys.portaro.commons.web;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StaticLink implements Link {

    @Getter
    @NonNull
    String url;

    @Getter
    @NonFinal
    @NonNull
    Text text;

    public StaticLink(String url) {
        this(url, Texts.ofNative(url));
    }

    public static StaticLink ofWithoutBase(String path) {
        return new StaticLink(path, Texts.ofNative(path));
    }

    public StaticLink withText(Text text) {
        this.text = text;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof StaticLink link)) {
            return false;
        }
        return url.equals(link.url);
    }

    @Override
    public int hashCode() {
        return url.hashCode();
    }
}
