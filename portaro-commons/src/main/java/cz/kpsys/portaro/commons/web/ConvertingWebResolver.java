package cz.kpsys.portaro.commons.web;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;

import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ConvertingWebResolver<S, T> implements WebResolver<T> {

    @NonNull WebResolver<S> sourceWebResolver;
    @NonNull Converter<S, T> converter;


    @Override
    public Optional<T> tryResolve(@NonNull HttpServletRequest request) {
        Optional<S> intermediate = sourceWebResolver.tryResolve(request);
        if (intermediate.isEmpty()) {
            return Optional.empty();
        }

        T converted = Objects.requireNonNull(converter.convert(intermediate.get()));

        log.debug("Intermediate value {} found in request, then converted to {}", intermediate.get(), converted);

        return Optional.of(converted);
    }

}
