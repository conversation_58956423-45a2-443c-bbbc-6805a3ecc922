package cz.kpsys.portaro.commons.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import org.springframework.core.convert.converter.Converter;

/**
 *
 * <AUTHOR>
 * @param <E>
 */
public class JsonToObjectConverter<E> implements Converter<String, E> {

    private final ObjectMapper mapper;
    private Class<E> clazz;
    private TypeReference<E> typeReference;

    public JsonToObjectConverter(ObjectMapper mapper, Class<E> clazz) {
        this.mapper = mapper;
        this.clazz = clazz;
    }

    public JsonToObjectConverter(Class<E> clazz) {
        this(new ObjectMapper(), clazz);
    }

    public JsonToObjectConverter(ObjectMapper mapper, TypeReference<E> typeReference) {
        this.mapper = mapper;
        this.typeReference = typeReference;
    }

    public JsonToObjectConverter(TypeReference<E> typeReference) {
        this(new ObjectMapper(), typeReference);
    }
    
    @Override
    public E convert(String s) {
        if (StringUtil.isNullOrEmpty(s)) {
            throw new EmptySourceForConversionException(JsonToObjectConverter.class);
        }
        try {
            if (clazz != null) {
                return mapper.readValue(s, clazz);
            }
            return mapper.readValue(s, typeReference);
        } catch (Exception ex) {
            throw new ConversionException(String.format("Cannot convert json '%s' to %s", s, ObjectUtil.firstNotNull(clazz, typeReference)), ex);
        }
    }
    
}
