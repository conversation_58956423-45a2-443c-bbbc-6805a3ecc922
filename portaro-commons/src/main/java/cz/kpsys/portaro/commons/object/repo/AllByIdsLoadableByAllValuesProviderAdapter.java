package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AllByIdsLoadableByAllValuesProviderAdapter<E extends Identified<ID>, ID> implements AllByIdsLoadable<E, ID> {

    @NonNull AllValuesProvider<E> allValuesProvider;

    @Override
    public List<E> getAllByIds(@NonNull List<ID> ids) {
        List<E> all = allValuesProvider.getAll();
        return ids.stream()
                .map(id -> ListUtil.getByIdOrThrow(all, id))
                .toList();
    }
}
