package cz.kpsys.portaro.commons.phonenumber;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;

public class InvalidPhoneNumberException extends RuntimeException implements UserFriendlyException, SeveritedException {

    public InvalidPhoneNumberException(String value) {
        super("Invalid phone number %s".formatted(value));
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Invalid phone number");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}
