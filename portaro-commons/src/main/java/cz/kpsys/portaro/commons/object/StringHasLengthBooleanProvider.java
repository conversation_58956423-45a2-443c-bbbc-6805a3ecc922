package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StringHasLengthBooleanProvider implements Provider<@NonNull Boolean> {

    @NonNull Provider<String> stringProvider;

    @Override
    public Boolean get() {
        return StringUtil.hasLength(stringProvider.get());
    }
}
