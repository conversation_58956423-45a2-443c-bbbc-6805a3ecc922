package cz.kpsys.portaro.commons.mail;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.RegExpPatterns;
import cz.kpsys.portaro.commons.util.StringUtil;
import jakarta.annotation.Nullable;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Set;

import static java.nio.charset.StandardCharsets.UTF_8;


@Validated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JavaMailSenderMailer<CTX> implements Mailer<CTX> {

    public static final String WRONG_EMAIL_FORMAT = "commons.ChybneZadanyEmail";

    @NonNull ContextualProvider<CTX, String> physicalSenderEmailProvider;
    @NonNull ContextualProvider<CTX, String> physicalSenderNameProvider;
    @NonNull ContextualProvider<CTX, Boolean> useSenderAndFromInPlaceOfFromAndReplyToProvider;
    @NonNull ContextualProvider<CTX, String> replyToAddressForMailsFromAdminProvider;
    @NonNull ContextualProvider<CTX, JavaMailSender> mailSenderProvider;


    @Override
    public void send(@Valid Mail<CTX> mail) {
        CTX ctx = mail.ctx();
        String customSenderEmail = mail.senderEmail();
        Set<String> recipientsEmails = mail.recipientsEmails();


        String physicalSenderEmail = physicalSenderEmailProvider.getOn(ctx);
        Assert.hasLength(physicalSenderEmail, "Není nastavena adresa odesílatele e-mailů, kontaktujte, prosím knihovnu");

        @Nullable String physicalSenderName = StringUtil.notBlankTrimmedString(physicalSenderNameProvider.getOn(ctx));

        IncorrectMailInputException.throwIf(ListUtil.isNullOrEmpty(recipientsEmails), "Recipient mails field is null or empty", Texts.ofMessageCoded(WRONG_EMAIL_FORMAT));
        for (String recipientEmail : recipientsEmails) {
            IncorrectMailInputException.throwIf(!recipientEmail.matches(RegExpPatterns.EMAIL), "Incorrect recipient email format", Texts.ofMessageCoded(WRONG_EMAIL_FORMAT));
        }
        IncorrectMailInputException.throwIf(StringUtil.hasLength(customSenderEmail) && !customSenderEmail.matches(RegExpPatterns.EMAIL), "Incorrect sender email format", Texts.ofMessageCoded(WRONG_EMAIL_FORMAT));

        Assert.state(mail.body() != null || ListUtil.hasLength(mail.attachments()), "At leas one of body and attachments must be filled");

        try {

            JavaMailSender javaMailSender = mailSenderProvider.getOn(ctx);

            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, UTF_8.name());
            StringBuilder sbLogMessage = new StringBuilder();
            sbLogMessage.append("Sending email");


            //FROM
            boolean isCustomSender = StringUtil.hasLength(customSenderEmail);
            if (!isCustomSender) {
                //sender je klasicky z ini (mail od knihovny ctenari) -> nic neresime, nastavime from
                @Nullable String replyToAddressForMailsFromLibrary = StringUtil.notBlankTrimmedString(replyToAddressForMailsFromAdminProvider.getOn(ctx));

                setFrom(physicalSenderEmail, physicalSenderName, helper, sbLogMessage);
                if (StringUtil.hasLength(replyToAddressForMailsFromLibrary)) {
                    setReplyTo(replyToAddressForMailsFromLibrary, physicalSenderName, helper, sbLogMessage);
                }

            } else if (useSenderAndFromInPlaceOfFromAndReplyToProvider.getOn(ctx)) {
                //sender je jiny nez z ini (mail od ctenare knihovne) - pouziti sender (jako realna adresa) + from (jako odesilatel)
                setSender(physicalSenderEmail, message, sbLogMessage);
                setFrom(customSenderEmail, mail.senderName(), helper, sbLogMessage);

            } else {
                //sender je jiny nez z ini (mail od ctenare knihovne) - pouziti from (jako realna adresa) + reply to (jako odesilatel)
                setFrom(physicalSenderEmail, physicalSenderName, helper, sbLogMessage);
                setReplyTo(customSenderEmail, mail.senderName(), helper, sbLogMessage);
            }


            //TO
            setTo(recipientsEmails, helper, sbLogMessage);


            //SUBJECT
            setSubject(mail.subject(), helper, sbLogMessage);


            //ATTACHMENTS
            setAttachments(mail.attachments(), helper, sbLogMessage);


            //BODY
            setBody(mail.body(), helper, sbLogMessage);


            if (log.isInfoEnabled()) {
                log.info(sbLogMessage.toString());
            }


//            javaMailSender.send(message);

        } catch (RuntimeException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    private void setFrom(@NonNull String from, @Nullable String personal, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException, UnsupportedEncodingException {
        if (personal != null) {
            helper.setFrom(from, personal);
        } else {
            helper.setFrom(from);
        }
        sbLogMessage.append(", from: ").append(from);
        sbLogMessage.append(" (personal: ").append(personal).append(")");
    }

    private void setReplyTo(@NonNull String email, @Nullable String personal, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException, UnsupportedEncodingException {
        if (personal != null) {
            helper.setReplyTo(email, personal);
        } else {
            helper.setReplyTo(email);
        }
        sbLogMessage.append(", replyTo: ").append(email);
        sbLogMessage.append(" (personal: ").append(personal).append(")");
    }

    private void setSender(@NonNull String email, @NonNull MimeMessage message, @NonNull StringBuilder sbLogMessage) throws MessagingException {
        message.setSender(parseAddress(email));
        sbLogMessage.append(", physical sender: ").append(email);
    }

    private void setTo(@NonNull Set<String> recipientsEmails, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException {
        helper.setTo(recipientsEmails.toArray(new String[0]));
        sbLogMessage.append(", to: ").append(StringUtil.listToString(recipientsEmails, ","));
    }

    private void setSubject(@NonNull String subject, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException {
        helper.setSubject(subject);
        sbLogMessage.append(", subject: \"").append(subject).append("\"");
    }

    private void setBody(@Nullable String body, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException {
        if (body != null) {
            helper.setText(body, true);
            sbLogMessage.append(", body:\n").append(body);
        } else {
            helper.setText("", false);
            sbLogMessage.append(", empty body");
        }
    }

    private void setAttachments(@NonNull List<File> attachments, @NonNull MimeMessageHelper helper, @NonNull StringBuilder sbLogMessage) throws MessagingException {
        for (File attachment : attachments) {
            helper.addAttachment(attachment.getName(), attachment);
        }
        sbLogMessage.append(", ").append(attachments.size()).append(" attachments");
    }


    private InternetAddress parseAddress(String address) throws MessagingException {
        InternetAddress[] parsed = InternetAddress.parse(address);
        if (parsed.length != 1) {
            throw new AddressException("Illegal address", address);
        }
        InternetAddress raw = parsed[0];
        try {
            return new InternetAddress(raw.getAddress(), raw.getPersonal(), UTF_8.name());
        } catch (UnsupportedEncodingException ex) {
            throw new MessagingException("Failed to parse embedded personal name to correct encoding", ex);
        }
    }

}
