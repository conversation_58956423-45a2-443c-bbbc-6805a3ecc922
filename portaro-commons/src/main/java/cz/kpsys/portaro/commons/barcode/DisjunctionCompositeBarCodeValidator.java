package cz.kpsys.portaro.commons.barcode;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DisjunctionCompositeBarCodeValidator implements BarCodeValidator {

    @NonNull List<BarCodeValidator> validators;

    @Override
    public String getTypeName() {
        return validators.stream().map(BarCodeValidator::getTypeName).collect(Collectors.joining(","));
    }

    @Override
    public Text getTypeText() {
        return MultiText.ofTexts(ListUtil.convert(validators, BarCodeValidator::getTypeText))
                .withDelimiter(", ", MultiText.ofTexts(" {} ", Texts.ofMessageCoded("commons.or")));
    }

    @Override
    public boolean isValid(@NonNull String source) {
        return validators.stream().anyMatch(validator -> validator.isValid(source));
    }

    @Override
    public void throwIfInvalid(@NonNull String source) throws InvalidBarCodeException {
        if (!isValid(source)) {
            throw createException(source);
        }
    }

    @Override
    public String getCore(@NonNull String source) throws InvalidBarCodeException {
        return validators
                .stream()
                .filter(validator -> validator.isValid(source))
                .findFirst()
                .orElseThrow(() -> createException(source))
                .getCore(source);
    }

    @Override
    public int getMaximumLength() {
        return validators.stream().mapToInt(BarCodeValidator::getMaximumLength).max().orElseThrow();
    }

    @Override
    public int getCoreLength() {
        return validators.stream().mapToInt(BarCodeValidator::getCoreLength).findFirst().orElseThrow();
    }

    @Override
    public String getEditorValidationPattern() {
        return String.join("|", ListUtil.convert(validators, BarCodeValidator::getEditorValidationPattern));
    }

    @Override
    public Stream<String> streamAllValidVariants(@NonNull String source) {
        return validators.stream()
                .flatMap(validator -> validator.streamAllValidVariants(source))
                .distinct();
    }

    @Override
    public Stream<String> streamAllValidCores(@NonNull String source) {
        return validators.stream()
                .flatMap(validator -> validator.streamAllValidCores(source))
                .distinct();
    }

    private InvalidBarCodeException createException(@NonNull String source) {
        return new InvalidBarCodeException("Given string '%s' is not valid barcode with any of %s".formatted(source, StringUtil.listToHumanReadableString(ListUtil.convert(validators, BarCodeValidator::getTypeName), " or ")));
    }

    @Override
    public String toString() {
        return "%s barcode validator".formatted(getTypeName());
    }
}
