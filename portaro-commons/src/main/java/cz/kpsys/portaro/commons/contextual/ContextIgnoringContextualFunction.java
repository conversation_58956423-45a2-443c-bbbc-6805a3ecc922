package cz.kpsys.portaro.commons.contextual;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ContextIgnoringContextualFunction<T, CTX, R> implements ContextualFunction<T, CTX, R> {

    @NonNull Function<@NonNull T, R> function;

    public static <T, CTX, R> ContextIgnoringContextualFunction<T, CTX, R> ofNullableReturningFunction(@NonNull Function<@NonNull T, R> function) {
        return new ContextIgnoringContextualFunction<T, CTX, R>(function);
    }

    public static <T, CTX, R> ContextIgnoringContextualFunction<T, CTX, R> ofNonNullReturningFunction(@NonNull Function<@NonNull T, @NonNull R> function) {
        return new ContextIgnoringContextualFunction<T, CTX, R>(function);
    }

    @Override
    public R getOn(@NonNull T input, CTX ignoredCtx) {
        return function.apply(input);
    }
}
