package cz.kpsys.portaro.commons.web;

import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

@FunctionalInterface
public interface WebResolver<V> {

    default V resolve(@NonNull HttpServletRequest request) throws RuntimeException {
        return tryResolve(request)
                .orElseThrow(() -> new RuntimeException("Cannot resolve object from http request (by resolver %s)".formatted(this)));
    }

    Optional<V> tryResolve(@NonNull HttpServletRequest request);

    default <C> WebResolver<C> thenConverting(@NonNull Converter<V, C> converter) {
        return new ConvertingWebResolver<>(this, converter);
    }
}
