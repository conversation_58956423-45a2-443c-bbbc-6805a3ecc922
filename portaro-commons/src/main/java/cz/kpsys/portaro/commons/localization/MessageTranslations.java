package cz.kpsys.portaro.commons.localization;

import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MessageTranslations {

    @NotEmpty @NonNull Map<String, MessageFormat> map;

    public static MessageTranslations ofStringMessages(@NotEmpty @NonNull Map<Locale, String> stringTranslations) {
        Map<String, MessageFormat> translations = new HashMap<>(stringTranslations.size());
        for (Map.Entry<Locale, String> localeStringEntry : stringTranslations.entrySet()) {
            translations.put(localeStringEntry.getKey().getLanguage(), stringToMessageFormat(localeStringEntry.getValue(), localeStringEntry.getKey()));
        }
        return new MessageTranslations(translations);
    }

    public MessageTranslations add(@NonNull Locale locale, @NonNull String translation) {
        map.put(locale.getLanguage(), new MessageFormat(translation, locale));
        return this;
    }

    public Optional<String> getMessage(@NonNull Locale locale, @NonNull FallbackLocaleResolver fallbackLocaleResolver, @NonNull List<Object> args) {
        return findMessageFormat(locale, fallbackLocaleResolver)
                .map(messageFormat -> messageFormat.format(args.toArray()));
    }

    private Optional<MessageFormat> findMessageFormat(@NonNull Locale locale, @NonNull FallbackLocaleResolver fallbackLocaleResolver) {
        MessageFormat translation = getTranslationFromMap(locale);
        if (translation != null) {
            return Optional.of(translation);
        }

        List<Locale> fallbackLocales = fallbackLocaleResolver.getFallbacksFor(locale);
        for (Locale fallbackLocale : fallbackLocales) {
            translation = getTranslationFromMap(fallbackLocale);
            if (translation != null) {
                return Optional.of(translation);
            }
        }

        return Optional.empty();
    }

    private MessageFormat getTranslationFromMap(Locale locale) {
        return map.get(locale.getLanguage());
    }

    @Override
    public String toString() {
        String text = map.entrySet().stream()
                .map(entry -> "%s=%s".formatted(entry.getKey(), entry.getValue() == null ? null : entry.getValue().toPattern()))
                .collect(Collectors.joining(","));
        return "Translations{%s}".formatted(text);
    }

    public static MessageFormat stringToMessageFormat(String msg, Locale locale) {
        if (msg == null) {
            return null;
        }
        msg = msg.replace("'", "''"); //ke kazde uvozovce pridame druhou (jedna uvozovka escapuje) - viz MessageFormat
        return new MessageFormat(msg, locale);
    }

}
