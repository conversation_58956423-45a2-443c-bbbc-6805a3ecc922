package cz.kpsys.portaro.commons.mail;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.io.File;
import java.util.List;
import java.util.Set;

@With
public record Mail<CTX>(

        @NullableNotBlank
        String senderName,

        @NullableNotBlank
        String senderEmail,

        @NotEmpty
        @NonNull
        Set<String> recipientsEmails,

        @NonNull
        String subject,

        @Nullable
        String body,

        @NonNull
        List<File> attachments,

        @NonNull
        CTX ctx

) {}
