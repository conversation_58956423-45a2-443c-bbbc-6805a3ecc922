package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Sequence;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.*;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class InMemoryRepository<E, ID> implements AllProvidingRepository<E, ID>, ByIdOptLoadableRepository<E, ID>, ByIdDeleter<ID> {

    @NonNull Map<ID, E> memory = new HashMap<>();

    @NonNull Function<E, ID> itemIdGetter;

    public static <E extends Identified<UUID>> InMemoryRepository<List<E>, Integer> ofList() {
        return new InMemoryRepository<>(System::identityHashCode);
    }

    public static <E extends Identified<ID>, ID> InMemoryRepository<E, ID> ofIdentified() {
        return new InMemoryRepository<>(Identified::getId);
    }

    public static <E, ID> InMemoryRepository<E, ID> of(@NonNull Function<E, ID> itemIdGetter) {
        return new InMemoryRepository<>(itemIdGetter);
    }

    @Override
    public E getById(@NonNull ID id) {
        E e = memory.get(id);
        if (e == null) {
            throw new EmptyResultDataAccessException(String.format("Memory does not contain item with id %s", id), 1);
        }
        log.debug("Object {} with id {} found in memory", e, itemIdGetter.apply(e));
        return e;
    }

    @Override
    public List<E> getAllByIds(@NonNull List<ID> ids) {
        return ListUtil.convert(ids, this::getById, false, false, false);
    }

    @Override
    public @NonNull E save(@NonNull E e) {
        ID id = itemIdGetter.apply(e);

        if (id == null && e instanceof IdSettable) {
            ((IdSettable) e).setId(Sequence.getNextNumber());
            id = itemIdGetter.apply(e);
        }

        memory.put(id, e);
        log.debug("Object {} with id {} saved to memory ({})", e, id, e);
        return e;
    }

    @Override
    public void deleteById(@NonNull ID id) {
        E e = memory.remove(id);
        if (e == null) {
            log.debug("Memory does not contain object with id {}, nothing to delete", id);
        } else {
            log.debug("Object {} with id {} deleted from memory", e, id);
        }
    }

    @Override
    public List<E> getAll() {
        return new ArrayList<>(memory.values());
    }

    @Override
    public void delete(E object) {
        this.deleteById(itemIdGetter.apply(object));
    }

}
