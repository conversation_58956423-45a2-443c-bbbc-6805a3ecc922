package cz.kpsys.portaro.commons.validation;


import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.NonNull;

public record Validity(

        @NonNull
        String property,

        @NonNull
        String type,

        boolean valid,

        @NonNull
        Text message
) {

    public static final String TYPE_FORMAT = "format";
    public static final String TYPE_UNIQUENESS = "unique";
    public static final String TYPE_MIN_LENGTH = "minlength";
    public static final String TYPE_MAX_LENGTH = "maxlength";
    public static final String TYPE_EMPTY_AS_VALID = "emptyAsValid";
    public static final String TYPE_EMPTY = "empty";
    public static final String TYPE_PASSWORD_MATCH = "passwordmatch";

    public static Validity ofEmptyAsValid(String fieldName) {
        return new Validity(fieldName, TYPE_EMPTY_AS_VALID, true, Texts.ofEmpty());
    }

}
