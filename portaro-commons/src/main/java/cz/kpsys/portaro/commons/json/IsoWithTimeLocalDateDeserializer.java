package cz.kpsys.portaro.commons.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import cz.kpsys.portaro.commons.util.DateUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * Deprecated - pass local date strings as "YYYY-MM-DD"
 */
@Deprecated
public class IsoWithTimeLocalDateDeserializer extends JsonDeserializer<LocalDate> {

    public static final ZoneId CZECH_TIME_ZONE = ZoneId.of("Europe/Prague");

    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        Instant instant = Instant.from(DateTimeFormatter.ISO_OFFSET_DATE_TIME.parse(p.getText()));
        return DateUtils.instantToLocalDate(instant, CZECH_TIME_ZONE);
    }
}