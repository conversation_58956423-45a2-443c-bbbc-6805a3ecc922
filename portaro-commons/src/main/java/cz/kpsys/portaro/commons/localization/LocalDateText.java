package cz.kpsys.portaro.commons.localization;

import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.util.Locale;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
public class LocalDateText implements Text {

    @NonNull LocalDate localDate;
    @NonNull LocaledDateFormatterResolver localedDateFormatterResolver;

    public static LocalDateText createWithoutTime(@NonNull LocalDate date) {
        return new LocalDateText(date, LocaledDateFormatterResolver.createWithoutTime());
    }

    @Override
    public String toString() {
        return "DateText %s".formatted(localDate);
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public <CTX> Optional<String> tryLocalize(Translator<CTX> translator, CTX ctx, Locale locale) {
        String formattedDate = localedDateFormatterResolver.getFormatter(locale).format(localDate);
        return Optional.of(formattedDate);
    }

}
