package cz.kpsys.portaro.commons.contextual;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Objects;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ContextualProviderToPredicate<CTX, V> implements Predicate<CTX> {

    @NonNull ContextualProvider<CTX, V> delegate;
    @NonNull Predicate<V> checker;

    public static <CTX, V> ContextualProviderToPredicate<CTX, V> valueNotNull(@NonNull ContextualProvider<CTX, V> delegate) {
        return new ContextualProviderToPredicate<>(delegate, Objects::nonNull);
    }

    @Override
    public boolean test(CTX ctx) {
        V value = delegate.getOn(ctx);
        return checker.test(value);
    }

    @Override
    public String toString() {
        return "ContextualProviderToPredicate{validator=%s, delegate=%s}".formatted(checker, delegate);
    }


}
