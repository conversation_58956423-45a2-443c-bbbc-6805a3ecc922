package cz.kpsys.portaro.commons.net;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandler;
import java.net.URLStreamHandlerFactory;
import java.util.Collection;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ResourceLoaderUrlStreamHandlerFactory implements URLStreamHandlerFactory {

    @NonNull ResourceLoader resourceLoader;
    @NonNull Collection<String> supportedProtocols;

    @Override
    public URLStreamHandler createURLStreamHandler(String protocol) {
        if (supportedProtocols.contains(protocol)) {
            return new URLStreamHandler() {

                @Override
                protected URLConnection openConnection(URL url) {
                    Resource resource = resourceLoader.getResource(url.toString());
                    return new ResourceUrlConnection(url, resource);
                }

            };
        }
        return null;
    }

}
