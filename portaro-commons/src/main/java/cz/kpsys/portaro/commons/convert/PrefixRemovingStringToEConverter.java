package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PrefixRemovingStringToEConverter<TARGET> implements Function<String, TARGET> {

    @NonNull String prefixToRemove;
    @NonNull Function<String, ? extends TARGET> removedPrefixConverter;

    @Override
    public TARGET apply(String source) {
        String removedPrefixSource = StringUtil.removePrefix(source, prefixToRemove);
        return removedPrefixConverter.apply(removedPrefixSource);
    }

}
