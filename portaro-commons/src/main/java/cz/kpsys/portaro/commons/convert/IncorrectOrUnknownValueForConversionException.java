package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import org.springframework.core.convert.converter.Converter;

/**
 * <AUTHOR>
 */
public class IncorrectOrUnknownValueForConversionException extends RuntimeException {

    public IncorrectOrUnknownValueForConversionException(String value, Class<?> fromType, Class<?> toType) {
        super(String.format("Incorrect or unknown value \"%s\" for conversion from %s to %s",
                            value,
                            fromType == null ? "??" : fromType.getSimpleName(),
                            toType == null ? "??" : toType.getSimpleName()));
    }

    public IncorrectOrUnknownValueForConversionException(String value, Converter<?, ?> converter) {
        this(value, getGenericType(converter, 0), getGenericType(converter, 1));
    }

    private static Class<Object> getGenericType(Converter<?, ?> converter, int idx) {
        try {
            return ObjectUtil.getGenericType(converter.getClass(), idx);
        } catch (Throwable t) {
            return null;
        }
    }

}
