package cz.kpsys.portaro.commons.web;

import cz.kpsys.portaro.commons.io.AbsoluteRangeBytes;
import cz.kpsys.portaro.commons.io.AbsoluteRangeBytesWithLength;
import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.experimental.StandardException;

import java.util.Optional;

public class ContentRangeUtils {

    public static AbsoluteRangeBytesWithLength parse(String contentRangeHeader) throws ParseException {
        if (StringUtil.isNullOrBlank(contentRangeHeader)) {
            return null;
        }

        String type = contentRangeHeader.substring(0, 6);
        if (! "bytes ".equalsIgnoreCase(type)) {
            throw new ParseException("Unknown Content-Range unit: %s".formatted(contentRangeHeader));
        }

        String[] rangeSize = contentRangeHeader.substring(6).split("/");
        if (rangeSize.length != 2) {
            throw new ParseException("Malformed Content-Range header: %s".formatted(contentRangeHeader));
        }

        return new AbsoluteRangeBytesWithLength(
                parseRange(rangeSize[0]),
                parseTotalLength(rangeSize[1])
        );
    }

    private static Optional<AbsoluteRangeBytes> parseRange(final String rangeStr) {
        if (rangeStr.equals("*")) {
            return Optional.empty();
        }
        try {
            BytesRange rng = BytesRange.fromRangeString(rangeStr);
            if (rng.isAbsolute()) {
                return Optional.of(new AbsoluteRangeBytes(rng.from().get(), rng.to().get()));
            } else {
                throw new ParseException("Invalid range: %s".formatted(rangeStr));
            }
        } catch (BytesRange.ParseException e) {
            throw new ParseException(e);
        }
    }

    private static Optional<Long> parseTotalLength(String lengthStr) {
        if (lengthStr.equals("*")) {
            return Optional.empty();
        }
        try {
            Optional<Long> totalLength = Optional.of(Long.parseLong(lengthStr));
            if (totalLength.get() < 0) {
                throw new ParseException("Total length cannot be negative");
            }
            return totalLength;
        } catch (NumberFormatException e) {
            throw new ParseException(e);
        }
    }

    public static String serialize(AbsoluteRangeBytesWithLength range) {
        StringBuilder sb = new StringBuilder("bytes ");
        range.range().map(AbsoluteRangeBytes::toRangeString).ifPresentOrElse(
                sb::append, () -> sb.append("*"));
        sb.append("/");
        range.totalLength().ifPresentOrElse(sb::append, () -> sb.append("*"));
        return sb.toString();
    }

    @StandardException
    public static class ParseException extends RuntimeException {
    }

}
