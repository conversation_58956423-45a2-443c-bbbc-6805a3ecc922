package cz.kpsys.portaro.token;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class InvalidTokenException extends RuntimeException implements UserFriendlyException {

    @NonNull Text text = Texts.ofNative("Neplatný token");

    public InvalidTokenException(Throwable cause) {
        super("Invalid token: %s".formatted(cause.getMessage()), cause);
    }

    public InvalidTokenException(String message) {
        super("Invalid token: %s".formatted(message));
    }

}
