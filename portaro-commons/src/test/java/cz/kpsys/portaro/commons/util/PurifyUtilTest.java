package cz.kpsys.portaro.commons.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

@Tag("ci")
@Tag("unit")
public class PurifyUtilTest {

    @ParameterizedTest
    @CsvSource(value = {
            "'antologie,', antologie",
            "'an?to,l;o|gie*', 'an?to,l;o|gie'",
            "'an?to,l;o|gie:', 'an?to,l;o|gie'",
            "'an?to,l;o|gie.', 'an?to,l;o|gie.'",
            "'', ''",
            "'.', '.'",
            "',', ''",
    })
    public void testAuthorPurifier(String input, String expected) {
        Assertions.assertEquals(expected, StringPurifier.ofMarcFieldContentExceptDot().purify(input));
    }
}
