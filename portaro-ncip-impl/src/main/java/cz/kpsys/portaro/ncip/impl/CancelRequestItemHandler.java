package cz.kpsys.portaro.ncip.impl;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.LoanState;
import cz.kpsys.portaro.loan.cancellation.CancellationService;
import cz.kpsys.portaro.loan.cancellation.NotCancellableException;
import cz.kpsys.portaro.ncip.NcipException;
import cz.kpsys.portaro.ncip.NcipRequest;
import cz.kpsys.portaro.ncip.handler.ProblemTypes;
import cz.kpsys.portaro.ncip.handler.UnwrappedHandler;
import cz.kpsys.portaro.ncip.impl.convert.NcipUserIdToUserConverter;
import cz.kpsys.portaro.ncip.schema.*;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.user.NoConcreteUser;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CancelRequestItemHandler implements UnwrappedHandler<CancelRequestItem, CancelRequestItemResponse> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Loan> loanSearchLoader;
    @NonNull Converter<ItemId, Exemplar> ncipItemIdToExemplarConverter;
    @NonNull Converter<RequestId, Loan> ncipRequestIdToloanConverter;
    @NonNull CancellationService cancellationService;
    @NonNull NcipUserIdToUserConverter ncipUserIdToUserConverter;


    @Override
    public CancelRequestItemResponse handleRequestObject(NcipRequest<CancelRequestItem> request) {
        Loan loanRequestToCancel = getLoanRequestFromRequest(request);
        UserAuthentication currentAuth = getCurrentAuth(request);

        cancelLoanRequest(loanRequestToCancel, currentAuth, request.getDepartment());

        CancelRequestItemResponse response = new CancelRequestItemResponse();
        response.getContent().addAll(request.getMessage().getContent());
        return response;
    }

    private UserAuthentication getCurrentAuth(NcipRequest<CancelRequestItem> request) {
        return findUserInRequest(request)
                .map((Function<User, UserAuthentication>) CurrentAuth::createWithAbsoluteAuthenticity)
                .orElseGet(() -> CurrentAuth.createAsAnonymous(NoConcreteUser.anonymousUser()));
    }

    private void cancelLoanRequest(@NonNull Loan loanRequestToCancel, @NonNull UserAuthentication currentAuth, @NonNull Department currentDepartment) {
        try {
            cancellationService.cancelWaitingLoan(loanRequestToCancel, currentDepartment, currentAuth);
        } catch (NotCancellableException e) {
            throw new NcipException(e, ProblemTypes.CANCEL_REQUEST_ITEM__CANNOT_BE_CANCELLED, Texts.ofNative("Request is in process and cannot be already cancelled"));
        } catch (Exception e) {
            if (e instanceof UserFriendlyException) {
                throw new NcipException(e, ProblemTypes.CANCEL_REQUEST_ITEM__PROBLEM, ((UserFriendlyException) e).getText());
            }
            throw e;
        }
    }

    private Loan getLoanRequestFromRequest(NcipRequest<CancelRequestItem> request) {
        return request.getMessage().getContent().stream()
                .filter(RequestId.class::isInstance)
                .map(RequestId.class::cast)
                .map(requestId -> Objects.requireNonNull(ncipRequestIdToloanConverter.convert(requestId)))
                .findFirst()
                .orElseGet(() -> getLoanRequestToCancelByLenderAndExemplar(request));
    }

    private Loan getLoanRequestToCancelByLenderAndExemplar(NcipRequest<CancelRequestItem> request) {
        Optional<User> lender = findUserInRequest(request);
        Optional<Exemplar> exemplar = findExemplarInRequest(request);
        if (lender.isEmpty() || exemplar.isEmpty()) { //jinak je to chybny vstup
            throw new NcipException(ProblemTypes.INVALID_REQUEST_FORMAT, "Expected both UserId and ItemId elements");
        }
        return loanSearchLoader.getOne("Last loan request", p -> {
            p.set(LoanConstants.SearchParams.LENDER, List.of(lender.get()));
            p.set(RecordConstants.SearchParams.RECORD, List.of(exemplar.get().getRecordId()));
            p.set(LoanConstants.SearchParams.LOAN_STATE, LoanState.allWaiting());
        });
    }

    private Optional<User> findUserInRequest(NcipRequest<CancelRequestItem> request) {
        return request.getMessage().getContent().stream()
                .filter(UserId.class::isInstance)
                .map(UserId.class::cast)
                .map(userId -> Objects.requireNonNull(ncipUserIdToUserConverter.convert(request.getDepartment(), userId)))
                .findFirst();
    }

    private Optional<Exemplar> findExemplarInRequest(NcipRequest<CancelRequestItem> request) {
        return request.getMessage().getContent().stream()
                .filter(ItemId.class::isInstance)
                .map(ItemId.class::cast)
                .map(itemId -> Objects.requireNonNull(ncipItemIdToExemplarConverter.convert(itemId)))
                .findFirst();
    }
}
