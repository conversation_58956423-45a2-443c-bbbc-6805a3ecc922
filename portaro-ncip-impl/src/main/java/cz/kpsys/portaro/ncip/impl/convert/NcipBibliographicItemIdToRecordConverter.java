package cz.kpsys.portaro.ncip.impl.convert;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.ncip.handler.ProblemTypes;
import cz.kpsys.portaro.ncip.impl.NcipUtil;
import cz.kpsys.portaro.ncip.schema.BibliographicItemId;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class NcipBibliographicItemIdToRecordConverter implements Converter<BibliographicItemId, Record> {

    @NonNull Provider<@NonNull Boolean> useRecordIdEnabledProvider;
    @NonNull IdAndIdsLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull ByIdLoadable<Record, UUID> nonDetailedDocumentLoader;

    @Override
    public Record convert(@NonNull BibliographicItemId bibliographicItemId) {
        String bibliographicItemIdentifier = bibliographicItemId.getBibliographicItemIdentifier();

        if (useRecordIdEnabledProvider.get()) {
            UUID recordId = StringToUuidConverter.INSTANCE.convert(bibliographicItemIdentifier);
            return nonDetailedDocumentLoader.getById(recordId);
        }

        int documentKindedId = NcipUtil.parseInt(bibliographicItemIdentifier, ProblemTypes.INVALID_ITEM_ID_FORMAT);
        return nonDetailedDocumentByKindedIdLoader.getById(documentKindedId);
    }
}
