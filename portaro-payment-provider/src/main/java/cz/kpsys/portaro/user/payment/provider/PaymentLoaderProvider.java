package cz.kpsys.portaro.user.payment.provider;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PaymentLoaderProvider {

    @NonNull Map<String, Class<?>> providerNamesToPaymentTypes;
    @NonNull Map<Class<? extends Payment>, ByIdLoadable<? extends Payment, Integer>> paymentTypesToLoaders;


    public ByIdLoadable<? extends Payment, Integer> getByProviderName(@NonNull String providerName) {
        Class<?> desiredPaymentType = providerNamesToPaymentTypes.get(providerName);
        if (desiredPaymentType == null) {
            throw new ItemNotFoundException(Class.class, providerName, Texts.ofNative("Cannot find byIdLoader - unknown payment provider " + providerName));
        }

        ByIdLoadable<? extends Payment, Integer> byIdLoadable = paymentTypesToLoaders.get(desiredPaymentType);
        if (byIdLoadable == null) {
            throw new ItemNotFoundException(ByIdLoadable.class, desiredPaymentType, Texts.ofNative(String.format("Cannot find byIdLoader for provider %s - unknown payment type %s", providerName, desiredPaymentType)));
        }
        return byIdLoadable;
    }


    @Override
    public String toString() {
        return "PaymentLoaderProvider dispatching to one of %s".formatted(paymentTypesToLoaders.values());
    }
}
