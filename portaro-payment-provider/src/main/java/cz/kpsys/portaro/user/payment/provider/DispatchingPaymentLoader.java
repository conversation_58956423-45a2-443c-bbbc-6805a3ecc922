package cz.kpsys.portaro.user.payment.provider;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * PaymentLoader which returns always concrete instance of payment (GopayPament, GpwebpayPament or Payment)
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DispatchingPaymentLoader implements ByIdLoadable<Payment, Integer> {

    @NonNull ByIdLoadable<Payment, Integer> purePaymentLoader;
    @NonNull PaymentLoaderProvider paymentLoaderProvider;

    @Override
    public Payment getById(@NonNull Integer id) {
        Payment purePayment = purePaymentLoader.getById(id);
        ByIdLoadable<? extends Payment, Integer> concretePaymentLoader = paymentLoaderProvider.getByProviderName(purePayment.getProvider());
        return concretePaymentLoader.getById(id);
    }


    @Override
    public String toString() {
        return "DispatchingPaymentLoader to %s".formatted(paymentLoaderProvider);
    }
}
