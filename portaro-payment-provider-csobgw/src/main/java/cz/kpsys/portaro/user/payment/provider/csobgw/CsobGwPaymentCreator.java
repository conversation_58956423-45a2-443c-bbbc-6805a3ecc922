package cz.kpsys.portaro.user.payment.provider.csobgw;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.payment.PayCommand;
import cz.kpsys.portaro.payment.Payment;
import cz.kpsys.portaro.payment.PaymentBasicCreator;
import cz.kpsys.portaro.payment.PaymentSaveCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CsobGwPaymentCreator {

    @NonNull PaymentBasicCreator paymentCreator;
    @NonNull Saver<PaymentSaveCommand<CsobGwPayment>, CsobGwPayment> csobGwPaymentSaver;
    @NonNull TransactionTemplate transactionTemplate;

    public CsobGwPayment create(@NonNull PayCommand command, @NonNull String csobGwOrderNumber) {
        return transactionTemplate.execute(_ -> {
            Payment payment = paymentCreator.createInstance(command, null);

            CsobGwPayment csobGwPayment = new CsobGwPayment(payment, csobGwOrderNumber);
            csobGwPaymentSaver.save(new PaymentSaveCommand<>(csobGwPayment, command.ctx(), command.currentAuth()));

            return csobGwPayment;
        });
    }
}
