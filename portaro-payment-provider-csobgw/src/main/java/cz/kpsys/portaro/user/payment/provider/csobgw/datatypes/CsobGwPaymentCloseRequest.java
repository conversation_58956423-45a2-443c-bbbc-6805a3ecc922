package cz.kpsys.portaro.user.payment.provider.csobgw.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

@Value
public class CsobGwPaymentCloseRequest {

    @JsonProperty("merchantId")
    @NonNull
    String merchantId;

    @JsonProperty("payId")
    @NonNull
    String payId;

    @JsonProperty("dttm")
    @NonNull
    String timestamp;

    @JsonProperty("totalAmount")
    @Nullable
    Integer totalAmount;

    @JsonProperty("signature")
    @NonNull
    String signature;
}
