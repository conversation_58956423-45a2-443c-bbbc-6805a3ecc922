package cz.kpsys.portaro.ext.cpk.impl;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

public record CpkDatasource(

        @NonNull AllValuesProvider<Fond> supportedFondsProvider,
        @NonNull Set<String> supportedSubkinds

) implements Datasource {

    public static final String DATASOURCE_EXTERNAL_CPK_GROUP = DATASOURCE_EXTERNAL_GROUP + "cpk-";
    public static final String DATASOURCE_EXTERNAL_CPK_DEFAULT = DATASOURCE_EXTERNAL_CPK_GROUP + "default";

    @NonNull
    @Override
    public String id() {
        return DATASOURCE_EXTERNAL_CPK_DEFAULT;
    }

    @NonNull
    @Override
    public Text text() {
        return Texts.ofNative("knihovny.cz api");
    }

    @Override
    public Collection<Fond> supportedFonds() {
        return supportedFondsProvider.getAll();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, CpkDatasource.class);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id());
    }
}
