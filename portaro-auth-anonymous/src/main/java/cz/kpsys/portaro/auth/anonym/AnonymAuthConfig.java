package cz.kpsys.portaro.auth.anonym;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.Authenticity;
import cz.kpsys.portaro.auth.current.resolver.CompositeUserAuthenticationAuthenticationResolver;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.authentication.AnonymousAuthenticationFilter;

import java.util.function.Consumer;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AnonymAuthConfig {

    @NonNull WebResolver<Department> currentDepartmentWebResolver;
    @NonNull CompositeUserAuthenticationAuthenticationResolver compositeUserAuthenticationAuthenticationResolver;
    @NonNull AuthenticationHolder authenticationHolder;

    @Bean
    public Consumer<HttpSecurity> anonymAuthHttpSecurityConfigurer() {
        return http -> http.addFilterAt(anonymAuthenticationFilter(), AnonymousAuthenticationFilter.class);
    }

    private AnonymCreatingAuthenticationFilter anonymAuthenticationFilter() {
        AnonymAuthenticator authenticator = new AnonymAuthenticator();
        return new AnonymCreatingAuthenticationFilter(authenticator, currentDepartmentWebResolver, authenticationHolder);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerAuthenticationResolver() {
        compositeUserAuthenticationAuthenticationResolver
                .withAuthenticatedUserResolverOf(AnonymSuccessAuthentication.class, AnonymSuccessAuthentication::getActiveUser)
                .withAdditionalAuthoritiesResolverOf(AnonymSuccessAuthentication.class, AnonymSuccessAuthentication::getAuthorities)
                .withAuthenticityResolverOf(AnonymSuccessAuthentication.class, auth -> Authenticity.NONE);
    }

}
