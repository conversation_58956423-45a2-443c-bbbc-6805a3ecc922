dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-user"))

    implementation("org.springframework.security:spring-security-config:6.+")
    implementation("org.springframework.security:spring-security-web:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
}
