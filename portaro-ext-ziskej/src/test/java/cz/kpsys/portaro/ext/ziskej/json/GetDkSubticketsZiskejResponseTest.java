package cz.kpsys.portaro.ext.ziskej.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.ext.ziskej.ZiskejClientObjectMapperFactory;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@Tag("ci")
@Tag("unit")
public class GetDkSubticketsZiskejResponseTest {

    private static ObjectMapper objectMapper() {
        return new ZiskejClientObjectMapperFactory().get();
    }

    @Test
    public void shouldDeserialize() throws JsonProcessingException {
        String json = """
                {
                  "subtickets": [
                    {
                      "back_date": null,
                      "is_open": true,
                      "refuse_reason": "",
                      "subticket_url": "https://ziskej-test.techlib.cz/tickets/a0515ca105b6462e/e24e61965b5d4d28",
                      "closed_date": null,
                      "ticket_id": "a0515ca105b6462e",
                      "sent_back_date": null,
                      "status": "queued",
                      "rfid": null,
                      "sent_date": null,
                      "is_snail_mail": true,
                      "fee_dk": 70,
                      "sigla_zk": "fmg002",
                      "cond_reason": "send_date",
                      "cond_a_fee_value": 0,
                      "cond_a_edition_value": "string",
                      "cond_a_send_date_value": "2024-06-10",
                      "cond_a_return_d_value": 0,
                      "accepted_date": null,
                      "cond_accepted_date": "2024-03-15",
                      "count_messages_unread": 0,
                      "barcode": null,
                      "hid": 100543,
                      "bibliographic_reference": "WARE, Ruth; KL\\u016eFOV\\u00c1, Petra. Chata v hor\\u00e1ch. 1. vyd\\u00e1n\\u00ed. 367 stran. ISBN 978-80-7588-390-2.",
                      "status_label": "P\\u0159id\\u011blen\\u00fd",
                      "subticket_id": "e24e61965b5d4d28",
                      "subticket_type": "mvs",
                      "count_messages": 0,
                      "doc_dk_id": null,
                      "doc_id": null,
                      "created_datetime": "2023-12-04T16:22:31+01:00",
                      "updated_datetime": "2023-12-04T16:22:50+01:00"
                    }
                  ]
                }
                """;

        GetDkSubticketsZiskejResponse response = objectMapper().readValue(json, GetDkSubticketsZiskejResponse.class);

        DkSubticketZiskejResponse subticket = response.subtickets().getFirst();
        assertEquals("e24e61965b5d4d28", subticket.subticketId());
        assertEquals("fmg002", subticket.siglaZk());
        assertEquals(100543, subticket.hid());
        assertEquals(SubticketStatusZiskejResponse.QUEUED, subticket.status());
        assertTrue(subticket.open());
        assertEquals(70, subticket.feeDk());
        assertEquals(ConditionReasonZiskejRequestResponse.SEND_DATE, subticket.condReason());
        assertEquals(LocalDate.parse("2024-06-10"), subticket.conditionASendDateValue());
        assertEquals(LocalDate.parse("2024-03-15"), subticket.condAcceptedDate());
        assertNull(subticket.refuseReason());
        assertEquals(Instant.parse("2023-12-04T15:22:31Z"), subticket.createdDatetime());
        assertEquals(Instant.parse("2023-12-04T15:22:50Z"), subticket.updatedDatetime());
    }
}
