package cz.kpsys.portaro.ext.ziskej;

import cz.kpsys.portaro.ext.ziskej.json.GetTicketsZiskejResponse;
import cz.kpsys.portaro.ext.ziskej.json.TicketZiskejResponse;
import cz.kpsys.portaro.ext.ziskej.model.ZiskejTicket;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GetTicketsZiskejResponseToResultConverter implements Converter<GetTicketsZiskejResponse, List<ZiskejTicket>> {

    @NonNull Converter<List<? extends TicketZiskejResponse>, List<ZiskejTicket>> ticketResponsesToZiskejTicketsConverter;

    @NonNull
    @Override
    public List<ZiskejTicket> convert(@NonNull GetTicketsZiskejResponse response) {
        return Objects.requireNonNull(ticketResponsesToZiskejTicketsConverter.convert(response.tickets()));
    }

}
