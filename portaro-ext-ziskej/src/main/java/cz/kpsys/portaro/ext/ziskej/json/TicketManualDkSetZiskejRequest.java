package cz.kpsys.portaro.ext.ziskej.json;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.validation.uppercase.Uppercase;
import lombok.NonNull;

public record TicketManualDkSetZiskejRequest(

        /**
         * <PERSON><PERSON><PERSON>, velk<PERSON><PERSON> pís<PERSON>y.
         */
        @JsonProperty("lb_manual_library")
        @NonNull
        @Uppercase
        String lbManualLibrary,

        /**
         * Poslat nebo vyzvednout. Jednotlivé hodnoty:
         * sent - Poslat
         * pick - Vyzvednout
         */
        @JsonProperty("lb_manual_library_sent")
        @NonNull
        LibrarySentTypeZiskejRequest lbManualLibrarySent

) {}
