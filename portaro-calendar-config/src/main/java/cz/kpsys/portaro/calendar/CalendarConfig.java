package cz.kpsys.portaro.calendar;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.config.SearchLoaderBuilderFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.util.Set;
import java.util.UUID;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CalendarConfig {

    @NonNull SearchLoaderBuilderFactory searchLoaderBuilderFactory;

    @Bean
    public Codebook<CalendarEventLabel, UUID> calendarEventLabelLoader() {
        return CalendarEventLabel.CODEBOOK;
    }

    @Bean
    public Codebook<CalendarEvent, UUID> calendarEventLoader() {
        return new StaticCodebook<>(

                // skolni rok 22/23
                new CalendarEvent(
                        UUID.fromString("b929910c-67b4-4e1e-a124-d8d0838abaa6"),
                        new CalendarEventGroup(UUID.fromString("3b88a3cb-9bf7-40b1-aaaf-4bd5d912f41e"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2022-09-01"), LocalDate.parse("2022-12-20"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("0c8aae30-ff7e-43f3-9763-96a6a4b81fdd"),
                        new CalendarEventGroup(UUID.fromString("485876a7-7038-46ef-aaae-420eb1a3f0ac"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.DAY_OFF, CalendarEventLabel.PUBLIC_HOLIDAY),
                        "vanoce",
                        DateRange.ofStartOfDays(LocalDate.parse("2022-12-24"), LocalDate.parse("2023-01-02"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("2be55fcb-3936-412c-b651-ca158532be91"),
                        new CalendarEventGroup(UUID.fromString("3975026f-4872-4c93-a829-857bd9b77e79"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2022-12-20"), LocalDate.parse("2023-02-13"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("958a6400-148e-4c3b-9286-5b47a71f3bfa"),
                        new CalendarEventGroup(UUID.fromString("84b4adc9-2a7b-40b4-92de-1151736518be"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-02-13"), LocalDate.parse("2023-05-22"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("d36b81b0-565b-49f0-a40e-2a35f230688e"),
                        new CalendarEventGroup(UUID.fromString("8f512025-93bb-4c12-958d-a5576818403c"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-05-22"), LocalDate.parse("2023-07-03"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),

                // skolni rok 23/24
                new CalendarEvent(
                        UUID.fromString("d15a9ef8-7460-4969-b2f6-c1239bf790ac"),
                        new CalendarEventGroup(UUID.fromString("c29519aa-de63-43c2-a830-0b54840584e8"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-09-01"), LocalDate.parse("2023-09-11"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("b732daeb-fdc7-4113-9a8f-7fbaa1ca7679"),
                        new CalendarEventGroup(UUID.fromString("3b88a3cb-9bf7-40b1-aaaf-4bd5d912f41e"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-09-11"), LocalDate.parse("2023-12-11"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("d33a1685-3aeb-4e4f-af16-071a38020c58"),
                        new CalendarEventGroup(UUID.fromString("990f277c-1ba7-4d42-874c-5c75a4e3ef7f"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-12-11"), LocalDate.parse("2024-02-19"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("3eef5898-91a9-435c-8bc9-960472067cf6"),
                        new CalendarEventGroup(UUID.fromString("4e73f1a0-09c6-411c-a542-fb15a95391a7"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.DAY_OFF, CalendarEventLabel.PUBLIC_HOLIDAY),
                        "vanoce",
                        DateRange.ofStartOfDays(LocalDate.parse("2023-12-23"), LocalDate.parse("2024-01-02"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("1094f0ba-afd4-4071-a42e-bdb9a9ed4c03"),
                        new CalendarEventGroup(UUID.fromString("96c64387-4235-4299-be65-33f435dd7660"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-02-19"), LocalDate.parse("2024-05-27"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("c5f42d03-9bd7-43f1-bc89-97792798f7df"),
                        new CalendarEventGroup(UUID.fromString("a4c12252-bb21-48d1-9d2f-35412b5df0f1"), CalendarEventPeriodicity.ANNUALLY),
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-05-27"), LocalDate.parse("2024-07-05"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),

                // skolni rok 24/25
                new CalendarEvent(
                        UUID.fromString("d3bd02ae-0dd5-48c9-a7c6-e019db6357d7"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-09-01"), LocalDate.parse("2024-09-09"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("c697d88e-8de7-4735-9541-b888cfdfca54"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-09-09"), LocalDate.parse("2024-12-09"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("9c124091-bf63-4224-9061-dd2d54eb0a94"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-12-09"), LocalDate.parse("2025-02-17"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("eebfadde-5453-4f7f-aadf-24720007d1e3"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.DAY_OFF, CalendarEventLabel.PUBLIC_HOLIDAY),
                        "vanoce",
                        DateRange.ofStartOfDays(LocalDate.parse("2024-12-23"), LocalDate.parse("2025-01-02"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("8c082ce9-f0e0-46aa-bbfd-f5da3639d790"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-02-17"), LocalDate.parse("2025-05-26"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("5bd43a1f-6a1c-47a0-8d7b-3c876386a28f"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-05-26"), LocalDate.parse("2025-07-05"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),

                // skolni rok 25/26
                new CalendarEvent(
                        UUID.fromString("17b7b498-e2ce-468e-95dc-5c7f59264539"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-09-01"), LocalDate.parse("2025-09-15"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("3f245994-36f3-4da1-9025-d17a5c775b41"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-09-15"), LocalDate.parse("2025-12-15"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("148656d3-1b61-4130-8ca1-5e02c2fc2638"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-12-15"), LocalDate.parse("2026-02-16"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("0e7f6738-7d71-4e24-a9c2-8cf4248a2468"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.DAY_OFF, CalendarEventLabel.PUBLIC_HOLIDAY),
                        "vanoce",
                        DateRange.ofStartOfDays(LocalDate.parse("2025-12-24"), LocalDate.parse("2026-01-02"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("6e41f084-37c7-4c99-b280-bab25b8ac76d"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.SEMESTER),
                        "semestr",
                        DateRange.ofStartOfDays(LocalDate.parse("2026-02-16"), LocalDate.parse("2026-05-25"), CoreConstants.CZECH_TIME_ZONE_ID)
                ),
                new CalendarEvent(
                        UUID.fromString("cb0423cd-2b5d-4201-bb67-d751f64e7ab6"),
                        null,
                        Department.testingRoot(),
                        Set.of(CalendarEventLabel.EXAM),
                        "zkouskove",
                        DateRange.ofStartOfDays(LocalDate.parse("2026-05-25"), LocalDate.parse("2026-07-04"), CoreConstants.CZECH_TIME_ZONE_ID)
                )
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, CalendarEvent> calendarEventSearchLoader() {
        PageSearchLoader<MapBackedParams, CalendarEvent, RangePaging> pageSearchLoader = new CalendarEventInMemorySearchLoader(calendarEventLoader());
        return searchLoaderBuilderFactory.searchLoader(MapBackedParams::createEmpty, pageSearchLoader).build();
    }

}
