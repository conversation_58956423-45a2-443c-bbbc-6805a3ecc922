package cz.kpsys.portaro.oai.provider.handler;

import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.oai.model.GetRecordResponse;
import cz.kpsys.portaro.oai.model.ListIdentifiersResponse;
import cz.kpsys.portaro.oai.model.ListRecordsResponse;
import cz.kpsys.portaro.oai.model.OaiVerbResponse;
import cz.kpsys.portaro.user.NoConcreteUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class EventLoggingVerbHandler<RES extends OaiVerbResponse> implements VerbHandler<RES> {

    @NonNull VerbHandler<RES> target;
    @NonNull Eventer eventer;

    @Override
    public @NonNull RES handle(@NonNull OaiCommand command) {
        Map<String, Object> eventDataBase = Map.of("verb", command.verb().toRequestParam(), "request", command.toString());

        try {
            TimeMeter tm = TimeMeter.start();

            RES result = target.handle(command);

            Map<String, Object> eventData = ListUtil.createNewMapAppending(eventDataBase, "response", createResponse(result, tm.elapsedTimeString()));
            eventer.save(Event.Codes.OAI_SERVER_HANDLING_COMPLETION, CurrentAuth.createAsAnonymous(NoConcreteUser.anonymousUser()), command.department(), eventData);

            return result;

        } catch (Exception e) {
            eventer.save(Event.Codes.OAI_SERVER_HANDLING_ERROR, CurrentAuth.createAsAnonymous(NoConcreteUser.anonymousUser()), command.department(), Event.createErrorObject(e, eventDataBase));
            throw e;
        }
    }

    private String createResponse(RES result, String duration) {
        try {
            String res = switch (result) {
                case GetRecordResponse getRecordResponse -> "OK: %s".formatted(getRecordResponse.record().header().identifier());
                case ListIdentifiersResponse listIdentifiersResponse -> "OK: %s identifiers".formatted(listIdentifiersResponse.headers().size());
                case ListRecordsResponse listRecordsResponse -> "OK: %s records".formatted(listRecordsResponse.records().size());
                default -> "OK";
            };
            return res + " (handled in %s)".formatted(duration);

        } catch (Exception e) {
            log.error("Cannot serialize OAI response for event data", e);
            return "Response serialization error %s".formatted(e.getMessage());
        }
    }

}
