package cz.kpsys.portaro.oai.provider.handler;

import cz.kpsys.portaro.commons.api.ActionRequestMethod;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.oai.provider.OaiProviderController;
import cz.kpsys.portaro.record.export.RecordExport;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

@Form(id = "oaiRequest",
        title = "OAI request",
        footer = """
                <br/>
                <br/>
                /api/oai?verb=ListSets
                <br/>
                /api/oai/{set}?verb=GetRecord&metadataPrefix=marc21&identifier={identifier}
                <br/>
                /api/oai/{set}?verb=ListIdentifiers&metadataPrefix=marc21&from=2023-03-16&until=2023-03-18
                <br/>
                /api/oai/{set}?verb=ListRecords&metadataPrefix=marc21&from=2023-03-16&until=2023-03-18
                """
)
@FormSubmit(path = OaiProviderController.API_PATH + "#{formObject.set == null ? '' : '/' + formObject.set}", method = ActionRequestMethod.GET, asPage = true)
@With
public record OaiRequest(

        @FormPropertyLabel("verb")
        @SingleAcceptableEditor(valuesSourceBean = "oaiVerbRequestLoader", visibleIfSingleValue = true)
        @NotNull
        OaiVerbRequest verb,

        @FormPropertyLabel("set")
        @SingleAcceptableEditor(valuesSourceBean = "oaiRequestAllowedSetsResolver", visibleIfSingleValue = true)
        @Size(min = 1, max = 20)
        @NullableNotBlank
        String set,

        @FormPropertyLabel("metadataPrefix")
        @SingleAcceptableEditor(valuesSourceBean = "oaiMetadataFormatLoader", visibleIfSingleValue = true)
        @Nullable
        OaiMetadataFormat metadataPrefix,

        @FormPropertyLabel("from")
        @TextEditor(editorOptions = @EditorOptions(placeholder = "2023-03-16"))
        @Size(min = 1, max = 20)
        @NullableNotBlank
        String from,

        @FormPropertyLabel("until")
        @TextEditor(editorOptions = @EditorOptions(placeholder = "2023-03-18"))
        @Size(min = 1, max = 20)
        @NullableNotBlank
        String until,

        @FormPropertyLabel("resumptionToken")
        @TextEditor
        @Size(min = 1, max = 1000)
        @NullableNotBlank
        String resumptionToken,

        @FormPropertyLabel("identifier")
        @TextEditor
        @ValueEditorModifier("oaiRequestIdentifierEditorModifier")
        @Size(min = 5, max = 80)
        @NullableNotBlank
        String identifier
) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class OaiRequestAllowedSetsResolver implements AcceptableValuesResolver<OaiRequest, NamedLabeledIdentified<String>> {

        @NonNull AllValuesProvider<? extends RecordExport> recordExportLoader;

        @Override
        public List<NamedLabeledIdentified<String>> resolveAcceptableValues(OaiRequest formObject, Department ctx) {
            return ListUtil.convert(recordExportLoader.getAll(), recordExport -> new BasicNamedLabeledIdentified<>(recordExport.getOaiSetId(), recordExport.getOaiSetName()));
        }
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class OaiRequestIdentifierEditorModifier implements TextValueEditorModifier<OaiRequest> {

        @NonNull ContextualProvider<Department, @NonNull String> documentIdPattern;
        @NonNull String recordIdPatternPlaceholder;
        @NonNull String kindedIdPatternPlaceholder;

        @Override
        public TextValueEditor modify(TextValueEditor editor, OaiRequest formObject, Department ctx) {
            String pattern = documentIdPattern.getOn(ctx);

            String wholeRecordIdPlaceholder = PlaceholderTemplate.SINGLE_CURLY_BRACKET_START + recordIdPatternPlaceholder + PlaceholderTemplate.SINGLE_CURLY_BRACKET_END;
            Optional<String> recordIdPlaceholderPrefix = StringUtil.removeSuffixOpt(pattern, wholeRecordIdPlaceholder);
            if (recordIdPlaceholderPrefix.isPresent()) {
                return editor
                        .withPrefix(recordIdPlaceholderPrefix.get())
                        .withIncludePrefixAndSuffixToModel(true)
                        .withPlaceholder(Texts.ofNative(recordIdPatternPlaceholder));
            }

            String wholeKindedIdPlaceholder = PlaceholderTemplate.SINGLE_CURLY_BRACKET_START + kindedIdPatternPlaceholder + PlaceholderTemplate.SINGLE_CURLY_BRACKET_END;
            Optional<String> kindedIdPlaceholderPrefix = StringUtil.removeSuffixOpt(pattern, wholeKindedIdPlaceholder);
            if (kindedIdPlaceholderPrefix.isPresent()) {
                return editor
                        .withPrefix(kindedIdPlaceholderPrefix.get())
                        .withIncludePrefixAndSuffixToModel(true)
                        .withPlaceholder(Texts.ofNative(kindedIdPatternPlaceholder));
            }

            return editor;
        }
    }

}
