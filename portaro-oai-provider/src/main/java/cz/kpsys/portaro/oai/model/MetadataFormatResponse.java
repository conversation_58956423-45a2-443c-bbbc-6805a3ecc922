package cz.kpsys.portaro.oai.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

public record MetadataFormatResponse(

        @JacksonXmlProperty(localName = "metadataPrefix", namespace = "http://www.openarchives.org/OAI/2.0/")
        @NonNull
        String metadataPrefix,

        @JacksonXmlProperty(localName = "schema", namespace = "http://www.openarchives.org/OAI/2.0/")
        @NonNull
        String schema,

        @JacksonXmlProperty(localName = "metadataNamespace", namespace = "http://www.openarchives.org/OAI/2.0/")
        @NonNull
        String metadataNamespace

) {}
