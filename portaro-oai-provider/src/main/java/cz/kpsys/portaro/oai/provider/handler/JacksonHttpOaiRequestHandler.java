package cz.kpsys.portaro.oai.provider.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.marcxml.convert.JacksonRecordMarcUtil;
import cz.kpsys.portaro.oai.model.OaiPmhResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JacksonHttpOaiRequestHandler implements HttpOaiRequestHandler {

    @NonNull ObjectMapper objectMapper = JacksonRecordMarcUtil.createXmlMapper();
    @NonNull OaiHandler oaiHandler;

    @Override
    public HttpOaiResponse handleRequest(OaiCommand request) {
        TimeMeter tm = TimeMeter.start();
        try {
            OaiPmhResponse oaiPmhResponse = handleAndLogRequest(request);
            String responseXml = serialize(oaiPmhResponse, request);
            return HttpOaiResponse.success(responseXml);

        } finally {
            log.info("OAI request {} filled in {}", request, tm.elapsedTimeString());
        }
    }

    private OaiPmhResponse handleAndLogRequest(OaiCommand command) {
        try {
            return oaiHandler.handleCommand(command);
        } catch (Exception ex) {
            log.error("Error while handle OAI command {}", command, ex);
            throw ObjectUtil.toRuntimeEx(ex);
        }
    }

    private String serialize(OaiPmhResponse oaiPmhResponse, OaiCommand request) {
        try {
            return objectMapper.writeValueAsString(oaiPmhResponse);
        } catch (Exception ex) {
            log.error("Error while serializing OAI response {} to JSON", request, ex);
            throw ObjectUtil.toRuntimeEx(ex);
        }
    }

}
