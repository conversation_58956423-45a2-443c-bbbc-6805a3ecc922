package cz.kpsys.portaro.export;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.template.TemplateDescriptor;
import cz.kpsys.portaro.template.TemplateEngine;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.TypeDescriptor;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CsvFileExporter<LIST_ITEM> extends AbstractToTextualFileExporter<List<LIST_ITEM>> {

    @NonNull TemplateEngine templateEngine;
    @NonNull Class<LIST_ITEM> itemClass;
    @NonNull String modelName;
    @NonNull TemplateDescriptor template;

    public CsvFileExporter(String filename, @NonNull TemplateDescriptor template, @NonNull Class<LIST_ITEM> itemClass, @NonNull String modelName, @NonNull TemplateEngine templateEngine) {
        super(filename);
        this.itemClass = itemClass;
        this.modelName = modelName;
        super.setPrependUtfBom(true);
        this.template = template;
        this.templateEngine = templateEngine;
    }

    @Override
    public String exportToString(List<LIST_ITEM> items, @NonNull UserAuthentication currentAuth, Department ctx, Locale locale) {
        Map<String, Object> model = new HashMap<>();
        model.put("csvEscaper", new CsvEscaper());
        model.put(modelName, items);
        return templateEngine.build(template, currentAuth, ctx, model, locale);
    }

    @Override
    public TypeDescriptor getType() {
        return TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(itemClass));
    }
}