package cz.kpsys.portaro.auth.oauth2.jwt;

import cz.kpsys.portaro.auth.process.AuthenticationRequestFactory;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

import jakarta.servlet.http.HttpServletRequest;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationRequestFactory implements AuthenticationRequestFactory<String, JwtAuthenticationRequest> {

    @NonNull AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Override
    public JwtAuthenticationRequest createAuthenticationRequest(@NonNull String token, @NonNull HttpServletRequest request, @NonNull Department department) {
        JwtAuthenticationRequest authRequest = new JwtAuthenticationRequest(department, token);
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
        return authRequest;
    }
}
