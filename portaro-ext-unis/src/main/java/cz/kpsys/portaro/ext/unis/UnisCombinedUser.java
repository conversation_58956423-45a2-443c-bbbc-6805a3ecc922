package cz.kpsys.portaro.ext.unis;

import cz.kpsys.portaro.ext.unis.datatypes.unis.ValidUnisPerson;
import cz.kpsys.portaro.user.Person;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Getter
@Setter
public class UnisCombinedUser {

    @NonNull
    final ValidUnisPerson validUnisPerson;

    @Nullable
    final Person dbUser;

    @NonNull
    public Optional<Person> getDbUser() {
        return Optional.ofNullable(dbUser);
    }

}
