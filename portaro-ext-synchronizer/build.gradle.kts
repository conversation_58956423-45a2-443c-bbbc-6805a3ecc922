dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))
    implementation(project(":portaro-web"))

    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework:spring-context-support:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")
    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
}
