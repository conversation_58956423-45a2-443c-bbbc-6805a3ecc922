package cz.kpsys.portaro.ext.synchronizer;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ForeignSystemSynchronizerRegistry<E> {

    @NonNull Map<String, BulkSynchronizer<E>> mapOfConcreteForeignSystemSynchronizer;

    public void register(@NonNull String systemSynchronizationKey,
                         @NonNull BulkSynchronizer<? extends E> synchronizer) {
        mapOfConcreteForeignSystemSynchronizer.put(systemSynchronizationKey, (BulkSynchronizer<E>) synchronizer);
    }

    @NonNull
    public BulkSynchronizer<? extends E> getSystemSynchronizerByName(@NonNull String systemSynchronizationName) {
        return Objects.requireNonNull(mapOfConcreteForeignSystemSynchronizer.get(systemSynchronizationName), () -> "Sync system %s is NOT supported.".formatted(systemSynchronizationName));
    }

    public Collection<BulkSynchronizer<E>> getAllSynchronizers() {
        return mapOfConcreteForeignSystemSynchronizer.values();
    }

}
