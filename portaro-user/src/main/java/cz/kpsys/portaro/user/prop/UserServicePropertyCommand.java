package cz.kpsys.portaro.user.prop;

import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Instant;

public record UserServicePropertyCommand(
        @NonNull
        String service,

        @NonNull
        String name,

        @NonNull
        String value,

        @Nullable
        Instant validityEndDate) {

    public static UserServicePropertyCommand withoutValidity(@NonNull String service, @NonNull String name, @NonNull String value) {
        return new UserServicePropertyCommand(service, name, value, null);
    }
}
