package cz.kpsys.portaro.user;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class NativeUserNameGenerator implements UserStringGenerator.TypedGenerator<NativeNamedUser> {

    @Override
    public boolean supports(@NonNull NativeNamedUser p) {
        return p.getNativeName() != null;
    }

    @Override
    public @NonNull String generate(@NonNull NativeNamedUser p) {
        return p.getNativeName();
    }
}
