package cz.kpsys.portaro.user.relation;

import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.User;
import lombok.NonNull;

import java.util.Optional;
import java.util.stream.Stream;

public interface UserRelationsHelper {

    /**
     * Z<PERSON>k<PERSON> rodiny, do kterých patří daný uživatel. Pokud je zadaný uživatel rodina, vr<PERSON><PERSON><PERSON> prázdný výsledek.
     *
     * @param basicUser zjišťovaný uživatel
     *
     * @return
     */
    Stream<Institution> getUserFamilies(@NonNull BasicUser basicUser);

    Optional<User> getRepresenter(@NonNull BasicUser basicUser);

    /**
     * Vrací představitele rodiny, do které patří daný uživatel (pokud do nějaké patří).
     *
     * @param basicUser uživatel
     *
     * @return představitel rodiny.
     */
    Optional<User> getHeadOfFamily(@NonNull BasicUser basicUser);

    boolean isMemberOfFamily(@NonNull BasicUser basicUser);

    boolean isHeadOfFamily(@NonNull BasicUser basicUser);

    boolean isFamily(@NonNull BasicUser user);

    Stream<User> getFamilies(@NonNull Stream<UserRelation> relations);

    Stream<BasicUser> getMembers(@NonNull Stream<UserRelation> relations);

    /**
     * Získá všechny členy rodin reprezentovaných tímto uživatelem, včetně rodiny samotné.
     * Pozor, pokud je vstupem rodina, výstup je prázdný!
     *
     * @param user uživatel
     *
     * @return
     */
    Stream<BasicUser> getAllUsersRepresentedBy(@NonNull BasicUser user);

    /**
     * Získá všechny členy rodiny, do které patří daný uživatel, včetně rodiny samotné. Pokud je vstupem rodina,
     * vrátí výstup jako pro jakéhokoliv člena rodiny (vrátí všechno).
     *
     * @param user uživatel
     *
     * @return
     */
    Stream<BasicUser> getWholeFamilyOf(@NonNull BasicUser user);

}
