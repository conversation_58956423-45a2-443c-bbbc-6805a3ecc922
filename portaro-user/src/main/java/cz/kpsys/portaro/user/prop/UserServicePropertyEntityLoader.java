package cz.kpsys.portaro.user.prop;

import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface UserServicePropertyEntityLoader extends JpaRepository<UserServicePropertyEntity, UUID> {

    List<UserServicePropertyEntity> getAllByServiceAndUserId(@NonNull String service, @NonNull Integer userId);

    List<UserServicePropertyEntity> getAllByUserIdIn(List<Integer> userIds);


}
