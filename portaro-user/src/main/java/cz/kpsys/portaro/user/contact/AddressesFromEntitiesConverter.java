package cz.kpsys.portaro.user.contact;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

public class AddressesFromEntitiesConverter implements Converter<List<AddressEntity>, List<Address>> {

    @Override
    public List<Address> convert(@NonNull List<AddressEntity> source) {
        return source.stream()
                .map(addressEntity -> {
                    return new Address(
                            addressEntity.getId(),
                            addressEntity.getStreet(),
                            addressEntity.getCity(),
                            addressEntity.getPostalCode(),
                            ObjectUtil.elvis(addressEntity.getCountryId(), Country.CODEBOOK::getById)
                    );
                })
                .toList();
    }
}
