package cz.kpsys.portaro.auth.saml2.idp.marshall;

import lombok.NonNull;
import org.opensaml.saml.common.AbstractSAMLObjectBuilder;
import org.opensaml.saml.common.xml.SAMLConstants;
import org.opensaml.saml.saml2.core.AttributeValue;

public class AttributeValueBuilder extends AbstractSAMLObjectBuilder<AttributeValue> {

    @Override
    public @NonNull AttributeValue buildObject() {
        return buildObject(SAMLConstants.SAML20_NS, AttributeValue.DEFAULT_ELEMENT_LOCAL_NAME, SAMLConstants.SAML20_PREFIX);
    }

    @Override
    public @NonNull AttributeValue buildObject(String namespaceURI, @NonNull String localName, String namespacePrefix) {
        return new AttributeValueImpl(namespaceURI, localName, namespacePrefix);
    }
}