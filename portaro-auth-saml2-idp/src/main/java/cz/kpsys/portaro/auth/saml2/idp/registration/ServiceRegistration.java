package cz.kpsys.portaro.auth.saml2.idp.registration;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.auth.saml2.idp.attribute.Saml2AttributeName;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Value
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ServiceRegistration implements LabeledIdentified<String> {

    /**
     * SP EntityId
     */
    @EqualsAndHashCode.Include
    @NonNull
    String id;

    @NonNull
    Text text;

    @NonNull
    Set<ServiceAttributeRegistration> attributes;

    @With
    @Nullable
    String informationUrl;

    @With
    @Nullable
    String privacyStatementUrl;

    public static ServiceRegistration of(@NonNull String id, @NonNull Text text, @NonNull Set<Saml2AttributeName> requiredAttributes) {
        return of(id, text, requiredAttributes, Set.of());
    }

    public static ServiceRegistration of(@NonNull String id, @NonNull Text text, @NonNull Set<Saml2AttributeName> requiredAttributes, @NonNull Set<Saml2AttributeName> optionalAttributes) {
        Set<ServiceAttributeRegistration> attributes = Stream.concat(
                requiredAttributes.stream().map(ServiceAttributeRegistration::required),
                optionalAttributes.stream().map(ServiceAttributeRegistration::optional)
        ).collect(Collectors.toUnmodifiableSet());
        return new ServiceRegistration(id, text, attributes, null, null);
    }

    @JsonIgnore
    public Set<Saml2AttributeName> getRequiredAttributes() {
        return attributes.stream()
                .filter(ServiceAttributeRegistration::isRequired)
                .map(ServiceAttributeRegistration::getAttributeName)
                .collect(Collectors.toUnmodifiableSet());
    }

    public Optional<String> getInformationUrl() {
        return Optional.ofNullable(informationUrl);
    }

    public Optional<String> getPrivacyStatementUrl() {
        return Optional.ofNullable(privacyStatementUrl);
    }
}
