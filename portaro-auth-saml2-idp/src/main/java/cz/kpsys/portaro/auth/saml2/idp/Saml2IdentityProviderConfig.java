package cz.kpsys.portaro.auth.saml2.idp;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.auth.saml2.idp.attribute.*;
import cz.kpsys.portaro.auth.saml2.idp.attributeprovider.*;
import cz.kpsys.portaro.auth.saml2.idp.auth.*;
import cz.kpsys.portaro.auth.saml2.idp.metadata.Saml2IdentityProviderMetadataController;
import cz.kpsys.portaro.auth.saml2.idp.metadata.Saml2IdentityProviderMetadataGenerator;
import cz.kpsys.portaro.auth.saml2.idp.registration.ServiceRegistration;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.image.ImageResizer;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.EnablableLabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadableByIdLoaderAdapter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.config.ConverterRegisterer;
import cz.kpsys.portaro.config.SaverBuilderFactory;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.custom.CustomFile;
import cz.kpsys.portaro.form.form.FormModifier;
import cz.kpsys.portaro.form.validation.AdhocValidator;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResolver;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.prop.*;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.velocity.app.VelocityEngine;
import org.opensaml.saml.saml2.core.Response;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.io.ByteArrayResource;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static cz.kpsys.portaro.auth.saml2.idp.attribute.Saml2AttributeName.*;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Saml2IdentityProviderConfig {

    public static final String FEATURE_NAME = "SAML2 identity provider";

    private static final String PORTARO_SIGNING_PRIVATE_KEY = """
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    private static final String PORTARO_SIGNING_CERTIFICATE = """
            -----BEGIN CERTIFICATE-----
            MIIDVzCCAj+gAwIBAgIEX/Nb7jANBgkqhkiG9w0BAQsFADBcMQswCQYDVQQGEwJD
            WjEXMBUGA1UECBMOQ3plY2ggUmVwdWJsaWMxEjAQBgNVBAcTCVBhcmR1YmljZTEP
            MA0GA1UEChMGS1AtU1lTMQ8wDQYDVQQDEwZLUC1TWVMwHhcNMTkwODAyMTMyMzQz
            WhcNNDYxMjE4MTMyMzQzWjBcMQswCQYDVQQGEwJDWjEXMBUGA1UECBMOQ3plY2gg
            UmVwdWJsaWMxEjAQBgNVBAcTCVBhcmR1YmljZTEPMA0GA1UEChMGS1AtU1lTMQ8w
            DQYDVQQDEwZLUC1TWVMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDJ
            jOfJyf0W2Xa+WHrJtfVPKHgFnkB0KcSUoJZwbuUfZws9QHjjLLoX7P+wJvlooMh/
            Fxgq42jLIwe6khw4hepw433DZ74uuffXeZwv1V60Ny8VQJWGNnCC5CrrI2Ny+RZ1
            m1OoEyktLUe9IQIijNQCHGeU4L2fIMVMYudyanP3N0c3rZV6b6TKPqCLqNJMaytO
            pw1bR+mc3CYEx9b2SmkBAymWAtRzhGcz5qNId6fwNO47skCT3BUNQfv9Ggr5WuvW
            Xm8TeXrtQi4KyJ+IdEzSK4j2xTVcuBaCLyhSYoGTj4rJ8XoZfT3UXjZnv5ivcWIp
            3DLoOT/wEiHp58hj7WnpAgMBAAGjITAfMB0GA1UdDgQWBBTczhMuvAh3s8qtN9LP
            b+D52Xw/LDANBgkqhkiG9w0BAQsFAAOCAQEAmypjHZx0uQY6OwrUKZig4ETP6vxm
            ObyFxw75pdxPYcPIzFrMh/QkRaJnWs/mYd6qPiQeluRpjrJvchZxWXl3N+juAd6S
            uJfopMvjophrR8X76av3HlBdETNXj7rf0sF26K7EaZI9OFaYRXNQQ3edLoJGdh9b
            RoHNia6Or4cCsJHiNwXoK1OQmTJQvjGMK8dlhzUXtNaDXppKUsWvdQSHiNk0THoR
            rYoBP5RH783N4PMq8EnSZztea+Dl5hCaw8ge0j3nJUl9GDZO2d4CvzL2DobkGSJt
            yMzare/MvieEWcoF72/DBneOiu9YOsd163FCZbyBEIFdFeEnJ4abiON4UA==
            -----END CERTIFICATE-----""";

    @NonNull SettingLoader settingLoader;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull ObjectMapper xmlMapper;
    @NonNull VelocityEngine velocityEngine;
    @NonNull Translator<Department> translator;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull ByIdLoadable<CustomFile, String> hierarchyTraversingCustomFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull ImageResizer imageResizer;
    @NonNull UserLoader userLoader;
    @NonNull AdhocValidator adhocValidator;
    @NonNull SecurityManager securityManager;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull UserServicePropertyLoader userServicePropertyLoader;
    @NonNull ConversionService conversionService;

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> saml2IdentityProviderEnabledProvider() {
        return settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ENABLED);
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> saml2IdentityProviderAllowUnknownServicesEnabledProvider() {
        return ContextIgnoringContextualProvider.of(false);
    }

    @Bean
    public ContextualProvider<Department, @NonNull String> idpEntityIdProvider() {
        return settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ENTITY_ID).throwingWhenNull();
    }

    @Bean
    public Saml2AuthRequestController saml2AuthRequestController() {
        return new Saml2AuthRequestController(
                saml2IdentityProviderEnabledProvider().toEnabledAsserter(value -> value, FEATURE_NAME, null),
                samlRequestHttpParameterConverter(),
                saml2RequestSignatureValidator(),
                provideUserAttributesRequestAllowedResolver(),
                saml2ResponseCreator(),
                saml2ResponseHttpParameterConverter(),
                velocityEngine,
                serverUrlProvider.throwingWhenNull(),
                adhocValidator,
                securityManager
        );
    }

    @Bean
    public ProvideUserAttributesRequestAllowedResolver provideUserAttributesRequestAllowedResolver() {
        return new ProvideUserAttributesRequestAllowedResolver(
                samlRequestHttpParameterConverter(),
                serviceRegistrationLoader(),
                saml2IdentityProviderAllowUnknownServicesEnabledProvider()
        );
    }

    @Bean
    public FormModifier<ProvideUserAttributesRequest> provideUserAttributesRequestFormModifier() {
        return new ProvideUserAttributesRequestFormModifier(samlRequestHttpParameterConverter(), serviceRegistrationLoader());
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<ProvideUserAttributesRequest> provideUserAttributesRequestPreValidationModifier() {
        return new ProvideUserAttributesRequest.ProvideUserAttributesRequestPreValidationModifier(
                provideUserAttributesRequiredAttributesResolver(),
                settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_ATTRIBUTES_CONFIRMATION_ENABLED)
        );
    }

    @Bean
    public AcceptableValuesResolver<ProvideUserAttributesRequest, Saml2AttributeName> provideUserAttributesAllAttributesResolver() {
        return provideUserAttributesRequiredAttributesResolver(); // currently, all attrs are only required ones
    }

    @Bean
    public AcceptableValuesResolver<ProvideUserAttributesRequest, Saml2AttributeName> provideUserAttributesRequiredAttributesResolver() {
        return new ProvideUserAttributesRequiredAttributesResolver(samlRequestHttpParameterConverter(), serviceRegistrationLoader());
    }

    @Bean
    public AuthenticatedAcceptableValuesResolver<ProvideUserAttributesRequest, EnablableLabeledIdentified<?>> provideUserAttributesFormAttributesResolver() {
        return new ProvideUserAttributesFormAttributesResolver(provideUserAttributesAllAttributesResolver(), currentAuthSaml2AttributesGenerator(), samlRequestHttpParameterConverter());
    }

    @Bean
    public Saml2IdentityProviderMetadataController saml2MetadataController() {
        return new Saml2IdentityProviderMetadataController(saml2IdentityProviderMetadataGenerator());
    }

    @Bean
    public Saml2RequestSignatureValidator saml2RequestSignatureValidator() {
        return new Saml2RequestSignatureValidator(settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_AUTH_REQUEST_SIGNATURE_REQUIRED));
    }

    @Bean
    public Saml2RequestHttpParameterConverter samlRequestHttpParameterConverter() {
        return new Saml2RequestHttpParameterConverter(
                new AuthnRequestUnmarshaller(xmlMapper),
                indexedConsumerServiceUrlRegister()
        );
    }

    @Bean
    public IndexedConsumerServiceUrlRegister indexedConsumerServiceUrlRegister() {
        return new IndexedConsumerServiceUrlRegister()
                .withIssuer("https://shibboleth.cambridge.org/shibboleth-sp", Map.of(1, "https://shibboleth.cambridge.org/Shibboleth.sso/SAML2/POST"));
    }

    @Bean
    public Converter<Response, String> saml2ResponseHttpParameterConverter() {
        return new Saml2ResponseHttpParameterConverter();
    }

    @Bean
    public Saml2ResponseCreator saml2ResponseCreator() {
        return new Saml2ResponseCreator(saml2AssertionFactory(), idpEntityIdProvider());
    }

    @Bean
    public Saml2AssertionFactory saml2AssertionFactory() {
        return new Saml2AssertionFactory(
                assertionSigner(),
                idpEntityIdProvider(),
                currentAuthSaml2AttributesGenerator(),
                new AttributeConverter(idpEntityIdProvider())
        );
    }

    private Saml2AssertionSigner assertionSigner() {
        return new Saml2AssertionSigner(
                new ByteArrayResource(PORTARO_SIGNING_CERTIFICATE.getBytes(StandardCharsets.UTF_8)),
                new ByteArrayResource(PORTARO_SIGNING_PRIVATE_KEY.getBytes(StandardCharsets.UTF_8)),
                idpEntityIdProvider()
        );
    }

    @Bean
    public CurrentAuthSaml2AttributesGenerator currentAuthSaml2AttributesGenerator() {
        return new CompositeCurrentAuthSaml2AttributesGenerator(attributeValueProviders(), userLoader);
    }

    private Map<Saml2AttributeName, AttributeValueProvider<String>> attributeValueProviders() {
        ContextualProvider<Department, @NonNull String> scopeDomainProvider = settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_SCOPE_DOMAIN).throwingWhenNull();
        ContextualProvider<Department, @NonNull List<ReaderCategory>> employeeReaderCategoriesProvider = settingLoader.getDepartmentedProvider(UserSettingKeys.EMPLOYEE_READER_CATEGORIES)
                .andThenFastReturningNull(ids -> new AllByIdsLoadableByIdLoaderAdapter<>(readerCategoryLoader).getAllByIds(ids));

        EduPersonAffiliationAttributeValueProvider eduPersonAffiliationAttributeValueProvider = new EduPersonAffiliationAttributeValueProvider(employeeReaderCategoriesProvider);
        UnstructuredNameAttributeValueProvider unstructuredNameAttributeValueProvider = new UnstructuredNameAttributeValueProvider(userServicePropertyHelper());

        Map<Saml2AttributeName, AttributeValueProvider<String>> map = new HashMap<>();
        map.put(EDU_PERSON_TARGETED_ID, new EduPersonTargetedIdAttributeValueProvider(userServicePropertyHelper()));
        map.put(CN, new CnAttributeValueProvider());
        map.put(DISPLAY_NAME, new DisplayNameAttributeValueProvider(localizer));
        map.put(EDU_PERSON_AFFILIATION, eduPersonAffiliationAttributeValueProvider);
        map.put(EDU_PERSON_ENTITLEMENT, new RequesterEntityRestrictingAttributeValueProvider(
                ProvidedAttributeValueProvider.ofStaticSingle("urn:mace:dir:entitlement:common-lib-terms"),
                Set.of(
                        "http://localhost/login/saml/metadata",
                        "https://kramerius-dnnt.nkp.cz/",
                        "https://kramerius.lib.cas.cz/shibboleth",
                        "https://kramerius.svkhk.cz/shibboleth",
                        "https://ndk.cz/",
                        "https://sdauth.sciencedirect.com/",
                        "https://shibbolethsp.jstor.org/shibboleth",
                        "http://shibboleth.ebscohost.com",
                        "https://iam.atypon.com/shibboleth",
                        "https://oup-sp.sams-sigma.com/shibboleth",
                        "https://eduid.kfbz.cz/realms/kramerius",
                        "https://eduid.knihovna-pardubice.cz/realms/kramerius"
                )
        ));
        map.put(EDU_PERSON_SCOPED_AFFILIATION, new ScopeAppendingAttributeValueProvider(eduPersonAffiliationAttributeValueProvider, scopeDomainProvider));
        map.put(EDU_PERSON_PRINCIPAL_NAME, new EduPersonPrincipalNameAttributeValueProvider(scopeDomainProvider, userServicePropertyHelper()));
        map.put(EDU_PERSON_UNIQUE_ID, new ScopeAppendingAttributeValueProvider(unstructuredNameAttributeValueProvider, scopeDomainProvider));
        map.put(UNSTRUCTURED_NAME, unstructuredNameAttributeValueProvider);
        map.put(GIVEN_NAME, new GivenNameAttributeValueProvider());
        map.put(SN, new SnAttributeValueProvider());
        map.put(MAIL, new EmailAttributeValueProvider());
        map.put(ORGANIZATION, new OrganizationAttributeValueProvider(organizationProvider()));
        map.put(USER_LIBRARY_ID, new PersonProvidingSingleAttributeValueProvider(basicUserToLibraryIdUserConverter()));
        map.put(SCHAC_HOME_ORGANIZATION, ProvidedAttributeValueProvider.ofSingle(scopeDomainProvider));
        return map;
    }

    private Saml2IdentityProviderMetadataGenerator saml2IdentityProviderMetadataGenerator() {
        return new Saml2IdentityProviderMetadataGenerator(
                velocityEngine,
                idpEntityIdProvider(),
                serverUrlProvider.throwingWhenNull(),
                settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_SCOPE_DOMAIN).throwingWhenNull(),
                ContextIgnoringContextualProvider.of(PORTARO_SIGNING_CERTIFICATE),
                hierarchyTraversingCustomFileLoader,
                fileDataStreamer,
                imageResizer,
                technicalContactPersonProvider(),
                organizationProvider(),
                translator
        );
    }

    private ContextualProvider<Department, @NonNull Institution> organizationProvider() {
        return new OrganizationDepartmentedProvider(userLoader);
    }

    private ContextualProvider<Department, @NonNull Person> technicalContactPersonProvider() {
        return new ContextualContactPersonProvider<>(
                settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_CONTACT_PERSON_ID).throwingWhenNull(),
                userLoader);
    }


    @Bean
    public Saver<UserServiceProperty, UserServiceProperty> userServicePropertySaver() {
        return saverBuilderFactory.<UserServiceProperty, UUID>saver()
                .intermediateConverting(new UserServicePropertyToEntityConverter())
                .withClearedCacheName(UserServicePropertyEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public UserServicePropertyHelper userServicePropertyHelper() {
        return new UserServicePropertyHelper(
                userServicePropertyLoader,
                userServicePropertySaver(),
                conversionService);
    }

    @Bean
    public ContextualFunction<BasicUser, Department, @NonNull String> basicUserToLibraryIdUserConverter() {
        return new ConfigurableBasicUserToStringIdentifierConverter(
                settingLoader.getDepartmentedProvider(Saml2IdentityProviderSettingKeys.SAML2_IDENTITY_PROVIDER_USER_LIBRARY_ID_USER_IDENTIFIER),
                userLoader)
                .throwingWhenNull();
    }

    @Bean
    public Codebook<ServiceRegistration, String> serviceRegistrationLoader() {
        // informations are visitable on https://met.refeds.org/
        return new StaticCodebook<>(
                ServiceRegistration.of("http://localhost/login/saml/metadata", Texts.ofNative("KPSYS TEST LOCALHOST"), Set.of(MAIL, EDU_PERSON_TARGETED_ID, EDU_PERSON_PRINCIPAL_NAME, EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_ENTITLEMENT, USER_LIBRARY_ID, SCHAC_HOME_ORGANIZATION)).withInformationUrl("https://kpsys.cz").withPrivacyStatementUrl("https://kpsys.cz"),

                ServiceRegistration.of("https://attributes.eduid.cz/shibboleth", Texts.ofNative("Zobrazovač EduID atributů"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, GIVEN_NAME, MAIL, CN, EDU_PERSON_TARGETED_ID, EDU_PERSON_UNIQUE_ID, EDU_PERSON_PRINCIPAL_NAME, SN, UNSTRUCTURED_NAME, DISPLAY_NAME, ORGANIZATION, USER_LIBRARY_ID, SCHAC_HOME_ORGANIZATION)),
                ServiceRegistration.of("https://metaman.eduid.cz/shibboleth", Texts.ofNative("MetaMan - Aplikace pro správu metadat federací."), Set.of(EDU_PERSON_UNIQUE_ID, MAIL, CN)),

                ServiceRegistration.of("https://sp.knihovny.cz/", Texts.ofNative("Knihovny.cz"), Set.of(EDU_PERSON_UNIQUE_ID, EDU_PERSON_PRINCIPAL_NAME, CN, GIVEN_NAME, SN, MAIL, EDU_PERSON_AFFILIATION, EDU_PERSON_SCOPED_AFFILIATION, USER_LIBRARY_ID)).withInformationUrl("https://www.knihovny.cz/Portal/Page/o-portalu").withPrivacyStatementUrl("https://www.knihovny.cz/Portal/Page/ochrana-osobnich-udaju"),
                ServiceRegistration.of("https://beta.knihovny.cz/", Texts.ofNative("Knihovny.cz BETA"), Set.of(EDU_PERSON_UNIQUE_ID, EDU_PERSON_PRINCIPAL_NAME, CN, GIVEN_NAME, SN, MAIL, EDU_PERSON_AFFILIATION, EDU_PERSON_SCOPED_AFFILIATION, USER_LIBRARY_ID)),
                ServiceRegistration.of("https://cpk-front.mzk.cz/", Texts.ofNative("cpk-front.mzk.cz"), Set.of(EDU_PERSON_UNIQUE_ID, EDU_PERSON_PRINCIPAL_NAME, CN, GIVEN_NAME, SN, MAIL, EDU_PERSON_AFFILIATION, EDU_PERSON_SCOPED_AFFILIATION, USER_LIBRARY_ID)),
                ServiceRegistration.of("https://cpk-front-devel.mzk.cz/", Texts.ofNative("cpk-front-devel.mzk.cz"), Set.of(EDU_PERSON_UNIQUE_ID, EDU_PERSON_PRINCIPAL_NAME, CN, GIVEN_NAME, SN, MAIL, EDU_PERSON_AFFILIATION, EDU_PERSON_SCOPED_AFFILIATION, USER_LIBRARY_ID)),

                ServiceRegistration.of("https://kramerius-dnnt.nkp.cz/", Texts.ofNative("Národní knihovna České republiky - Digitální knihovna Kramerius"), Set.of(EDU_PERSON_AFFILIATION, EDU_PERSON_UNIQUE_ID, EDU_PERSON_ENTITLEMENT)).withInformationUrl("https://kramerius-dnnt.nkp.cz/podminky-zpristupneni"),
                ServiceRegistration.of("https://ndk.cz/", Texts.ofNative("Národní digitální knihovna"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_ENTITLEMENT, EDU_PERSON_PRINCIPAL_NAME)).withInformationUrl("https://ndk.cz/podminky-zpristupneni"),
                ServiceRegistration.of("https://dnnt.mzk.cz/", Texts.ofNative("Moravská zemská knihovna v Brně - Díla nedostupná na trhu"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, GIVEN_NAME, CN, EDU_PERSON_UNIQUE_ID, SN, EDU_PERSON_PRINCIPAL_NAME)).withInformationUrl("https://dnnt.mzk.cz/about"),
                ServiceRegistration.of("https://login.kramerius.mzk.cz/realms/kramerius", Texts.ofNative("Moravská zemská knihovna v Brně - Digitální knihovna"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_UNIQUE_ID)).withInformationUrl("https://www.digitalniknihovna.cz/mzk/about").withPrivacyStatementUrl("https://www.mzk.cz/en/personaldatainfo"),
                ServiceRegistration.of("https://kramerius.lib.cas.cz/shibboleth", Texts.ofNative("Digitální knihovna AV ČR"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_UNIQUE_ID, MAIL, EDU_PERSON_ENTITLEMENT, EDU_PERSON_PRINCIPAL_NAME)).withInformationUrl("https://kramerius.lib.cas.cz/podminky-zpristupneni"),
                ServiceRegistration.of("https://kramerius-edu.lib.cas.cz/shibboleth", Texts.ofNative("Digitální knihovna AV ČR"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, MAIL, EDU_PERSON_PRINCIPAL_NAME)).withInformationUrl("https://kramerius-edu.lib.cas.cz/podminky-zpristupneni"),
                ServiceRegistration.of("https://kramerius.techlib.cz/shibboleth", Texts.ofNative("Národní technická knihovna - Digitální knihovna Kramerius"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, GIVEN_NAME, MAIL, EDU_PERSON_PRINCIPAL_NAME, CN, ORGANIZATION, SN)).withInformationUrl("https://kramerius.techlib.cz/"),
                ServiceRegistration.of("https://kramerius.techlib.cz/auth/realms/kramerius", Texts.ofNative("Národní technická knihovna - Kramerius 7 digitální knihovna"), Set.of(EDU_PERSON_UNIQUE_ID, EDU_PERSON_SCOPED_AFFILIATION)).withInformationUrl("https://kramerius.techlib.cz/about").withPrivacyStatementUrl("https://www.techlib.cz/en/82764-protection-of-personal-data"),
                ServiceRegistration.of("https://kramerius.svkhk.cz/shibboleth", Texts.ofNative("Studijní a vědecká knihovna v Hradci Králové"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_UNIQUE_ID, EDU_PERSON_ENTITLEMENT, EDU_PERSON_PRINCIPAL_NAME, MAIL)).withInformationUrl("https://kramerius.svkhk.cz/podminky-zpristupneni"),
                ServiceRegistration.of("https://login.ceskadigitalniknihovna.cz/realms/kramerius", Texts.ofNative("Česká digitální knihovna"), Set.of(EDU_PERSON_AFFILIATION, CN, DISPLAY_NAME, EDU_PERSON_UNIQUE_ID, MAIL, GIVEN_NAME, SN)).withInformationUrl("https://ceskadigitalniknihovna.cz/").withPrivacyStatementUrl("https://www.mzk.cz/en/personaldatainfo"),
                ServiceRegistration.of("https://eduid.kfbz.cz/realms/kramerius", Texts.ofNative("KFBZ / Kramerius 7"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_UNIQUE_ID, EDU_PERSON_ENTITLEMENT, MAIL), Set.of(EDU_PERSON_AFFILIATION, CN, DISPLAY_NAME, SCHAC_HOME_ORGANIZATION, EDU_PERSON_PRINCIPAL_NAME, GIVEN_NAME, SN)).withInformationUrl("https://kramerius.kfbz.cz/about").withPrivacyStatementUrl("https://www.kfbz.cz/ochrana-osobnich-udaju"),
                ServiceRegistration.of("https://eduid.knihovna-pardubice.cz/realms/kramerius", Texts.ofNative("Digitální knihovna Krajské knihovny v Pardubicích"), Set.of(EDU_PERSON_SCOPED_AFFILIATION, EDU_PERSON_UNIQUE_ID, EDU_PERSON_ENTITLEMENT, GIVEN_NAME, SN)).withInformationUrl("https://kramerius.knihovna-pardubice.cz/about").withPrivacyStatementUrl("https://kramerius.knihovna-pardubice.cz/about"),

                ServiceRegistration.of("https://sdauth.sciencedirect.com/", Texts.ofNative("Elsevier Science Direct"), Set.of(EDU_PERSON_TARGETED_ID, EDU_PERSON_ENTITLEMENT)).withInformationUrl("https://www.elsevier.com/about").withPrivacyStatementUrl("https://www.elsevier.com/legal/privacy-policy"),
                ServiceRegistration.of("https://shibbolethsp.jstor.org/shibboleth", Texts.ofNative("JSTOR"), Set.of(EDU_PERSON_TARGETED_ID, EDU_PERSON_ENTITLEMENT, EDU_PERSON_SCOPED_AFFILIATION)).withInformationUrl("http://www.ithaka.org/").withPrivacyStatementUrl("https://www.ithaka.org/privacypolicy"),
                ServiceRegistration.of("http://shibboleth.ebscohost.com", Texts.ofNative("EBSCO Publishing, Inc"), Set.of(EDU_PERSON_TARGETED_ID, EDU_PERSON_ENTITLEMENT, EDU_PERSON_SCOPED_AFFILIATION)).withInformationUrl("https://search.ebscohost.com/").withPrivacyStatementUrl("https://www.ebsco.com/company/privacy-policy"),
                ServiceRegistration.of("https://iam.atypon.com/shibboleth", Texts.ofNative("Atypon SP"), Set.of(EDU_PERSON_TARGETED_ID, EDU_PERSON_ENTITLEMENT, EDU_PERSON_SCOPED_AFFILIATION)).withInformationUrl("https://www.atypon.com/").withPrivacyStatementUrl("https://www.atypon.com/privacy-policy/"),
                ServiceRegistration.of("https://oup-sp.sams-sigma.com/shibboleth", Texts.ofNative("Oxford University Press"), Set.of(EDU_PERSON_ENTITLEMENT, EDU_PERSON_SCOPED_AFFILIATION)),
                ServiceRegistration.of("https://shibboleth.cambridge.org/shibboleth-sp", Texts.ofNative("Cambridge Core"), Set.of(EDU_PERSON_SCOPED_AFFILIATION)).withPrivacyStatementUrl("https://www.cambridge.org/core/legal-notices/federated-access-privacy-statement"),
                ServiceRegistration.of("https://publi.cz", Texts.ofNative("Publi"), Set.of(GIVEN_NAME, SN, MAIL)).withInformationUrl("https://publi.cz"),
                ServiceRegistration.of("https://extlogin.ctk.cz/shibboleth", Texts.ofNative("Infobanka ČTK"), Set.of(EDU_PERSON_PRINCIPAL_NAME, GIVEN_NAME, MAIL, SN, EDU_PERSON_SCOPED_AFFILIATION)).withInformationUrl("https://www.ctk.cz/sluzby/databaze/infobanka/"),

                ServiceRegistration.of("https://meta.cesnet.cz/sp/shibboleth", Texts.ofNative("e-Infrastruktura CESNET"), FALLBACK_REQUIRED_ATTRIBUTES).withInformationUrl("https://www.cesnet.cz/e-infrastruktura-3/").withPrivacyStatementUrl("https://www.cesnet.cz/sdruzeni/dokumenty/oou/"),
                ServiceRegistration.of("https://tcs.cesnet.cz/simplesaml/", Texts.ofNative("Certifikáty TCS"), FALLBACK_REQUIRED_ATTRIBUTES).withInformationUrl("https://tcs.cesnet.cz")
        );
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForStringId(Saml2AttributeName.class, CODEBOOK);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(Saml2IdentityProviderSecurityActions.SAML2_PROVIDE_USER_ATTRIBUTES, PermissionResolver.and(
                permissionFactory.currentEvidedAuthenticActive(),
                PermissionResolver.or(
                        permissionFactory.edit(),
                        permissionFactory.subjectUserHasValidRegistration()
                )
        ));
    }


}
