package cz.kpsys.portaro.auth.switchuser;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.composite.CompositeSuccessAuthentication;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.sec.SecurityActions;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.web.authentication.switchuser.SwitchUserGrantedAuthority;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SwitchedUserAuthenticator implements Authenticator<SwitchUserAuthenticationRequest, SwitchedSuccessAuthentication> {

    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull SecurityManager securityManager;

    @Override
    public SwitchedSuccessAuthentication authenticate(SwitchUserAuthenticationRequest authRequest) throws AuthenticationException {
        checkIfCanSwitch(authRequest);

        CompositeSuccessAuthentication currentAuth = authenticationHolder.getAuthentication();

        // get the original authorities and add the new switch user authority
        List<GrantedAuthority> extendedTargetUserAuths = createExtendedAuthorities(authRequest, currentAuth);

        // create the new authentication token
        return new SwitchedSuccessAuthentication(authRequest.getTargetUser(), authRequest.getDepartment(), extendedTargetUserAuths, currentAuth);
    }

    protected void checkIfCanSwitch(SwitchUserAuthenticationRequest authRequest) {
        UserAuthentication currentAuth = authenticationHolder.getCurrentAuth();
        securityManager.throwIfCannot(SecurityActions.AUTH_SWITCH_ACTIVE_USER, currentAuth, authRequest.getDepartment(), authRequest.getTargetUser());
    }

    private List<GrantedAuthority> createExtendedAuthorities(SwitchUserAuthenticationRequest authRequest, AuthoritiedSuccessAuthentication currentAuthentication) {
        List<GrantedAuthority> targetUserAuths = AuthorityUtils.createAuthorityList(authRequest.getTargetUser().getRole().toArray(new String[]{}));
        SwitchUserGrantedAuthority switchUserAuthority = new SwitchUserGrantedAuthority(SwitchedSuccessAuthentication.SWITCH_AUTHORITY_ROLE, currentAuthentication);
        return ListUtil.createNewListAppending(targetUserAuths, switchUserAuthority);
    }

}
