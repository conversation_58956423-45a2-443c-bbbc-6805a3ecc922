dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-grid"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-record-grid"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
}
