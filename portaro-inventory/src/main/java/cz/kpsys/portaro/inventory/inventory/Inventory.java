package cz.kpsys.portaro.inventory.inventory;

import cz.kpsys.portaro.commons.object.NamedLabeledIdentifiedRecord;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.util.List;

public record Inventory(
        @NonNull Integer id,
        @NonNull String name,
        @NonNull List<Location> locations,
        @Nullable Fond fond,
        @Nullable Department department,
        @With @NonNull InventoryState inventoryState,
        @With @Nullable LocalDate date,
        @NonNull LocalDate createDate,
        @With @Nullable LocalDate closeDate,
        @NonNull BasicUser creator,
        @With @NonNull BasicUser lastEditor,
        @NonNull Boolean includeIssues,
        @Nullable String signaturePrefix,
        @Nullable String lastSignature,
        @Nullable String firstAccessNumber,
        @Nullable String lastAccessNumber,
        @NonNull Boolean includeExchangeSets) implements NamedLabeledIdentifiedRecord<Integer> {

    public static final String SCHEMA_EXAMPLE_ID = "5";

}
