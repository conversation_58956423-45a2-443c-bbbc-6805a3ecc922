version: '3'

services:

  firebird:
    image: kpsys/firebird:latest 
    ports: 
      - 3050:3050
    volumes: 
      - ./kpsys_develop_2_0.fdb:/db.fdb
      - /var/run/docker.sock:/var/run/docker.sock

  appserver:
    image: kpsys/appserver
    ports:
      - 8182:8182
    volumes:
      - ./appserver-workspace:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DATABASE_URL=jdbc:firebirdsql:firebird:db_verbis?lc_ctype=UTF8
      - APPSERVER_INDEXING_ENABLED=false
      - APPSERVER_SCHEDULED_TASKS_ENABLED=false
    depends_on:
      - firebird
