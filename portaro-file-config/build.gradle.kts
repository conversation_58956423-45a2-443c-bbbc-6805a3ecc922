dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-sql-generator"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
    implementation("com.google.guava:guava:+")
}
