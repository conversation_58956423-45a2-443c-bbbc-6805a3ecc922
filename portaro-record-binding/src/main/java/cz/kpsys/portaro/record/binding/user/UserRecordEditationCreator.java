package cz.kpsys.portaro.record.binding.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserStringGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserRecordEditationCreator implements RecordEditationCreator<User> {

    @NonNull Provider<@NonNull Fond> userPersonAuthorityFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull UserStringGenerator prettyUserNameGenerator;

    public RecordEditation ofNewRecord(@NonNull User user, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        Fond fond = userPersonAuthorityFondProvider.get();

        RecordEditation recordEditation;
        if (user.getRid() != null) {
            recordEditation = recordEditationFactory
                    .withHoldingsWithoutCtx(holdingDepartments, ctx)
                    .ofNew(user.getRid(), fond)
                    .build(currentAuth);
        } else {
            recordEditation = recordEditationFactory
                    .withHoldingsWithoutCtx(holdingDepartments, ctx)
                    .ofNew(fond)
                    .build(currentAuth);
        }

        recordEditationHelper.setStringSubfieldValue(prettyUserNameGenerator.generate(user), true, RecordWellKnownFields.AuthorityPersonName.TYPE_ID, true, RecordWellKnownFields.AuthorityPersonName.Name.TYPE_ID, recordEditation, ctx, currentAuth);
        return recordEditation;
    }

    public RecordEditation ofExistingRecord(@NonNull User user, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(recordLoader.getById(user.getRecordId()))
                .build(currentAuth);
    }
}
