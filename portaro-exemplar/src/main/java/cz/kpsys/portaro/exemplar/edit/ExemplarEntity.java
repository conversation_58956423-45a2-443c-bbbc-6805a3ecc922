package cz.kpsys.portaro.exemplar.edit;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5.*;


@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ExemplarEntity implements Identified<Integer> {

    @Id
    @Column(name = ID_EX)
    @EqualsAndHashCode.Include
    Integer id;

    @Nullable
    @Deprecated
    @Column(name = FK_ZAZ)
    Integer documentId;

    @NonNull
    @Column(name = RECORD_ID)
    UUID recordId;

    @Nullable
    @Column(name = FK_PUJC)
    Integer departmentId;

    @NonNull
    @Column(name = FK_LOKACE)
    Integer locationId;

    @Nullable
    @Column(name = BAR_COD)
    String barCode;

    @Nullable
    @Column(name = PRIR_CISLO)
    String accessNumber;

    @Nullable
    @Column(name = SIGNATURA)
    String signature;

    @NonNull
    @Column(name = FK_VYPKAT)
    String loanCategoryId;

    @NonNull
    @Column(name = FK_TEMSKUP)
    String thematicGroupId;

    @NonNull
    @Column(name = FK_ZPNAB)
    String acquisitionWayId;

    @NonNull
    @Column(name = FK_STATUS)
    Integer statusId;

    @Nullable
    @Column(name = DATUM)
    Instant creationDate;

    @NonNull
    @Column(name = FK_DOKFOND)
    Integer fondId;

    @Nullable
    @Column(name = CIS_FAK)
    String invoiceNumber;

    @Nullable
    @Column(name = FK_UBYTEK)
    Long discardionId;

    @Nullable
    @Column(name = FK_POLFAK)
    Integer invoiceItem;

    @Nullable
    @Column(name = POZNAMKA)
    String note;

    @Nullable
    @Column(name = PRILOHY)
    String attachments;

    @NonNull
    @Column(name = PORADI)
    Integer order;

    @Nullable
    @Column(name = DODAVATEL)
    String supplier;

    @Nullable
    @Column(name = UZIVATEL)
    String owner;

    @Nullable
    @Column(name = POMOCNE)
    String customValueId;

    @Nullable
    @Column(name = CENA)
    BigDecimal amount;

    @NonNull
    @Column(name = FK_MENY)
    String currencyId;

    @Nullable
    @Column(name = ROK_PRIR)
    Integer acquisitionYear;

    @NonNull
    @Column(name = TYP_CISLA)
    Integer typeId;

    @NonNull
    @Column(name = POCET_KS)
    Integer quantity;

    @Nullable
    @Column(name = TRIDPRIC)
    String accessNumberSorter;

    @Nullable
    @Column(name = TRIDSIGN)
    String signatureSorter;

    @Nullable
    @Column(name = VOLUME)
    String bundledVolumeNumber;

    @Nullable
    @Column(name = ROK_VOL)
    String bundledVolumeYear;

    @Nullable
    @Column(name = ROZM_CISEL)
    String bundledVolumeIssueRange;

    @Nullable
    @Column(name = DATCAS)
    Instant lastModificationDate;

    @NonNull
    @Column(name = FK_UZIV)
    Integer initiatorId;

    @NonNull
    @Column(name = FK_UCTU)
    String accountId;

    @Nullable
    @Column(name = RFID)
    String rfid;

    @Nullable
    @Column(name = FK_FULLTEXT_SKUPINY)
    Integer directoryId;

    @Nullable
    @Column(name = POZNAMKA_INTERNAL)
    String internalNote;
}