package cz.kpsys.portaro.exemplar;

import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledValue;
import cz.kpsys.portaro.exemplar.discard.Discardion;
import cz.kpsys.portaro.exemplar.replacementway.ReplacementWay;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.prop.RecordPropertyKeys;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExemplarWithDocumentAndDiscardion extends ExemplarProxy {

    @NonNull Record document;
    @Nullable Discardion discardion;

    public ExemplarWithDocumentAndDiscardion(Exemplar e, @NonNull Record document, @Nullable Discardion discardion) {
        super(e);
        this.document = document;
        this.discardion = discardion;
    }

    public @NonNull LabeledRecordRef getRecord() {
        return LabeledRecordRef.ofRecordLink(document.idFondPair(), document.getText());
    }

    public List<? extends Labeled> getDocumentMainAuthors() {
        return document.getProps().find(RecordPropertyKeys.PRIMARY_AUTHORS).orElse(List.of());
    }

    public List<LabeledValue<String>> getDocumentIsbns() {
        return document.getProps().find(RecordPropertyKeys.ISBNS).orElseGet(List::of);
    }



    public String getDiscardionDiscardNumber() {
        return discardion == null ? null : discardion.getDiscardNumber();
    }

    public String getDiscardionReason() {
        return discardion == null ? null : discardion.getReason();
    }

    public ReplacementWay getDiscardionReplacementWay() {
        return discardion == null ? null : discardion.getReplacementWay();
    }

    public Price getDiscardionCost() {
        return discardion == null ? null : discardion.getCost();
    }

    public String getDiscardionConsultationNumber() {
        return discardion == null ? null : discardion.getConsultationNumber();
    }
}
