package cz.kpsys.portaro.exemplar;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ExemplarListSorter {

    @NonNull Comparator<Exemplar> exemplarComparator;
    @NonNull Comparator<Exemplar> bindingComparator;
    @NonNull Comparator<Exemplar> issueComparator;

    public <E extends Exemplar> List<E> sort(List<E> exemplars) {
        List<E> sorted = new ArrayList<>(exemplars);
        sorted.sort(getComparator(sorted));
        return sorted;
    }

    private Comparator<Exemplar> getComparator(List<? extends Exemplar> all) {
        boolean bindingsOnly = ListUtil.isAllTrue(all, Exemplar::isBinding);
        if (bindingsOnly) {
            return bindingComparator;
        }

        boolean issuesOnly = ListUtil.isAllTrue(all, Exemplar::isIssue);
        if (issuesOnly) {
            return issueComparator;
        }

        return exemplarComparator;
    }

}
