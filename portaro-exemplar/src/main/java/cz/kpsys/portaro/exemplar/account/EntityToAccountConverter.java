package cz.kpsys.portaro.exemplar.account;

import cz.kpsys.portaro.commons.localization.Texts;
import org.springframework.core.convert.converter.Converter;

public class EntityToAccountConverter implements Converter<AccountEntity, Account> {

    @Override
    public Account convert(AccountEntity source) {
        return new Account(
                source.getId(),
                Texts.ofNative(source.getName()),
                source.getOrder(),
                source.getDflt()
        );
    }

}
