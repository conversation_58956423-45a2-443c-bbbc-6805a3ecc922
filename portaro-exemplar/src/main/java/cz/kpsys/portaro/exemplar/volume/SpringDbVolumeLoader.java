package cz.kpsys.portaro.exemplar.volume;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.ISSUES.FK_VOL;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.ISSUES.ISSUES;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.VOLUME.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbVolumeLoader implements VolumeLoader, RowMapper<Volume> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<VolumePeriodicity, Integer> volumePeriodicityLoader = VolumePeriodicity.CODEBOOK;

    @Override
    public Volume getById(@NonNull Integer id) {
        return DataUtils.getByIdUsingAllByIds(this, Volume.class, id);
    }


    @Override
    public List<Volume> getAllByIds(@NonNull List<Integer> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(WHOLE(VOLUME), AS(getSelectMinIssueSubquery(), FK_VOL));
        sq.from(VOLUME);
        sq.where().in(ID_VOL, ids);

        List<Volume> result = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
        result.sort(ComparatorForExplicitIdSorting.forIdentified(ids));
        return result;
    }


    @Override
    public List<Volume> getByDocumentId(@NonNull UUID recordId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.selectDistinct(WHOLE(VOLUME), AS(getSelectMinIssueSubquery(), FK_VOL));
        sq.from(VOLUME);
        sq.where()
                .eq(RECORD_ID, recordId)
                .and()
                .eq(TC(VOLUME, VYRAZEN), false);
        sq.orderBy().addDesc(
                TC(VOLUME, ROK_VOL),
                TC(VOLUME, CIS_VOL));
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }


    private String getSelectMinIssueSubquery() {
        SelectQuery selectMinIssueSq = queryFactory.newSelectQuery()
                .select(MIN(FK_VOL))
                .from(ISSUES);
        selectMinIssueSq
                .where().addStringCondition(COLSEQ(TC(VOLUME, ID_VOL), TC(ISSUES, FK_VOL)));
        return "(" + selectMinIssueSq.getSql() + ")";
    }


    @Override
    public Volume mapRow(ResultSet rs, int rowNum) throws SQLException {
        int id = rs.getInt(ID_VOL);
        String cisVol = rs.getString(CIS_VOL);

        Volume v = new Volume(id, StringUtil.hasTrimmedLength(cisVol) ? Texts.ofNative(cisVol.trim()) : Texts.ofEmpty());
        v.setRecordId(DbUtils.uuidNotNull(rs, RECORD_ID));
        v.setVolumeNumber(cisVol);
        v.setIssueQuantity(rs.getInt(POC_KS));
        v.setYear(rs.getString(ROK_VOL));
        v.setPeriodicity(volumePeriodicityLoader.getById(rs.getInt(TYP_PER)));
        v.setPeriodicityMultiplier(rs.getInt(POC_JEDN));
        v.setDate(rs.getDate(DATUM));
        v.setDescription(rs.getString(POZNAMKA));
        v.setWithIssues(rs.getInt(FK_VOL) > 0);
        v.setDeleteDate(rs.getBoolean(VYRAZEN) ? Instant.now() : null);
        return v;
    }
}
