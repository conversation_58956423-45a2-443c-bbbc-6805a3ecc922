package cz.kpsys.portaro.exemplar.exchangeset;

import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class ExchangeExemplarSet extends BasicNamedLabeledIdentified<Integer> {

    @Nullable
    String description;

    public ExchangeExemplarSet(@NonNull Integer id,
                               @NonNull String name,
                               @Nullable String description) {
        super(id, name);
        this.description = description;
    }

}
