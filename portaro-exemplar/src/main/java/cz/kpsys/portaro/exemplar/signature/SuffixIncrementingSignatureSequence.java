package cz.kpsys.portaro.exemplar.signature;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.PatternMatcher;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.regex.Pattern;

import static cz.kpsys.portaro.commons.util.RegExpUtils.*;

public class SuffixIncrementingSignatureSequence extends SignatureSequence<SuffixIncrementingSignatureSequenceItem> {

    public SuffixIncrementingSignatureSequence(Long id, String prefix, String prefixDelimiter, int bodyLength, String suffixType, String suffixDelimiter, boolean firstWithSuffix, Integer lastSavedBodyValue) {
        super(id, prefix, prefixDelimiter, bodyLength, suffixType, replaceSpecialChars(suffixDelimiter), firstWithSuffix, lastSavedBodyValue);
        Assert.hasText(suffixType, "suffixType parameter must not be empty");
    }


    @Override
    public SuffixIncrementingSignatureSequenceItem createKnown(@NonNull String realExemplarValue) {
        return new SuffixIncrementingSignatureSequenceItem(this,
                Integer.parseInt(getPattern().getGroup(realExemplarValue, GROUP_BODY)),
                getPattern().getGroupLenient(realExemplarValue, GROUP_SUFFIX),
                true);
    }


    @Override
    public SuffixIncrementingSignatureSequenceItem createLast() {
        return new SuffixIncrementingSignatureSequenceItem(this, getLastSavedBodyValue(), null, false);
    }


    @Override
    public Text getText() {
        return Texts.ofNative(prefix + prefixDelimiter + "<tělo:" + bodyLength + "znaků>" + suffixDelimiter + suffixType);
    }


    @Override
    protected PatternMatcher getPattern() {
        return new PatternMatcher(
                startEnd(
                    group(Pattern.quote(prefix)) +
                        group(Pattern.quote(prefixDelimiter)) +
                        group("\\d+") + //body
                        optionalOne(group(Pattern.quote(suffixDelimiter)) + group(getRegexSuffix()), false)
                )
        );
    }


    private String getRegexSuffix() {
        if (suffixType.isEmpty()) {
            throw new IllegalArgumentException(String.format("Suffix type \"%s\" cannot be empty", suffixType));
        }
        char typeChar = suffixType.charAt(0);
        return switch (typeChar) {
            case '1' -> "\\d+";
            case 'a' -> "[a-z]+";
            case 'A' -> "[A-Z]+";
            case 'i', 'I' -> "i+";
            default -> throw new IllegalArgumentException(String.format("suffix type is not valid (\"%s\"", suffixType));
        };
    }

}
