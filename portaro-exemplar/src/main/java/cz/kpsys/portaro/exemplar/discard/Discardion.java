package cz.kpsys.portaro.exemplar.discard;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.exemplar.replacementway.ReplacementWay;
import cz.kpsys.portaro.finance.Price;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@EqualsAndHashCode(callSuper = true)
@Value
@FieldNameConstants
public class Discardion extends BasicIdentified<Long> {

    @NonNull
    String discardNumber;

    @Nullable
    ReplacementWay replacementWay;

    @NonNull
    Price cost;

    @NullableNotBlank
    String reason;

    @NullableNotBlank
    String consultationNumber;

    public Discardion(Long id, @NonNull String discardNumber, @NullableNotBlank String reason, @Nullable ReplacementWay replacementWay, @NonNull Price cost, @NullableNotBlank String consultationNumber) {
        super(id);
        this.discardNumber = discardNumber;
        this.reason = reason;
        this.replacementWay = replacementWay;
        this.cost = cost;
        this.consultationNumber = consultationNumber;
    }
}
