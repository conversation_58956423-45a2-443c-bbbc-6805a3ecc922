package cz.kpsys.portaro.exemplar.edit;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordSaveCommand;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.edit.RecordEditationEvent;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ExemplarSaverFondChangingProxy implements Saver<EditedExemplar, Exemplar> {

    @NonNull Saver<EditedExemplar, Exemplar> target;
    @NonNull Set<Integer> fondsToChange;
    @NonNull Provider<Fond> targetFondProvider;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordSaver recordSaver;

    @Override
    public @NonNull Exemplar save(@NonNull EditedExemplar ee) {
        if (fondsToChange.contains(ee.getFond().getId())) {
            ee = changeFond(ee);
        }
        return target.save(ee);
    }

    private EditedExemplar changeFond(EditedExemplar ee) {
        Fond targetFond = targetFondProvider.get();

        Record record = recordLoader.getById(ee.getRecordId());
        record.setFond(targetFond);

        Record savedRecord = saveRecordWithFondChange(ee, record);
        ee.setFond(savedRecord.getFond());
        return ee;
    }

    private Record saveRecordWithFondChange(EditedExemplar ee, Record record) {
        RecordSaveCommand recordSaveCmd = RecordSaveCommand.createStandard(
                record,
                ee.getCtx(),
                ee.getCtx(),
                ee.getCurrentAuth(),
                List.of(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_FOND_CHANGE)),
                true
        );
        return recordSaver.save(recordSaveCmd);
    }
}
