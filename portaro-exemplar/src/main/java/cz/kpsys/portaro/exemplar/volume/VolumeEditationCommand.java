package cz.kpsys.portaro.exemplar.volume;

import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.Date;
import java.util.UUID;

public record VolumeEditationCommand(

    @NonNull
    Integer id,

    @NonNull
    UUID recordId,

    @Nullable
    String volumeNumber,

    @NonNull
    Integer issueQuantity,

    @Nullable
    String year,

    @NonNull
    VolumePeriodicity periodicity,

    @NonNull
    Integer periodicityMultiplier,

    @Nullable
    Date date,

    @Nullable
    String description,

    @Nullable
    Boolean withIssues,

    @Nullable
    Instant deleteDate
) {
}
