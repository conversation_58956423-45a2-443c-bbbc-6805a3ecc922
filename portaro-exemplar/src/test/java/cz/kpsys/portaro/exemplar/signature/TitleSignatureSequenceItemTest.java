package cz.kpsys.portaro.exemplar.signature;

import cz.kpsys.portaro.sequence.SequenceItem;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("ci")
@Tag("unit")
public class TitleSignatureSequenceItemTest {
    
    
    @Test
    public void shouldIncrementOnlyBodyAndIgnoreSuffixWhenTitleSignaturesEnabledAndFirstExemplar() {
        int body = 123;
        int bodyLength = 5;
        String prefix = "FF";
        String prefixDelim = "#";
        String suffixType = "a";
        String suffixDelim = "~";
        boolean firstWithSuffix = false;

        TitleSignatureSequence definition = new TitleSignatureSequence(5L, prefix, prefixDelim, bodyLength, body);
        SequenceItem seq = definition.createLast();

        seq = seq.next();
        Assertions.assertEquals("FF 00124", seq.getValue());

        seq = seq.next();
        Assertions.assertEquals("FF 00124", seq.getValue());
    }


    @Test
    public void shouldIncrementNothingAndIgnoreSuffixWhenTitleSignaturesEnabledAndSecondExemplar() {
        int body = 124;
        int bodyLength = 5;
        String prefix = "FF";
        String prefixDelim = "#";
        String suffixType = "a";
        String suffixDelim = "~";
        boolean firstWithSuffix = false;

        TitleSignatureSequence definition = new TitleSignatureSequence(5L, prefix, prefixDelim, bodyLength, body);
        SequenceItem seq = definition.createKnown("FF 00124");

        seq = seq.next();
        Assertions.assertEquals("FF 00124", seq.getValue());

        seq = seq.next();
        Assertions.assertEquals("FF 00124", seq.getValue());
    }

    
    @Test
    public void shouldCorrectlyInterpretTULSignatureWhenBodyIsAlreadyOut() {
        int body = 15355;
        int bodyLength = 1;
        String prefix = "B";
        String prefixDelim = "#";
        String suffixType = "a";
        String suffixDelim = "#";
        boolean firstWithSuffix = false;

        TitleSignatureSequence definition = new TitleSignatureSequence(5L, prefix, prefixDelim, bodyLength, body);
        SequenceItem seq = definition.createKnown("B 14009");

        seq = seq.next();
        Assertions.assertEquals("B 14009", seq.getValue());

        seq = seq.next();
        Assertions.assertEquals("B 14009", seq.getValue());
    }


    @Test
    public void shouldCorrectlyInterpretOtherTULSignatureWhenBodyIsAlreadyOut() {
        int body = 15355;
        int bodyLength = 1;
        String prefix = "B";
        String prefixDelim = "#";
        String suffixType = "a";
        String suffixDelim = "#";
        boolean firstWithSuffix = false;

        TitleSignatureSequence definition = new TitleSignatureSequence(5L, prefix, prefixDelim, bodyLength, body);
        Assertions.assertFalse(definition.matches("CD 14009"));
        SequenceItem seq = definition.createLast();

        seq = seq.next();
        Assertions.assertEquals("B 15356", seq.getValue());

        seq = seq.next();
        Assertions.assertEquals("B 15356", seq.getValue());
    }


    @Test
    public void shouldNotGetAbsoluteAuthenticityForFirstValue() {
        TitleSignatureSequence definition = new TitleSignatureSequence(5L, "B", "#", 1, 168);
        SequenceItem seq = definition.createLast();

        Assertions.assertNotSame(1.0, seq.getAuthenticity());
    }

    
    @Test
    public void shouldGetAbsoluteAuthenticityForNextValue() {
        TitleSignatureSequence definition = new TitleSignatureSequence(5L, "B", "#", 1, 168);
        SequenceItem seq = definition.createKnown("B 5555");

        seq = seq.next();
        Assertions.assertEquals(1.0, seq.getAuthenticity(), 0.001);
    }
    
}
