package cz.kpsys.portaro.sip2.server.adapter;

import com.ceridwen.circulation.SIP.messages.FeePaid;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.server.Sip2ServerRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

public record Sip2FeePaidServerRequest(@NonNull FeePaid message) implements Sip2ServerRequest<FeePaid>, TerminalPasswordedServerRequest {

    @NonNull
    @Override
    public CommandType type() {
        return CommandType.FEE_PAID_REQUEST;
    }

    @NonNull
    @Override
    public String getTerminalPassword() {
        return message.getTerminalPassword();
    }

    @NonNull
    @NotBlank
    public String getPatronIdentifier() {
        return StringUtil.requireNotBlank(message.getPatronIdentifier(), "Patron identifier cannot be empty");
    }
}
