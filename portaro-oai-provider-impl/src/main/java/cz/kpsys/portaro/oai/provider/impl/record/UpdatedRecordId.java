package cz.kpsys.portaro.oai.provider.impl.record;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class UpdatedRecordId extends BasicIdentified<UUID> implements Identified<UUID> {

    @Nullable Instant updateDate;

    private UpdatedRecordId(@NonNull UUID id,
                            @Nullable Instant updateDate) {
        super(id);
        this.updateDate = updateDate;
    }

    public static UpdatedRecordId ofKnownUpdateDate(@NonNull UUID id, @NonNull Instant updateDate) {
        return new UpdatedRecordId(id, updateDate);
    }

    public static UpdatedRecordId ofUnknownUpdateDate(@NonNull UUID id) {
        return new UpdatedRecordId(id, null);
    }

}
