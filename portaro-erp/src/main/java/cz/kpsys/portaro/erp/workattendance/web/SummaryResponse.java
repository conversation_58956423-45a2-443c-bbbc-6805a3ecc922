package cz.kpsys.portaro.erp.workattendance.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NonNull;

/// @param commitment Fond - pocet dni bez vikendu (ale se svatkama) * {denni_uva<PERSON>k}
/// @param holiday Sv. (kolik bylo svatků)
/// @param work Souhrnné údaje o odpracovaných hodinách a plánu
/// @param overtime Souhrnné údaje o přesčasech
/// @param onCallDuty Souhrnný údaj o počtu hodin pohotovosti
/// @param absence Souhrnné údaje o absenci
public record SummaryResponse(

        @Schema(description = "Fond - pocet dni bez vikendu (ale se svatkama) * {denni_uvazek}")
        @NonNull
        DescriptedDurationItemResponse commitment,

        @Schema(description = "Sv. (kolik bylo svatků)")
        @NonNull
        DescriptedDurationItemResponse holiday,

        @NonNull
        WorkSummaryResponse work,

        @NonNull
        SummaryOvertimeResponse overtime,

        @NonNull
        SummaryAbsenceResponse absence,

        @NonNull
        DescriptedDurationItemResponse onCallDuty

) {}
