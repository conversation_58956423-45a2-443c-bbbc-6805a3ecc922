package cz.kpsys.portaro.erp.workattendance.web;

import cz.kpsys.portaro.commons.localization.Text;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Duration;
import java.util.List;

public record AbsenceDayAttendanceItemResponse(

        @NonNull
        Duration duration,

        @NonNull
        List<AbsenceReasonResponse> reasons,

        @NonNull
        Text description,

        @Nullable
        Text errorDescription

) implements DurationItemResponse {}
