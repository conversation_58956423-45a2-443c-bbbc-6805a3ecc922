package cz.kpsys.portaro.erp.workattendance.web;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobContract.JobData.PersonNumber;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.Holiday;
import cz.kpsys.portaro.erp.workattendance.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.web.GenericApiController;
import cz.kpsys.portaro.web.swagger.SwaggerConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.Instant;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.Day.Date;
import static java.time.DayOfWeek.SATURDAY;
import static java.time.DayOfWeek.SUNDAY;

@Tag(name = "work-attendance", description = "ERP Work Attendance API")
@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WorkAttendanceApiController extends GenericApiController {

    public static final String OVERVIEW_PATH = "/api/work-attendance/overview";
    public static final String DAYS_PATH = "/api/work-attendance/days";

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ActivitySearchLoader activitySearchLoader;
    @NonNull Provider<ZoneId> timeZoneProvider;
    @NonNull WorkAttendanceReportLoader workAttendanceReportLoader;
    @NonNull WorkAttendanceToResponseMapper workAttendanceToResponseMapper;
    @NonNull RecordDayIdLoader recordDayIdLoader;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull RangedJobFilesByUserRecordIdLoader rangedJobFilesByUserRecordIdLoader;
    @NonNull RangedWorkCommitmentByUserRecordIdLoader rangedWorkCommitmentByUserRecordIdLoader;

    @Operation(summary = "Get overview data for work-attendance table")
    @GetMapping(OVERVIEW_PATH)
    public WorkAttendanceOverviewResponse getOverview(@RequestParam("userRecordId")
                                                      @Parameter(description = "Record id of user, data will be generated for", example = "0193bb11-c572-7424-8743-************", required = true)
                                                      UUID userRecordId,

                                                      @RequestParam(value = "month", required = false)
                                                      @Parameter(schema = @Schema(implementation = YearMonth.class), description = "Month, data will be generated for", example = SwaggerConstants.SCHEMA_EXAMPLE_YEAR_MONTH)
                                                      YearMonth yearMonth,

                                                      @CurrentDepartment Department ctx) {

        ZoneId zoneId = timeZoneProvider.get();
        YearMonth defaultedYearMonth = yearMonth == null
                ? YearMonth.now()
                : yearMonth;
        DateRange period = DateRange.ofMonth(defaultedYearMonth, zoneId);
        Record userRecord = recordLoader.getById(userRecordId);

        List<Record> jobFiles = rangedJobFilesByUserRecordIdLoader.load(userRecordId, period, ctx);
        Assert.state(jobFiles.size() <= 1, () -> "Too many job contracts loaded! %s".formatted(jobFiles));

        List<UUID> daysInPeriod = recordDayIdLoader.load(period);
        var recordDaysInPeriod = Objects.requireNonNull(idsToRecordsConverter.convert(daysInPeriod));
        EnrichedLocalDates days = enrichDays(recordDaysInPeriod);

        WorkAtendanceReport attendanceSystemReport;
        WorkCommitment workCommitment;
        if (jobFiles.isEmpty()) { // No active job file at specified time
            attendanceSystemReport = null;
            workCommitment = WorkCommitment.NONE;
        } else {
            Record jobFile = jobFiles.getFirst();
            String personalNumber = jobFile.getFirst(PersonNumber.FIELD_FINDER);
            attendanceSystemReport = workAttendanceReportLoader.load(List.of(personalNumber), days, ctx);
            workCommitment = rangedWorkCommitmentByUserRecordIdLoader.load(userRecordId, period, ctx);
        }

        List<Activity> activities = activitySearchLoader.load(userRecord, daysInPeriod, ctx);

        return workAttendanceToResponseMapper.mapAll(workCommitment, activities, attendanceSystemReport, days);
    }

    @Operation(summary = "Get days for work-attendance (days only) table")
    @GetMapping(DAYS_PATH)
    public List<DayAttendanceResponse> getDays(@RequestParam("userRecordId")
                                               @Parameter(description = "Record id of user, data will be generated for", example = "0193bb11-c572-7424-8743-************", required = true)
                                               UUID userRecordId,

                                               @RequestParam("fromDate")
                                               @Parameter(description = "Inclusive from-date", required = true, example = SwaggerConstants.SCHEMA_EXAMPLE_FROM_INSTANT)
                                               Instant fromDate,

                                               @RequestParam("toDate")
                                               @Parameter(description = "Exclusive to-date", required = true, example = SwaggerConstants.SCHEMA_EXAMPLE_TO_INSTANT)
                                               Instant toDate,

                                               @CurrentDepartment Department ctx) {

        DateRange period = new DateRange(fromDate, toDate);
        Record userRecord = recordLoader.getById(userRecordId);

        List<Record> jobFiles = rangedJobFilesByUserRecordIdLoader.load(userRecordId, period, ctx);

        List<UUID> daysInPeriod = recordDayIdLoader.load(period);
        var recordDaysInPeriod = Objects.requireNonNull(idsToRecordsConverter.convert(daysInPeriod));
        EnrichedLocalDates days = enrichDays(recordDaysInPeriod);

        List<String> personalNumbers = jobFiles.stream()
                .map(jobFile -> jobFile.getFirst(PersonNumber.FIELD_FINDER))
                .toList();
        Assert.notEmpty(personalNumbers, () -> "Cannot find any personal number for user %s".formatted(userRecord));

        WorkAtendanceReport attendanceSystemReport = workAttendanceReportLoader.load(personalNumbers, days, ctx);
        List<Activity> activities = activitySearchLoader.load(userRecord, daysInPeriod, ctx);
        DayEnrichedActivities enrichedActivities = DayEnrichedActivities.fromActivities(activities, days);

        return workAttendanceToResponseMapper.mapDays(enrichedActivities, attendanceSystemReport.days(), days);
    }


    private EnrichedLocalDates enrichDays(List<? extends Record> recordDaysInPeriod) {
        return new EnrichedLocalDates(recordDaysInPeriod.stream()
                .map(WorkAttendanceApiController::enrichRecordDay)
                .sorted(Comparator.comparing(EnrichedLocalDate::value))
                .toList());
    }

    private static EnrichedLocalDate enrichRecordDay(Record recordDay) {
        var day = recordDay.getFirst(Date.FIELD_FINDER);
        var isHoliday = recordDay.findFirst(Holiday.Value.FIELD_FINDER).orElse(false);

        Set<DateLabel> dateLabels = new HashSet<>();
        if (isHoliday) {
            dateLabels.add(DateLabel.HOLIDAY);
        }
        if (day.getDayOfWeek() == SATURDAY) {
            dateLabels.add(DateLabel.SATURDAY);
        }
        if (day.getDayOfWeek() == SUNDAY) {
            dateLabels.add(DateLabel.SUNDAY);
        }
        return new EnrichedLocalDate(day, dateLabels);
    }

}
