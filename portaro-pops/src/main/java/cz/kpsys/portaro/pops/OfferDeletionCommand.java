package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import jakarta.servlet.http.HttpSession;
import lombok.NonNull;

public record OfferDeletionCommand(
        @NonNull Integer offerId,
        @NonNull User activeUser,
        @NonNull Department currentDepartment,
        @NonNull UserAuthentication currentAuth,
        @NonNull HttpSession session
) {
}
