package cz.kpsys.portaro.pops.agreement;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.signatures.SignatureUtil;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.pdf.PdfTextExtractor;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;

@Slf4j
public class ITextPdfAgreementFileVerifier implements AgreementFileVerifier {

    @Override
    public AgreementFileVerificationResult verifyAgreement(LoadedIdentifiedFile loadedSignedAgreementFile) {
        log.info("Verification of pdf signature of supplier company {}", loadedSignedAgreementFile.getFilename());

        try (PdfReader pdfReader = new PdfReader(new ByteArrayInputStream(loadedSignedAgreementFile.getInputStream().readAllBytes()));
             PdfDocument document = new PdfDocument(pdfReader)
        ) {

            String pdfContent = new PdfTextExtractor(document).extractAllPages();
            if (pdfContent.isEmpty()) {
                return new AgreementFileVerificationResult(false, Texts.ofMessageCoded("file.PdfFileEmpty"));
            }

            SignatureUtil signatureUtil = new SignatureUtil(document);
            List<String> signatureNames = signatureUtil.getSignatureNames();

            if (signatureNames.isEmpty()) {
                log.info("Given pdf is not signed");
                return new AgreementFileVerificationResult(false, Texts.ofMessageCoded("file.PdfFileNotSigned"));
            }

            log.info("Pdf is signed (signatures: {}). Total revisions: {}.", signatureNames, signatureUtil.getTotalRevisions());

            for (String signatureName : signatureNames) {
                boolean coversWholeDocument = signatureUtil.signatureCoversWholeDocument(signatureName);
                if (!coversWholeDocument) {
                    return new AgreementFileVerificationResult(false, Texts.ofMessageCoded("file.PdfSignature"));
                }
                log.info("Pdf signature {}, revision {}", signatureNames, signatureUtil.getRevision(signatureName));
            }

            return new AgreementFileVerificationResult(true, null);

        } catch (IOException e) {
            throw ObjectUtil.toRuntimeEx(e);
        }
    }
}
