package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.Optional;

import static cz.kpsys.portaro.pops.agreement.AgreementStyle.FILE_SIGNING;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SignedFileVerifyingAgreementConfirmer implements AgreementConfirmer {

    @NonNull AgreementFileVerifier agreementFileVerifier;
    @NonNull Saver<Agreement, ?> agreementSaver;
    @NonNull Provider<AgreementStyle> agreementStyleProvider;

    @Transactional
    @Override
    public void confirmAgreement(Agreement agreement) throws AgreementConfirmationException {
        if (agreementStyleProvider.get() == FILE_SIGNING) {
            Assert.isInstanceOf(LoadedSignedFileAgreement.class, agreement);

            Optional<? extends LoadedIdentifiedFile> signedFile = ((LoadedSignedFileAgreement) agreement).getSignedFile();
            Assert.isTrue(signedFile.isPresent(), "Signed agreement file must be present to validate it");

            AgreementFileVerificationResult verificationResult = agreementFileVerifier.verifyAgreement(signedFile.get());
            if (!verificationResult.success()) {
                throw new AgreementConfirmationException(verificationResult.message().toString(), verificationResult.message());
            }
        }

        agreement.confirm(Instant.now());
        agreementSaver.save(agreement);
    }
}
