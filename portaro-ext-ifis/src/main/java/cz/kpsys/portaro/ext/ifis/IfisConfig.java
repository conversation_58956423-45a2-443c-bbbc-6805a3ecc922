package cz.kpsys.portaro.ext.ifis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kagkarlsson.scheduler.task.Task;
import com.github.kagkarlsson.scheduler.task.helper.Tasks;
import com.github.kagkarlsson.scheduler.task.schedule.Schedules;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.payment.Payment;
import cz.kpsys.portaro.record.export.batch.*;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import feign.RequestInterceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.UUID;
import java.util.function.Supplier;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IfisConfig {

    public static final String TRANSFER_ID = "0d7fd00c-be79-4ca7-871c-01aa35ebfd5e";

    @NonNull SettingLoader settingLoader;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull TransferredBatchLoader transferredBatchLoader;
    @NonNull Saver<TransferredBatch, TransferredBatchEntity> isolatedTransferredBatchSaver;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Payment> paymentSearchLoader;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull RequestInterceptor userAgentAddingInterceptor;
    @NonNull ObjectMapper xmlMapper;

    @Bean
    public BatchTransferer ifisBatchTransferrer() {
        return new ConditionallyEnabledBatchTransferer(
                settingLoader.getOnRootProvider(IfisSettingKeys.IFIS_ENABLED),
                new SimpleBatchTransferer(
                        nextBatchSupplier(),
                        isolatedTransferredBatchSaver,
                        rootDepartmentProvider,
                        ifisBatchRunner()
                )
        );
    }

    @Bean
    public IfisBatchRunner ifisBatchRunner() {
        return new IfisBatchRunner(
                settingLoader.getDepartmentedProvider(IfisSettingKeys.IFIS_SOAP_ENVELOPE_USERNAME).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(IfisSettingKeys.IFIS_SOAP_ENVELOPE_PASSWORD).throwingWhenNull(),
                paymentSearchLoader,
                new DynamicContextualIfisClient<>(
                        new IfisClientContextualProvider<>(
                                settingLoader.getDepartmentedProvider(IfisSettingKeys.IFIS_SOAP_URL).throwingWhenNull(),
                                userAgentAddingInterceptor,
                                xmlMapper
                        )
                ),
                localizer
        );
    }

    @Bean
    public Supplier<TransferredBatch> nextBatchSupplier() {
        return new NextBatchSupplier(
                new LastSuccessfulBatchSupplier(
                        transferIdProvider(),
                        transferredBatchLoader
                ),
                transferIdProvider(),
                Duration.ofDays(30)
        );
    }

    @Bean
    public Supplier<@NonNull UUID> transferIdProvider() {
        return StaticProvider.of(UUID.fromString(TRANSFER_ID));
    }

    @Bean
    public Task<Void> ifisExportTask() {
        return Tasks.recurring("integ-ifis-export-task", Schedules.fixedDelay(Duration.ofMinutes(20)))
                .execute((taskInstance, executionContext) -> ifisBatchTransferrer().transferSingleBatch());
    }

}