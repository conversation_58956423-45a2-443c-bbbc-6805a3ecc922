package cz.kpsys.portaro.batch.db;

import cz.kpsys.portaro.batch.ChunkLoader;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.search.Chunk;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RangePagingJdbcChunkLoader<T> implements ChunkLoader<T, Object, RangePaging> {

    @NonNull QueryFactory queryFactory;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull String table;
    @NonNull RowMapper<T> rowMapper;
    @NonNull Integer pageSize;
    @NonNull Consumer<String> logger;

    @Override
    public Chunk<T, RangePaging> loadFirst(@NonNull Object params) {
        return loadNext(params, RangePaging.forFirstPage(pageSize));
    }

    @Override
    public Chunk<T, RangePaging> loadNext(@NonNull Object params, @NonNull RangePaging cursor) {
        Range range = cursor.range();

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(table);
        sq.setRange(range);

        logger.accept("Table %s: reading rows %s-%s: %s".formatted(table, range.getInclusiveFrom(), range.getInclusiveTo(), sq.getRevealedParamsQuery()));

        TimeMeter tm = TimeMeter.start();
        List<T> results = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), rowMapper);

        logger.accept("Table %s: reading %s rows completed in %s (%s)".formatted(table, results.size(), tm.elapsedTimeString(), tm.toCurrentRateString(results.size())));

        if (results.size() < cursor.pageSize()) {
            return Chunk.ofDefinitelyFinal(results);
        }
        return Chunk.of(results, cursor.incrementPage());
    }

    @Override
    public String toString() {
        return "RangePagingLoader{table %s}".formatted(table);
    }
}
