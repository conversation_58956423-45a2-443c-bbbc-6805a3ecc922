package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.authority.RichAuthorityRowMapper;
import cz.kpsys.portaro.record.document.RichDocumentRowMapper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class InternalRecordLoadConfig {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Codebook<RecordStatus, Integer> recordStatusLoader;
    @NonNull CacheService cacheService;


    @Bean
    public InternalRecordLoader internalRecordAllByIdsLoader() {
        RowMapper<Record> documentRowMapper = new RichDocumentRowMapper(fondLoader, recordStatusLoader);
        RowMapper<Record> authorityRowMapper = new RichAuthorityRowMapper(fondLoader, recordStatusLoader);
        return new CachedInternalRecordLoader(recordCache(), new SpringDbAllDatabasedRichRecordsByIdsLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                documentRowMapper,
                authorityRowMapper
        ));
    }

    @Bean
    public DynamicCache<Record> recordCache() {
        GuavaTimedDynamicCache<Record> bean = new GuavaTimedDynamicCache<>(Record::getId, Duration.ofSeconds(10), false);
        cacheService.registerCleaner(Record.class.getSimpleName(), bean::clear);
        return bean;
    }
}
