package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.github.kagkarlsson.scheduler.task.Task;
import com.github.kagkarlsson.scheduler.task.helper.Tasks;
import com.github.kagkarlsson.scheduler.task.schedule.Schedules;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.ThisThreadAuthenticatingContextualRunner;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerToAnyConverter;
import cz.kpsys.portaro.commons.json.ByIntConverterJsonDeserializer;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.record.IdsToRecordsConverter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.view.SearchTextResolver;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.sdi.*;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.format.FormatterRegistry;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.LocalTime;
import java.util.UUID;
import java.util.function.Consumer;

import static cz.kpsys.portaro.databasestructure.UserDb.OPAC_SDI_SENDINGS.SEQ_ID_OPAC_SDI_SENDING_FB;
import static cz.kpsys.portaro.databasestructure.UserDb.OPAC_SDI_SENDINGS.SEQ_ID_OPAC_SDI_SENDING_PG;
import static cz.kpsys.portaro.databasestructure.UserDb.SDI.SEQ_ID_OPAC_SDI_FB;
import static cz.kpsys.portaro.databasestructure.UserDb.SDI.SEQ_ID_OPAC_SDI_PG;
import static cz.kpsys.portaro.security.PermissionResolver.*;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SdiConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull FormatterRegistry conversionService;
    @NonNull SimpleModule objectMapperModule;
    @NonNull TemplateEngine templateEngine;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordParagraphItemsConverter;
    @NonNull MailService mailService;
    @NonNull AllByIdsLoadable<Record, UUID> richRecordLoader;
    @NonNull AllByIdsLoadable<Record, Integer> documentByKindedIdLoader;
    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService;
    @NonNull Translator<Department> translator;
    @NonNull ByIdLoadable<User, Integer> userLoader;
    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, UUID> recordOperationRecordIdSearchLoader;
    @NonNull SecurityManager securityManager;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<User> portaroUserProvider;
    @NonNull SearchTextResolver searchTitleResolver;
    @NonNull CacheDeletableById recordCache;
    @NonNull EntityManager entityManager;
    @NonNull ContextualProvider<Department, Converter<UUID, @NonNull String>> recordIdToRecordDetailUrlConverterProvider;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;


    @Bean
    public SdiApiController sdiApiController() {
        return new SdiApiController(
                sdiService(),
                sdiRequestLoader(),
                sdiRequestDeleter(),
                sdiSendingLoader(),
                securityManager,
                sdiRequestCreator(),
                sdiRequestUpdater()
        );
    }

    @Bean
    public SdiRequestCreator sdiRequestCreator() {
        return new SdiRequestCreator(
                sdiRequestSaver(),
                ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofSequenceValueDbDependent(SEQ_ID_OPAC_SDI_FB, SEQ_ID_OPAC_SDI_PG, notAutoCommittingJdbcTemplate, queryFactory, databaseProperties)),
                defaultTransactionTemplateFactory.get(),
                securityManager,
                userLoader
        );
    }

    @Bean
    public SdiRequestUpdater sdiRequestUpdater() {
        return new SdiRequestUpdater(
                sdiRequestLoader(),
                sdiRequestSaver(),
                securityManager,
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public SdiRequestLoader sdiRequestLoader() {
        return new SpringDbSdiRequestLoader(notAutoCommittingJdbcTemplate, queryFactory, basicUserLoader, departmentLoader, readonlyTransactionTemplateFactory.get());
    }

    @Bean
    public Saver<SdiRequest, SdiRequest> sdiRequestSaver() {
        return new PostConvertingSaver<>(
                new PreConvertingSaver<>(
                        new SdiRequestToEntityConverter(),
                        new FlushingJpaSaver<>(new SimpleJpaRepository<>(SdiRequestEntity.class, entityManager))
                ),
                new SdiRequestFromEntityConverter(userLoader, departmentLoader)
        );
    }

    @Bean
    public Deleter<SdiRequest> sdiRequestDeleter() {
        return new TransactionalDeleter<>(
                new SdiRequestDeleter(
                        sdiRequestSaver()
                ),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public SdiSendingLoader sdiSendingLoader() {
        return new SpringDbSdiSendingLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public Saver<SdiSending, SdiSending> sdiSendingSaver() {
        return new PostConvertingSaver<>(
                new PreConvertingSaver<>(
                        new SdiSendingToEntityConverter(),
                        new FlushingJpaSaver<>(new SimpleJpaRepository<>(SdiSendingEntity.class, entityManager))
                ),
                new SdiSendingFromEntityConverter()
        );
    }

    @Bean
    public SdiSendingCreator sdiSendingCreator() {
        return new SdiSendingCreator(
                Provider.of(IntegerValueDatabaseLoader.ofSequenceValueDbDependent(SEQ_ID_OPAC_SDI_SENDING_FB, SEQ_ID_OPAC_SDI_SENDING_PG, notAutoCommittingJdbcTemplate, queryFactory, databaseProperties)),
                defaultTransactionTemplateFactory.get(),
                sdiSendingSaver()
        );
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<SdiRequestCreationRequest> sdiRequestCreationRequestDefaulter() {
        return new SdiRequestCreationRequestDefaulter(settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE), searchTitleResolver, translator);
    }

    @Bean
    public SdiService sdiService() {
        return new SdiService(
                sdiRequestLoader(),
                sdiSendingLoader(),
                new IdsToRecordsConverter()
                        .withDocumentSupport(documentByKindedIdLoader)
                        .withRecordSupport(richRecordLoader),
                recordSearchService,
                mailService,
                templateEngine,
                recordsToViewableRecordParagraphItemsConverter,
                translator,
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                recordOperationTypeLoader,
                recordOperationRecordIdSearchLoader,
                rootDepartmentProvider,
                recordCache,
                recordIdToRecordDetailUrlConverterProvider,
                sdiSendingCreator()
        ).withTimeZone(CoreConstants.CZECH_TIME_ZONE_ID);
    }


    @Bean
    public Task<Void> sdiSendTask() {
        return Tasks.recurring("sdi-send-task", Schedules.daily(CoreConstants.CZECH_TIME_ZONE_ID, LocalTime.parse("05:00:00")))
                .execute((taskInstance, executionContext) -> sendAllSendableSdisRunner().accept(rootDepartmentProvider.get()));
    }


    @Bean
    public Consumer<Department> sendAllSendableSdisRunner() {
        return new ThisThreadAuthenticatingContextualRunner(authenticationHolder, portaroUserProvider, sdiService()::sendAllSendable);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        IdToObjectConverter<Integer, SdiRequest> integerToSdiRequestConverter = new IdToObjectConverter<>(sdiRequestLoader());
        conversionService.addConverter(Integer.class, SdiRequest.class, integerToSdiRequestConverter);
        conversionService.addConverter(String.class, SdiRequest.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToSdiRequestConverter));

        objectMapperModule.addDeserializer(Periodicity.class, new ByIntConverterJsonDeserializer<>(Periodicity.class, new IdToObjectConverter<>(Periodicity.CODEBOOK)));
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(SdiSecurityActions.SDI_USE, permissionFactory.currentEvidedAuthenticActive());

        permissionRegistry.add(SdiSecurityActions.ALL_SDI_REQUESTS_SHOW, permissionFactory.edit());

        permissionRegistry.add(SdiSecurityActions.USER_SDI_REQUESTS_SHOW, and(
                permissionFactory.subjectUserIsEvided(),
                or(
                        permissionFactory.currentEvidedAuthenticActiveIsSubjectUser(),
                        permissionFactory.editOnReadableDepartmentsOfUser()
                )
        ));

        permissionRegistry.add(SdiSecurityActions.SDI_SAVE, adaptingSubject(SdiRequest::getUser, permissionRegistry.getLazy(SdiSecurityActions.USER_SDI_REQUESTS_SHOW)));
    }

}
