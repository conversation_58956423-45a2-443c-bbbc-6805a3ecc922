package cz.kpsys.portaro.config;

import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.batch.CacheCleaningChunkLoader;
import cz.kpsys.portaro.batch.ChunkLoader;
import cz.kpsys.portaro.commons.async.AsyncProcessStatus;
import cz.kpsys.portaro.commons.async.AsyncProcessesManager;
import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.cache.CacheDeleter;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.StaticContextualDelegatingProvider;
import cz.kpsys.portaro.commons.convert.FallbackingConverter;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerToAnyConverter;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.Sequence;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.databasestructure.InventoryDb;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.ExemplarDescriptor;
import cz.kpsys.portaro.exemplar.discard.ExemplarDiscarder;
import cz.kpsys.portaro.exemplar.exchangesetitem.SpringDbExchangeSetItemEntityLoader;
import cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus;
import cz.kpsys.portaro.exemplar.loancategory.LoanCategory;
import cz.kpsys.portaro.exemplar.thematicgroup.ThematicGroup;
import cz.kpsys.portaro.export.*;
import cz.kpsys.portaro.inventory.InventoryConstants;
import cz.kpsys.portaro.inventory.InventorySecurityActions;
import cz.kpsys.portaro.inventory.api.CaptureApiController;
import cz.kpsys.portaro.inventory.api.InventoryApiController;
import cz.kpsys.portaro.inventory.api.InventoryPageApiController;
import cz.kpsys.portaro.inventory.api.MatchApiController;
import cz.kpsys.portaro.inventory.capture.*;
import cz.kpsys.portaro.inventory.discardion.InventoryExemplarDiscarder;
import cz.kpsys.portaro.inventory.discardion.InventoryExemplarDiscardionRequest;
import cz.kpsys.portaro.inventory.execution.InventoryProcessExecutor;
import cz.kpsys.portaro.inventory.execution.MatchFinder;
import cz.kpsys.portaro.inventory.execution.QuantumLoansChangesSumByExemplarLoader;
import cz.kpsys.portaro.inventory.inventory.*;
import cz.kpsys.portaro.inventory.match.*;
import cz.kpsys.portaro.licence.FeatureEnabledProvider;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.LoanQuantityLoader;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.location.LocationImpl;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.batch.BySearchLoaderRangePagingChunkLoader;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResolver;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.ZoneId;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static cz.kpsys.portaro.exemplar.ExemplarSettingKeys.INVENTORY_DISCARD_ONLY_TWICE_NOT_FOUND;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class InventoryConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull EntityManager entityManager;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<Location, Integer> locationLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull ByIdLoadable<LoanCategory, String> loanCategoryLoader;
    @NonNull ByIdLoadable<ThematicGroup, String> thematicGroupLoader;
    @NonNull Codebook<ExemplarStatus, Integer> exemplarStatusLoader;
    @NonNull ByIdLoadable<? extends ExemplarDescriptor, Integer> exemplarDescriptorLoader;
    @NonNull IdAndIdsLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedDocumentLoader;
    @NonNull IdAndIdsLoadable<Exemplar, Integer> exemplarLoader;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull Provider<@NonNull ZoneId> defaultTimeZoneProvider;
    @NonNull ContextualProvider<Department, BarCodeValidator> exemplarBarCodeValidatorProvider;
    @NonNull Provider<Department> realRootDepartmentProvider;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Exemplar> exemplarSearchLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Loan> nonDetailedLoanSearchLoader;
    @NonNull LoanQuantityLoader loanQuantityLoader;
    @NonNull ExecutorService executorService;
    @NonNull ExemplarDiscarder exemplarDiscarder;
    @NonNull SettingLoader settingLoader;
    @NonNull AuthenticatedContextualProvider<Department, List<LabeledIdentified<String>>> discardNumberSequenceAuthenticatedContextualProvider;
    @NonNull FeatureManager featureManager;
    @NonNull ExporterResolver exporterResolver;
    @NonNull StaticExportDescriptorLoader staticExportDescriptorLoader;
    @NonNull TemplateEngine templateEngine;
    @NonNull CompositeExportDescriptorLoader exportDescriptorLoader;
    @NonNull CacheDeleter<Record> recordCache;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;

    @Bean
    public InventoryApiController inventoryApiController() {
        return new InventoryApiController(
                inventoryLoader(),
                inventoryCreator(),
                inventoryUpdater(),
                inventoryProcessExecutor(),
                inventoryRemover(),
                inventoryCloser(),
                inventoryExemplarDiscarder(),
                executionProcessesManager(),
                discardionProcessesManager());
    }

    @Bean
    InventoryPageApiController inventoryPageApiController() {
        return new InventoryPageApiController(new FeatureEnabledProvider(featureManager, FeatureManager.FEATURE_EXPORTS), exportDescriptorLoader);
    }

    @Bean
    public CaptureApiController captureApiController() {
        return new CaptureApiController(captureSaver(), exemplarLoader, nonDetailedDocumentLoader);
    }

    @Bean
    public MatchApiController matchApiController() {
        return new MatchApiController(matchEditor());
    }

    @Bean Saver<Capture, Capture> captureSaver() {
        return saverBuilderFactory.<Capture, Integer>saver()
                .intermediateConverting(new CaptureToEntityConverter())
                .idSetting(CaptureEntity.class, new StringToIntegerConverter(), IdSettable::setId)
                .withClearedCacheName(CaptureEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public IdAndIdsLoadable<Inventory, Integer> inventoryLoader() {
        var entitiesToInventoriesConverter = new EntitiesToInventoriesConverter(
                basicUserLoader,
                fondLoader,
                departmentLoader,
                deprecatedIdFormatFallbackingStringIdToLocationConverter(),
                inventoryStateLoader()
        );
        return modelBeanBuilder.allByIdsLoader(InventoryEntity.class, entitiesToInventoriesConverter).build();
    }

    @Bean
    public Deleter<Inventory> inventoryDeleter() {
        return modelBeanBuilder.hibernateHardDeleter(InventoryEntity.class, new InventoryToEntityConverter()).build();
    }

    @NonNull
    private FallbackingConverter<String, Location> deprecatedIdFormatFallbackingStringIdToLocationConverter() {
        return FallbackingConverter.onConversionException(
                itemNotFoundFallbackingStringIdToLocationConverter(),
                (deprecatedLocationCode) -> new LocationImpl(-Sequence.getNextNumber(), deprecatedLocationCode, deprecatedLocationCode, 0)
        );
    }

    @NonNull
    private FallbackingConverter<String, Location> itemNotFoundFallbackingStringIdToLocationConverter() {
        return FallbackingConverter.onException(
                StringToIntegerToAnyConverter.strict(new IdToObjectConverter<>(locationLoader)),
                ItemNotFoundException.class,
                StringToIntegerToAnyConverter.strict(naId -> new LocationImpl(naId, "unknown-%s".formatted(naId), String.valueOf(naId), 0))
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Integer> inventoryIdSearchLoader() {
        PageSearchLoader<MapBackedParams, Integer, RangePaging> pureSearchLoader = new SpringDbInventoryIdSearchLoader(jdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, Integer, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return modelBeanBuilder.idSearchLoader(MapBackedParams::createEmpty, paramsConvertingSearchLoader).build();
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Inventory> inventorySearchLoader() {
        return modelBeanBuilder.modelSearchLoaderByIdSearchLoader(MapBackedParams::createEmpty, inventoryIdSearchLoader(), inventoryLoader()).build();
    }

    @Bean
    public CaptureByInventoryDeleter captureByInventoryDeleter() {
        return new CaptureByInventoryDeleter(queryFactory, jdbcTemplate);
    }

    @Bean
    public Deleter<Capture> captureDeleter() {
        return modelBeanBuilder.hibernateHardDeleter(CaptureEntity.class, new CaptureToEntityConverter()).build();
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Capture> captureSearchLoader() {
        PageSearchLoader<MapBackedParams, Capture, RangePaging> modelSearchLoader = new SpringDbCaptureSearchLoader(
                jdbcTemplate,
                queryFactory,
                new CaptureFromDtoConverter(inventoryLoader(), exemplarDescriptorLoader, CaptureWay.CODEBOOK),
                new StaticContextualDelegatingProvider<>(exemplarBarCodeValidatorProvider, realRootDepartmentProvider));

        return modelBeanBuilder.searchLoader(MapBackedParams::createEmpty, modelSearchLoader).build();
    }

    @Bean
    public IdAndIdsLoadable<Match, UUID> matchLoader() {
        var entitiesToMatchesConverter = new EntitiesToMatchesConverter(
                inventoryLoader(),
                matchStateLoader(),
                nonDetailedDocumentByKindedIdLoader,
                deprecatedIdFormatFallbackingStringIdToLocationConverter(),
                FallbackingConverter.onException(new IdToObjectConverter<>(loanCategoryLoader), ItemNotFoundException.class, naId -> new LoanCategory(naId, naId)),
                FallbackingConverter.onException(new IdToObjectConverter<>(thematicGroupLoader), ItemNotFoundException.class, naId -> new ThematicGroup(naId, naId, 0)),
                FallbackingConverter.onException(StringToIntegerToAnyConverter.strict(new IdToObjectConverter<>(exemplarStatusLoader)), ItemNotFoundException.class, StringToIntegerToAnyConverter.strict(naId -> new ExemplarStatus(naId, "unknown-%s".formatted(naId))))
        );
        return modelBeanBuilder.allByIdsLoader(MatchEntity.class, entitiesToMatchesConverter).build();
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, UUID> matchIdSearchLoader() {
        SpringDbMatchIdSearchLoader modelIdSearchLoader = new SpringDbMatchIdSearchLoader(jdbcTemplate, queryFactory);
        return modelBeanBuilder.idSearchLoader(MapBackedParams::createEmpty, modelIdSearchLoader).build();
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Match> matchSearchLoader() {
        return modelBeanBuilder.modelSearchLoaderByIdSearchLoader(MapBackedParams::createEmpty, matchIdSearchLoader(), matchLoader()).build();
    }

    @Bean
    public Codebook<MatchState, Integer> matchStateLoader() {
        return MatchState.CODEBOOK;
    }

    @Bean
    public Saver<Match, Match> matchSaver() {
        return saverBuilderFactory.<Match, UUID>saver()
                .intermediateConverting(new MatchToEntityConverter())
                .withClearedCacheName(MatchEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public MatchByInventoryDeleter matchByInventoryDeleter() {
        return new MatchByInventoryDeleter(queryFactory, jdbcTemplate);
    }

    @Bean
    public MatchFinder matchFinder() {
        return new MatchFinder(jdbcTemplate, queryFactory, matchLoader());
    }

    @Bean
    public MatchEditor matchEditor() {
        return new MatchEditor(
                captureSaver(),
                matchSaver(),
                exemplarLoader,
                captureSearchLoader(),
                captureDeleter(),
                matchFinder());
    }

    @Bean
    public Codebook<InventoryState, Integer> inventoryStateLoader() {
        return InventoryState.CODEBOOK;
    }


    @Bean
    public Saver<Inventory, InventoryEntity> inventorySaver() {
        return new PreConvertingSaver<>(
                new InventoryToEntityConverter(),
                new FlushingJpaSaver<>(new SimpleJpaRepository<>(InventoryEntity.class, entityManager))
        );
    }

    @Bean
    public InventoryCreator inventoryCreator() {
        return new InventoryCreator(
                ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofSequenceValue(InventoryDb.HL_REVI.SEQ_ID_HL_REVI, jdbcTemplate, queryFactory)),
                inventorySaver(),
                defaultTimeZoneProvider
        );
    }

    @Bean
    public InventoryUpdater inventoryUpdater() {
        return new InventoryUpdater(inventorySaver(), executionProcessesManager(), discardionProcessesManager());
    }

    @Bean
    public InventoryProcessExecutor inventoryProcessExecutor() {
        return new InventoryProcessExecutor(
                exemplarSearchLoader,
                new BySearchLoaderRangePagingChunkLoader<>(exemplarSearchLoader, 100),
                exemplarStatusLoader,
                matchByInventoryDeleter(),
                nonDetailedDocumentLoader,
                captureSearchLoader(),
                new QuantumLoansChangesSumByExemplarLoader(nonDetailedLoanSearchLoader, loanQuantityLoader),
                new SpringDbExchangeSetItemEntityLoader(jdbcTemplate, queryFactory),
                matchSaver(),
                matchFinder(),
                inventorySaver(),
                executionProcessStatusRepository(),
                executorService,
                defaultTimeZoneProvider,
                executionProcessesManager(),
                discardionProcessesManager(),
                recordCache
        );
    }

    @Bean
    public InventoryRemover inventoryRemover() {
        return new InventoryRemover(
                inventoryDeleter(),
                matchByInventoryDeleter(),
                captureByInventoryDeleter(),
                executionProcessesManager(),
                discardionProcessesManager());
    }

    @Bean
    public ByIdOptLoadableRepository<AsyncProcessStatus<Integer>, Integer> executionProcessStatusRepository() {
        return InMemoryRepository.ofIdentified();
    }

    @Bean
    public AsyncProcessesManager<Integer> executionProcessesManager() {
        return new AsyncProcessesManager<>(executionProcessStatusRepository());
    }

    @Bean
    public ByIdOptLoadableRepository<AsyncProcessStatus<Integer>, Integer> discardionProcessStatusRepository() {
        return InMemoryRepository.ofIdentified();
    }

    @Bean
    public AsyncProcessesManager<Integer> discardionProcessesManager() {
        return new AsyncProcessesManager<>(discardionProcessStatusRepository());
    }

    @Bean
    public InventoryCloser inventoryCloser() {
        return new InventoryCloser(
                inventorySaver(),
                defaultTimeZoneProvider,
                executionProcessesManager(),
                discardionProcessesManager());
    }

    @Bean
    public InventoryExemplarDiscarder inventoryExemplarDiscarder() {
        return new InventoryExemplarDiscarder(
                exemplarDiscarder,
                matchSaver(),
                exemplarLoader,
                matchSearchLoader(),
                new BySearchLoaderRangePagingChunkLoader<>(matchSearchLoader(), 100),
                settingLoader.getDepartmentedProvider(INVENTORY_DISCARD_ONLY_TWICE_NOT_FOUND),
                executorService,
                discardionProcessStatusRepository(),
                executionProcessesManager(),
                discardionProcessesManager()
        );
    }


    @Bean
    public TypedAuthenticatedContextualObjectModifier<InventoryExemplarDiscardionRequest> inventoryExemplarDiscardionRequestDefaulter() {
        return (request, ctx, currentAuth) -> request.withDiscardNumber(discardNumberSequenceAuthenticatedContextualProvider.getOn(currentAuth, ctx).getFirst().getId());
    }

    @Bean
    public ChunkLoader<Match, MapBackedParams, RangePaging> matchCacheCleaningChunkLoader() {
        return new CacheCleaningChunkLoader<>(
                new BySearchLoaderRangePagingChunkLoader<>(matchSearchLoader(), 100),
                matchCacheDeletable());
    }

    @Bean
    public FilteringChunkLoader<Inventory, Match> matchByInventoryFilteringChunkLoader() {
        return new FilteringChunkLoader<>(
                matchCacheCleaningChunkLoader(),
                (inventory, params) -> params.set(InventoryConstants.SearchParams.INVENTORY, List.of(inventory.id()))
        );
    }

    @Bean
    public CacheDeleter<Match> matchCacheDeletable() {
        return match -> recordCache.deleteFromCache(match.record());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerExports() {
        if (featureManager.isEnabled(FeatureManager.FEATURE_EXPORTS)) {
            Exporter<List<Match>> matchesCsvExporter = new CsvFileExporter<>("matches.csv", InventoryConstants.Template.TEMPLATE_INVENTORY_MATCHES_CSV, Match.class, "matches", templateEngine);
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.MATCHES_CSV, matchesCsvExporter);

            Exporter<List<Match>> matchesXlsExporter = new XlsFileExporterByCsvExporter<>("matches.xls", matchesCsvExporter);
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.MATCHES_XLS, matchesXlsExporter);

            Exporter<?> searchedLoansCsvExporter = SearchedItemsExporter.ofUnlimited(matchesCsvExporter).withCacheDeleter(matchCacheDeletable());
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.SEARCHED_MATCHES_CSV, searchedLoansCsvExporter);

            Exporter<?> searchedLoansXlsExporter = SearchedItemsExporter.ofUnlimited(matchesXlsExporter).withCacheDeleter(matchCacheDeletable());
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.SEARCHED_MATCHES_XLS, searchedLoansXlsExporter);

            Exporter<Inventory> inventoryCsvExporter = new ChunkLoadingDelegatingExporter<>(
                    matchesCsvExporter,
                    Inventory.class,
                    matchByInventoryFilteringChunkLoader());
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.INVENTORY_MATCHES_CSV, inventoryCsvExporter);

            Exporter<Inventory> inventoryXlsExporter = new ChunkLoadingDelegatingExporter<>(
                    matchesXlsExporter,
                    Inventory.class,
                    matchByInventoryFilteringChunkLoader());
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.INVENTORY_MATCHES_XLS, inventoryXlsExporter);

            var matchesPrintExporter = new TemplatedExporter<>("matches.html", Match.class, "matches", InventoryConstants.Template.TEMPLATE_INVENTORY_MATCHES_PRINT, templateEngine);
            Exporter<Inventory> inventoryPrintExporter = new ChunkLoadingDelegatingToStringExporter<>(
                    matchesPrintExporter,
                    Inventory.class,
                    matchByInventoryFilteringChunkLoader());
            exporterResolver.addStatic(InventoryConstants.Export.Exporters.INVENTORY_MATCHES_PRINT, inventoryPrintExporter);
        }

        staticExportDescriptorLoader
                .with(List.of(InventoryConstants.Export.Tag.INVENTORY, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), InventoryConstants.Export.Exporters.INVENTORY_MATCHES_CSV, Texts.ofMessageCoded("export.CsvButton"))
                .with(List.of(InventoryConstants.Export.Tag.INVENTORY, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), InventoryConstants.Export.Exporters.INVENTORY_MATCHES_XLS, Texts.ofMessageCoded("export.XlsButton"))
                .with(List.of(InventoryConstants.Export.Tag.INVENTORY, ExportConstants.Tag.HTML, ExportConstants.Tag.PRINT), InventoryConstants.Export.Exporters.INVENTORY_MATCHES_PRINT, Texts.ofMessageCoded("commons.tisk"))
                .with(List.of(InventoryConstants.Export.Tag.SEARCHED_MATCHES, ExportConstants.Tag.CSV, ExportConstants.Tag.DOWNLOAD), InventoryConstants.Export.Exporters.SEARCHED_MATCHES_CSV, Texts.ofMessageCoded("export.CsvButton"))
                .with(List.of(InventoryConstants.Export.Tag.SEARCHED_MATCHES, ExportConstants.Tag.XLS, ExportConstants.Tag.DOWNLOAD), InventoryConstants.Export.Exporters.SEARCHED_MATCHES_XLS, Texts.ofMessageCoded("export.XlsButton"));
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForIntegerId(Inventory.class, inventoryLoader())
                .registerForUuidId(Match.class, matchLoader())
                .registerForIntegerId(MatchState.class, matchStateLoader())
                .registerForIntegerId(InventoryState.class, inventoryStateLoader());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {

        PermissionResolver<Inventory> userIsOwner = (auth, ctx, inventory) -> PermissionResult.ifCan(inventory.creator().equals(auth.getActiveUser()), auth, () -> Texts.ofNative("User is not the owner"));

        permissionRegistry.add(InventorySecurityActions.INVENTORIES_SHOW, permissionFactory.edit());
        permissionRegistry.add(InventorySecurityActions.INVENTORY_SHOW, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_SHOW),
                permissionFactory.edit())
        );
        permissionRegistry.add(InventorySecurityActions.INVENTORY_CREATE, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_CREATE),
                permissionFactory.edit())
        );
        permissionRegistry.add(InventorySecurityActions.INVENTORY_EDIT, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_EDIT),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.INVENTORY_DELETE, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_DELETE),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.INVENTORY_CLOSE, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_CLOSE),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.START_INVENTORY_EXECUTION, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_EXECUTE),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.STOP_INVENTORY_EXECUTION, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_EXECUTE),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.START_INVENTORY_DISCARDION, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_DISCARD),
                permissionFactory.edit(),
                userIsOwner)
        );
        permissionRegistry.add(InventorySecurityActions.STOP_INVENTORY_DISCARDION, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_DISCARD),
                permissionFactory.edit(),
                userIsOwner)
        );

        permissionRegistry.add(InventorySecurityActions.MATCH_MANUAL_FIX, PermissionResolver.and(
                        permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_MANUAL_FIX),
                        PermissionResolver.adaptingSubject(Match::inventory, permissionRegistry.getLazy(InventorySecurityActions.INVENTORY_EDIT)),
                        permissionFactory.edit())
        );

        permissionRegistry.add(InventorySecurityActions.INVENTORY_CAPTURES_SHOW, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_CAPTURES_SHOW),
                permissionFactory.edit())
        );
        permissionRegistry.add(InventorySecurityActions.INVENTORY_CAPTURE_CREATE, PermissionResolver.and(
                permissionFactory.editAction(LibrarianPrivileges.ACTION_INVENTORY_CAPTURE_CREATE),
                permissionFactory.edit())
        );
    }

}
