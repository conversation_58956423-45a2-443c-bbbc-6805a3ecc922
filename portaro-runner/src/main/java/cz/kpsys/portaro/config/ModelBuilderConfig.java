package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.StaticCacheFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ModelBuilderConfig {

    @NonNull EntityManager entityManager;
    @NonNull StaticCacheFactory staticCacheFactory;
    @NonNull CacheService cacheService;
    @NonNull CacheManager cacheManager;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;

    @Bean
    public CodebookLoaderBuilderFactory codebookLoaderBuilderFactory() {
        return new BasicCodebookLoaderBuilderFactory(entityManager, staticCacheFactory, cacheService, cacheManager);
    }

    @Bean
    public CodebookLoaderBuilderFactory transactionalCodebookLoaderBuilderFactory() {
        return new TransactionalCodebookLoaderBuilderFactory(
                entityManager,
                staticCacheFactory,
                cacheService,
                cacheManager,
                readonlyTransactionTemplateFactory
        );
    }

    @Bean
    public CachedCodebookAssembly codebookAssembler() {
        return new CachedCodebookAssembly(cacheService, staticCacheFactory);
    }

}
