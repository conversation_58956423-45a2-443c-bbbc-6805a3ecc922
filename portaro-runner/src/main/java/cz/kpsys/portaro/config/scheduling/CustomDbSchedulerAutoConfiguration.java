package cz.kpsys.portaro.config.scheduling;

import com.github.kagkarlsson.scheduler.Scheduler;
import com.github.kagkarlsson.scheduler.boot.autoconfigure.DbSchedulerAutoConfiguration;
import com.github.kagkarlsson.scheduler.boot.config.DbSchedulerCustomizer;
import com.github.kagkarlsson.scheduler.boot.config.DbSchedulerProperties;
import com.github.kagkarlsson.scheduler.event.ExecutionInterceptor;
import com.github.kagkarlsson.scheduler.event.SchedulerListener;
import com.github.kagkarlsson.scheduler.stats.StatsRegistry;
import com.github.kagkarlsson.scheduler.task.Task;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;
import java.util.List;

public class CustomDbSchedulerAutoConfiguration extends DbSchedulerAutoConfiguration {

    public CustomDbSchedulerAutoConfiguration(@NonNull DbSchedulerProperties dbSchedulerProperties,
                                              @Qualifier("notCriticalDataSource") @NonNull DataSource notCriticalDataSource,
                                              @NonNull List<Task<?>> configuredTasks,
                                              @NonNull List<SchedulerListener> schedulerListeners,
                                              @NonNull List<ExecutionInterceptor> executionInterceptors) {
        super(dbSchedulerProperties, notCriticalDataSource, configuredTasks, schedulerListeners, executionInterceptors);
    }

    @Bean(destroyMethod = "stop")
    @Override
    public Scheduler scheduler(DbSchedulerCustomizer customizer, StatsRegistry registry) {
        return super.scheduler(customizer, registry);
    }
}
