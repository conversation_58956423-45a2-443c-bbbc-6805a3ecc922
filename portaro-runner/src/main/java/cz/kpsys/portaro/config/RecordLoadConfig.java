package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.authority.RecordLoaderDelegatingAuthorityLoader;
import cz.kpsys.portaro.record.detail.AppserverRecordDetailLoader;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl;
import cz.kpsys.portaro.record.detail.appservermarc.StringBuilderDetailToMarcXmlConverter;
import cz.kpsys.portaro.record.document.DocumentLoader;
import cz.kpsys.portaro.record.document.RecordDelegatingDocumentLoader;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.*;
import cz.kpsys.portaro.record.load.RecordFieldsLoader;
import cz.kpsys.portaro.record.load.RecordFieldsLoadingRecordDetailLoader;
import cz.kpsys.portaro.record.load.SwitchableRecordFieldsLoader;
import cz.kpsys.portaro.record.operation.*;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.UserLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.ZoneId;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

@Configuration
@Import({
        RecordLoadCommonConfig.class
})
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordLoadConfig {

    @NonNull ObjectMapper appserverXmlMapper;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SecurityManager securityManager;
    @NonNull SettingLoader settingLoader;
    @NonNull Provider<@NonNull ZoneId> databaseColumnsTimeZoneProvider;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull UserLoader userLoader;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull Provider<Department> realRootDepartmentProvider;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull CacheService cacheService;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;
    @NonNull RecordFieldsLoader legacyRecordFieldsLoader;
    @NonNull RecordFieldsLoader newRecordFieldsLoader;
    @NonNull InternalRecordLoader internalRecordAllByIdsLoader;
    @NonNull DynamicCache<Record> recordCache;


    @Bean
    public Provider<@NonNull Boolean> recordDetailPortaroLoadEnabled() {
        return settingLoader.getOnRootProvider(RecordSettingKeys.PORTARO_DETAIL_LOAD_ENABLED);
    }

    @Bean
    public Provider<@NonNull Boolean> recordDetailPortaroRecordFieldLoadEnabled() {
        return settingLoader.getOnRootProvider(RecordSettingKeys.PORTARO_DETAIL_RECORD_FIELD_LOAD_ENABLED);
    }

    @Bean
    public IdAndIdsLoadable<RecordEntity, UUID> recordEntityLoader() {
        return modelBeanBuilder.allByIdsLoader(RecordEntity.class);
    }

    @Bean
    public RecordFieldsLoader recordFieldsLoader() {
        return new SwitchableRecordFieldsLoader(
                recordDetailPortaroRecordFieldLoadEnabled(),
                newRecordFieldsLoader,
                legacyRecordFieldsLoader
        );
    }

    @Bean
    public AllByIdsLoadable<IdentifiedFieldContainer, Record> recordDetailLoader() {
        MarcXmlToDetailConverterImpl detailFromMarcXmlConstructor = new MarcXmlToDetailConverterImpl(
                false,
                fieldTypesByFondLoader,
                recordFieldStringToInstantConverter,
                fondLoader
        );
        var bean = new AppserverRecordDetailLoader(
                mappingAppserver,
                false,
                detailFromMarcXmlConstructor,
                fondLoader
        );
        ChunkingAllByIdsLoader<IdentifiedFieldContainer, Record, UUID> chunking = ChunkingAllByIdsLoader.of(bean, Identified::getId, Record::getId)
                .withChunkSize(50);

        var portaroRecordDetailLoader = new RecordFieldsLoadingRecordDetailLoader(
                recordFieldsLoader()
        );

        return new SwitchingAllByIdsLoader<>(
                recordDetailPortaroLoadEnabled(),
                portaroRecordDetailLoader,
                chunking
        );
    }

    @Bean
    public RichRecordLoader richRecordLoader() {
        return new DelegatingRichRecordsByIdsLoader(
                recordCache,
                recordDetailLoader(),
                internalRecordAllByIdsLoader,
                true
        );
    }

    @Bean
    public IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader() {
        return new DelegatingRichRecordsByIdsLoader(
                recordCache,
                recordDetailLoader(),
                internalRecordAllByIdsLoader,
                false
        );
    }

    @Bean
    public IdAndIdsLoadable<Record, Integer> authorityByKindedIdLoader() {
        return new ByIdLoadableByAllByIdsLoadable<>(richRecordLoader()::getDetailedAuthoritiesByKindedIds, Record.class);
    }

    @Bean
    public IdAndIdsLoadable<Record, Integer> nonDetailedAuthorityByKindedIdLoader() {
        return new ByIdLoadableByAllByIdsLoadable<>(richRecordLoader()::getAuthoritiesByKindedIds, Record.class);
    }

    @Bean
    public IdAndIdsLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader() {
        return new ByIdLoadableByAllByIdsLoadable<>(richRecordLoader()::getDocumentsByKindedIds, Record.class);
    }

    @Bean
    public IdAndIdsLoadable<Record, Integer> documentByKindedIdLoader() {
        return new ByIdLoadableByAllByIdsLoadable<>(richRecordLoader()::getDetailedDocumentsByKindedIds, Record.class);
    }

    @Bean
    public IdAndIdsLoadable<Record, UUID> nonDetailedDocumentLoader() {
        RecordDelegatingDocumentLoader documentLoader = new RecordDelegatingDocumentLoader(richRecordLoader(), false);
        return new ByIdLoadableByAllByIdsLoadable<>(documentLoader, Record.class);
    }

    @Bean
    public DocumentLoader detailedDocumentLoader() {
        return new RecordDelegatingDocumentLoader(richRecordLoader(), true);
    }

    @Bean
    public IdAndIdsLoadable<Record, UUID> authorityLoader() {
        return new RecordLoaderDelegatingAuthorityLoader(richRecordLoader());
    }


    @Bean
    public Converter<List<RecordOperationEntity>, List<? extends RecordOperation>> entitiesToRecordOperationsConverter() {
        return new EntitiesToRecordOperationsConverter(
                recordOperationTypeLoader(),
                departmentLoader,
                nonDetailedRichRecordLoader(),
                basicUserLoader
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, RecordOperation> recordOperationSearchLoader() {
        SpringDbRecordOperationEntitySearchLoader pureSearchLoader = new SpringDbRecordOperationEntitySearchLoader(jdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, RecordOperationEntity, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.OPERATED_SUBKIND, RecordConstants.SearchParams.OPERATED_FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.OPERATED_ROOT_FOND, RecordConstants.SearchParams.OPERATED_FOND, enabledLoadableFondsExpander);

        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ResultConvertingPageSearchLoader<>(
                        paramsConvertingSearchLoader,
                        entitiesToRecordOperationsConverter()
                )
        );
    }

    @Bean
    public CachingRecordEditLevelProvider recordEditLevelProvider() {
        RecordOperationSearchDelegatingRecordEditLevelProvider bean = new RecordOperationSearchDelegatingRecordEditLevelProvider(recordOperationSearchLoader(), recordOperationTypeLoader(), userLoader);
        CachingRecordEditLevelProvider cached = new CachingRecordEditLevelProvider(bean);
        cacheService.registerSpringCacheCleaner(RecordOperationType.class.getSimpleName(), CachingRecordEditLevelProvider.CACHE_NAME);
        return cached;
    }

    @Bean
    public RecordHoldingUpserter recordHoldingUpserter() {
        var bean = new AppserverRecordHoldingUpserter(
                appserverXmlMapper,
                mappingAppserver,
                recordHoldingLoader,
                recordOperationTypeLoader(),
                recordOperationSaver()
        );
        var authorityHoldingDepartmentReplacing = new AuthorityHoldingDepartmentReplacingRecordHoldingUpserter(bean, realRootDepartmentProvider);
        return new ExternalRecordSavingRecordHoldingUpserter(recordSaver(), authorityHoldingDepartmentReplacing);
    }

    @Bean
    public RecordSaver recordSaver() {
        AppserverRecordSaver pure = new AppserverRecordSaver(
                mappingAppserver,
                new StringBuilderDetailToMarcXmlConverter(
                        new InstantToStringConverter(databaseColumnsTimeZoneProvider, MarcConstants.MARCXML_INSTANT_FORMATTER)
                ),
                authorityLoader(),
                List.of(recordCache, recordEditLevelProvider()),
                recordEntityLoader()
        );
        return new SecuredRecordSaver(pure, securityManager);
    }

    @Bean
    public Codebook<RecordOperationType, Integer> recordOperationTypeLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(RecordOperationTypeEntity.class)
                .convertedEachBy(new EntityToRecordOperationTypeConverter())
                .staticCached(RecordOperationType.class.getSimpleName())
                .build();
    }

    @Bean
    public Saver<RecordOperation, RecordOperation> recordOperationSaver() {
        return saverBuilderFactory.<RecordOperation, UUID>saver()
                .intermediateConverting(new RecordOperationToEntityConverter())
                .build();
    }

}
