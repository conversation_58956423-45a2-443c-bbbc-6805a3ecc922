== Record bulk edit resource

=== Bulk upsert record fields

For a bulk editation of one or more fields in one or more records

[source,url,options="nowrap"]
----
/api/records/fields/bulkedit/complex
----

==== Example 200

If we want to upsert (create or replace) field `856` with value `https://neco.cz` in subfield `u` and value `plný text` in subfield `y`.
If subfield `856.u` with value `https://neco.cz` is already present in record, it will be reused and updated with `plný text` to subfield `y`.

[source,http,options="nowrap"]
----
POST /api/records/fields/bulkedit/complex
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
----

[source,json,options="nowrap"]
----
{
    "reusing": [{"id": "dkid:23543"}],
    "value": [
        {
            "reusing": [{
                "type": "d856",
                "havingField": {"type": "d856.u", "value": "https://neco.cz"}
            }],
            "set": {
                "type": "d856",
                "value": [
                    {
                        "reusing": [{"type": "d856.u"}],
                        "set": {"type": "d856.u", "value": "https://neco.cz"}
                    },
                    {
                        "reusing": [{"type": "d856.y"}],
                        "set": {"type": "d856.y", "value": "plný text"}
                    }
                ]
            }
        }
    ]
}
----

where

- `reusing` is object, which defines record(s) or field(s), which will be edited ("deduplicated"). If no record/field is found, new record/field will be created.
- `reusing.type` type id of field, which we want to edit
- `reusing.havingField` if we want to specify, that field we want to edit must have subfield of given type and value
