import type {ActionResponse, UUID} from 'typings/portaro.be.types';
import type {SvelteComponentConstructor} from 'core/types';

export interface CancelAction {
    type: 'cancel';
}

export interface AcknowledgeAction {
    type: 'acknowledge';
}

export interface ReturnAction {
    type: 'return';
    value: ActionResponse;
}

export interface ResolveAction {
    type: 'resolve';
    value: Promise<ActionResponse>;
}

export type ModalWindowAction = CancelAction | AcknowledgeAction | ReturnAction | ResolveAction;

export interface ModalWindowSettings {
    size?: 'md' | 'lg';
}

export interface ModalWindowActions {
    cancel: () => void;
    acknowledge: () => void;
    return: (actionResponse: ActionResponse) => void;
    resolve: (promise: Promise<ActionResponse>) => void;
    /** @deprecated use {@link resolve} */
    submitPromise: (promise: Promise<ActionResponse>) => void;
}

export interface ModalWindowResolveValues<MODEL> {
    id: UUID;
    modalWindowActions: ModalWindowActions;
    model: MODEL;
    component: SvelteComponentConstructor;
    settings: ModalWindowSettings;
}

export const MODAL_WINDOW_TYPES = [
    'searchSelection',
    'recordEditation',
    'catalogueSelection',
    'loanSelection',
    'universalForm',
    'registrationOptions',
    'confirmationDialog',
    'loginForm',
    'passwordQuestion',
    'userPreferences',
    'settings',
    'field7Editor',
    'field8Editor',
    'authorityField8Editor',
    'specialSymbolsKeyboard',
    'diagramNodeDetails',
    'filesSelection',
    'userFilesUpload',
    'discountRequest',
    'authPairingResponse',
    'departmentCreated',
    'exception',
    'finishedRenewalCompositeResponse',
    'goToResponse',
    'loanReadyNoticeToSendQuestion',
    'loanRequestForm',
    'richTextFinishedResponse',
    'finishedSaveResponse',
    'finishedResponse',
    'finishedRenewalResponse',
    'paymentForm',
    'paymentGatewayRedirectResponse',
    'printPage',
    'redirectionResponse',
    'userActivationRequired',
    'submittingForm'
] as const;

export type ModalWindowType = typeof MODAL_WINDOW_TYPES[number];

export type ModalWindowTypeToComponentMap = Record<ModalWindowType, SvelteComponentConstructor>;

