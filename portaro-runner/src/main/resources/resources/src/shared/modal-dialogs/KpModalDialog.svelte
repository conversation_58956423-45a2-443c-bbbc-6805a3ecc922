<script lang="ts">
    import './modal-dialog-transitions.less';
    import {assertExists, exists} from 'shared/utils/custom-utils';
    import type {Px} from 'shared/ui-widgets/types';
    import {setStyleProperty} from './use.set-style-property';
    import type {UUID} from 'typings/portaro.be.types';

    export let id: UUID;
    export let size: 'md' | 'lg' = 'md';
    export let open: boolean;

    let dialogRef: HTMLDialogElement;
    let bodyScrollbarWidth: Px;

    $: {
        if (exists(dialogRef)) {
            handleOpenState(open)
        }
    }

    function handleOpenState(desiredOpenState: boolean) {
        assertExists(dialogRef);
        const shouldOpen = desiredOpenState && !dialogRef.open;
        if (shouldOpen) {
            openDialog();
            return;
        }

        const shouldClose = !desiredOpenState && dialogRef.open
        if (shouldClose) {
            closeDialog();
            return;
        }
    }

    function openDialog() {
        assertExists(dialogRef);
        bodyScrollbarWidth = getBodyScrollbarWidth()
        dialogRef.showModal();
    }

    function closeDialog() {
        assertExists(dialogRef);
        dialogRef.close();
    }

    function onClose() {
        open = false
    }

    function onCancel() {
        closeDialog();
    }

    function preventDismissal(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            event.preventDefault();
        }
    }

    function getBodyScrollbarWidth(): Px {
        return `${window.innerWidth - document.documentElement.clientWidth}px`;
    }

</script>

<svelte:body use:setStyleProperty={{property: '--last-scrollbar-width', value: bodyScrollbarWidth}}/>

<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-noninteractive-element-interactions listen for keydown to prevent 'Esc' dismissal -->
<dialog {id}
        class="kp-modal-dialog modal-dialog-transitions size-{size}"
        bind:this={dialogRef}
        on:keydown={preventDismissal}
        on:cancel={onCancel}
        on:close={onClose}>
    <slot/>
</dialog>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    dialog.kp-modal-dialog {
        --modal-width-lg: 900px;
        --modal-width-md: 600px;

        width: auto;
        border: 1px solid rgba(0,0,0,.2);
        border-radius: @border-radius-large;

        @media (min-width: @screen-sm-min) {
            width: var(--modal-width-md);
        }

        @media (min-width: @screen-md-min) {
            &.size-lg {
                width: var(--modal-width-lg);
            }
        }
    }
</style>