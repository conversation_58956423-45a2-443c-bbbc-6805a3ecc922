import type {ActionReturn} from 'svelte/action';
import type {AnyObject} from 'typings/portaro.fe.types';
import {exists} from 'shared/utils/custom-utils';

interface Parameters {
    disabled: boolean;
}

const defaultParams: Parameters = {
    disabled: false
}

// trap focus in element (modal window), inspired by uibModal implementation
export function enforceFocusWithin(node: HTMLElement, params: Parameters = defaultParams): ActionReturn<Parameters, AnyObject> {
    let disabled = params.disabled;
    let disabledInitially = params.disabled;

    const documentKeydownListener = (event: KeyboardEvent) => {
        if (event.key === 'Tab' && !disabled) {
            const elems = loadFocusElementList(node);
            let focusChanged = false;

            if (event.shiftKey) {
                if (isFocusInFirstItem(event, elems) || isModalFocused(event, node)) {
                    focusChanged = focusLastFocusableElement(elems);
                }
            } else {
                if (isFocusInLastItem(event, elems)) {
                    focusChanged = focusFirstFocusableElement(elems);
                }
            }

            if (focusChanged) {
                event.preventDefault();
                event.stopPropagation();
            }
        }
    }

    document.addEventListener('keydown', documentKeydownListener);

    if (!disabled) {
        node.tabIndex = -1;
        node.focus(); // focus modal window on open
    }

    return {
        update: (parameters) => {
            disabled = parameters.disabled;

            if (disabledInitially && !disabled) {
                node.tabIndex = -1;
                node.focus(); // focus modal window on enable
                disabledInitially = false;
            }
        },
        destroy: () => document.removeEventListener('keydown', documentKeydownListener)
    }
}

const tabbableSelector = 'a[href], area[href], input:not([disabled]):not([tabindex=\'-1\']), ' +
    'button:not([disabled]):not([tabindex=\'-1\']),select:not([disabled]):not([tabindex=\'-1\']), textarea:not([disabled]):not([tabindex=\'-1\']), ' +
    'iframe, object, embed, *[tabindex]:not([tabindex=\'-1\']), *[contenteditable=true]';

function isVisible(element: HTMLElement) {
    return exists(element.offsetWidth ||
        element.offsetHeight ||
        element.getClientRects().length);
}

function loadFocusElementList(node: HTMLElement): HTMLElement[] {
    return [...node.querySelectorAll<HTMLElement>(tabbableSelector)].filter(isVisible);
}

function focusFirstFocusableElement(list: HTMLElement[]) {
    if (list.length > 0) {
        list[0].focus();
        return true;
    }

    return false;
}

function focusLastFocusableElement(list: HTMLElement[]) {
    if (list.length > 0) {
        list[list.length - 1].focus();
        return true;
    }

    return false;
}

function isModalFocused(evt: KeyboardEvent, modalWindowElement: HTMLElement) {
    return evt.target === modalWindowElement;
}

function isFocusInFirstItem(evt: KeyboardEvent, list: HTMLElement[]) {
    if (list.length > 0) {
        return evt.target === list[0];
    }

    return false;
}

function isFocusInLastItem(evt: KeyboardEvent, list: HTMLElement[]) {
    if (list.length > 0) {
        return evt.target === list[list.length - 1];
    }

    return false;
}