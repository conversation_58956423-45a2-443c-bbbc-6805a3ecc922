<script lang="ts">
    import './new-button-styles.less';
    import type {ComponentSize, ButtonStyle, ButtonType} from '../types';
    import type {ActionArray} from 'shared/utils/svelte-actions-utils';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {focusOnMount} from 'shared/value-editors/internal/shared/use.focus-on-mount';
    import {exists} from 'shared/utils/custom-utils';
    import {useActions} from 'shared/utils/svelte-actions-utils';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';

    export let buttonStyle: ButtonStyle = 'default';
    export let buttonSize: ComponentSize = 'md';
    export let buttonType: ButtonType = 'button';
    export let isBlock = false;
    export let isDisabled = false;
    export let isLoading = false;
    export let additionalClasses = '';
    export let id: string | null = null;
    export let title: string | null = null;
    export let noWrap = false;
    export let focused = false;
    export let rounded = false;
    export let tooltipLabel: string | null = null;
    export let dataQa: string | null = null;
    export let use: ActionArray = [];

    $: buttonClasses = `btn btn-${buttonStyle} btn-${buttonSize} ${isBlock ? 'btn-block' : ''} ${rounded ? 'btn-rounded' : ''}`;
</script>

<span aria-disabled={isDisabled || isLoading} class="kp-button-wrapper" use:tooltip={{enabled: exists(tooltipLabel), content: tooltipLabel ?? '', role: 'tooltip'}}>
    <button type={buttonType}
            {id}
            {title}
            {...$$restProps}
            class="kp-button {buttonClasses} {additionalClasses}"
            class:no-text-wrap={noWrap}
            class:is-loading={isLoading}
            disabled={isDisabled || isLoading}
            data-qa="{dataQa}"
            use:focusOnMount={focused}
            use:useActions={use}
            on:click
            on:focus
            on:blur
            on:contextmenu
            on:dblclick
            on:focusin
            on:focusout
            on:keydown
            on:keyup
            on:mouseenter
            on:mouseleave
            on:mousemove
            on:mouseout
            on:mouseover
            on:mouseup
            on:toggle>
        <slot/>

        {#if isLoading}
            <span class="loading-wrapper">
                <KpLoadingInline size="xs"/>
            </span>
        {/if}
    </button>
</span>

<style lang="less">
    .kp-button {
        white-space: normal;

        &.is-loading {
            position: relative;

            .loading-wrapper {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }

        &.no-text-wrap {
            white-space: nowrap;
        }
    }

    .btn-link {
        color: darkblue;
    }
</style>