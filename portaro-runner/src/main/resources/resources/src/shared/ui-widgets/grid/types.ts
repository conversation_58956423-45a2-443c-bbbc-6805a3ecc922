import type {Row, RowData} from '@tanstack/svelte-table';
import type {ColumnType} from 'src/features/record-grid/lib/constants';
import type {SupportedEditors} from 'src/features/record-grid/lib/grid-value-editors';
import type {Datatype, FieldTypeId} from 'typings/portaro.be.types';
import type {CustomJsonSerializer, Equatable} from 'typings/portaro.fe.types';
import type {FieldTypeDefinitionAttribute} from 'src/features/record/field-types/types';
import type {SEPARATOR_CHAR} from 'shared/ui-widgets/grid/constants';
import {serializeCellCoordinates} from 'shared/ui-widgets/grid/utils';
import {isNullOrUndefined} from 'shared/utils/custom-utils';
import '@tanstack/svelte-table';

// define datatype of metadata on column object
declare module '@tanstack/svelte-table' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface ColumnMeta<TData extends RowData, TValue> {
        text?: string;
        columnType?: ColumnType;
        editorType?: SupportedEditors;
        datatype?: Datatype;
        enabled?: FieldTypeDefinitionAttribute<boolean>;
        editable?: FieldTypeDefinitionAttribute<boolean>;
        required?: FieldTypeDefinitionAttribute<boolean>;
        staticDependentFieldTypeIds?: FieldTypeId[];
    }
}

export interface GridSettings<ROW> {
    condensed: boolean;
    colorAccented: boolean;
    stickyHeader: boolean;
    columnResizingEnabled: boolean;
    columnPinningEnabled: boolean;
    mainGroupsDivided: boolean;
    hoverRows: boolean;
    hoverColumns: boolean;
    selectionEnabled: boolean;
    editable: boolean;
    showOnlyTopAndBottomHeaders: boolean,
    rowClassTemplate: (row: Row<ROW>) => string
}

export type KeyboardEventModifiers = Record<'shiftKey' | 'ctrlKey', boolean>;
export type MouseEventModifiers = Record<'shiftKey' | 'ctrlKey' | 'dragging', boolean>;

export interface BaseCellCoordinates {
    rowIndex: number;
    columnIndex: number;
}

export type SerializedCellCoordinates = `${BaseCellCoordinates['rowIndex']}${typeof SEPARATOR_CHAR}${BaseCellCoordinates['columnIndex']}`;

export class CellCoordinates implements Readonly<BaseCellCoordinates>, CustomJsonSerializer, Equatable {
    readonly #rowIndex: number;
    readonly #columnIndex: number;

    constructor({rowIndex, columnIndex}: BaseCellCoordinates) {
        this.#rowIndex = rowIndex;
        this.#columnIndex = columnIndex;
    }

    public get rowIndex() {
        return this.#rowIndex;
    }

    public get columnIndex() {
        return this.#columnIndex;
    }

    public copy(): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex,
            columnIndex: this.columnIndex
        });
    }

    public serialize(): SerializedCellCoordinates {
        return serializeCellCoordinates(this);
    }

    public up(): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex - 1,
            columnIndex: this.columnIndex
        });
    }

    public down(): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex + 1,
            columnIndex: this.columnIndex
        });
    }

    public right(): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex,
            columnIndex: this.columnIndex + 1
        });
    }

    public left(): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex,
            columnIndex: this.columnIndex - 1
        });
    }

    public withRowIndex(rowIndex: number): CellCoordinates {
        return new CellCoordinates({
            rowIndex,
            columnIndex: this.columnIndex
        });
    }

    public withColumnIndex(columnIndex: number): CellCoordinates {
        return new CellCoordinates({
            rowIndex: this.rowIndex,
            columnIndex
        });
    }

    public underRow(rowIndex: number): boolean {
        return this.rowIndex > rowIndex;
    }

    public aboveRow(rowIndex: number): boolean {
        return this.rowIndex < rowIndex;
    }

    public leftFromColumn(columnIndex: number): boolean {
        return this.columnIndex < columnIndex;
    }

    public rightFromColumn(columnIndex: number): boolean {
        return this.columnIndex > columnIndex;
    }

    public isEqualTo(other: CellCoordinates): boolean {
        if (isNullOrUndefined(other)) {
            return false;
        }
        return this.rowIndex === other.rowIndex && this.columnIndex === other.columnIndex;
    }

    public equals(other: any): boolean {
        if (other instanceof CellCoordinates) {
            return this.isEqualTo(other);
        }
        return false;
    }

    public toJSON(): BaseCellCoordinates {
        return {
            columnIndex: this.columnIndex,
            rowIndex: this.rowIndex
        };
    }
}

export interface SelectedState {
    allSelected: Set<SerializedCellCoordinates>;
    selectedTop: Set<SerializedCellCoordinates>;
    selectedBottom: Set<SerializedCellCoordinates>;
    selectedLeft: Set<SerializedCellCoordinates>;
    selectedRight: Set<SerializedCellCoordinates>;
}

export interface CursorState {
    physicalCursor: CellCoordinates | null;
    virtualCursor: CellCoordinates | null;
}

export interface HoverState {
    hoverCursor: CellCoordinates | null;
    hoverRow: Set<SerializedCellCoordinates>;
    hoverColumn: Set<SerializedCellCoordinates>;
}

export interface EditState {
    editedCell: CellCoordinates | null;
}

export interface DependedState {
    depended: Set<SerializedCellCoordinates>;
}

export interface GridState {
    selected: SelectedState;
    cursor: CursorState;
    hover: HoverState;
    edit: EditState;
    depended: DependedState;
}

export type DataChangingCommands = 'copy' | 'paste' | 'cut' | 'delete'; // feature specific implementation is required
export type GenericGridCommands = 'rollback-cell-change'; // generic grid commands
export interface GridCommand {
    type: DataChangingCommands | GenericGridCommands;
    target: CellCoordinates;
}
