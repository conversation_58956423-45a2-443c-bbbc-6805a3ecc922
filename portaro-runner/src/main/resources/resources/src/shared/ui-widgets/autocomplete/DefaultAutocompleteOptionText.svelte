<script lang="ts">
    import {containsQueryText, parseHighlightedQueryText} from './utils';

    export let optionText: string;
    export let queryText: string;

    let showHighlightedText: boolean;
    $: showHighlightedText = containsQueryText(optionText, queryText);
    let prefix: string;
    let highlightedText: string;
    let suffix: string;
    $: [prefix, highlightedText, suffix] = showHighlightedText ? parseHighlightedQueryText(optionText, queryText) : [null, null, null];
</script>

{#if (showHighlightedText)}
    <span>{prefix}<strong>{highlightedText}</strong>{suffix}</span>
{:else }
    <span>{optionText}</span>
{/if}