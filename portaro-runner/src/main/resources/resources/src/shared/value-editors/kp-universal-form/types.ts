import type {ObjectValueEditorField} from '../internal/meta-editors/object/types';
import type {LabeledIdentified} from 'typings/portaro.be.types';


/**
 * @ngdoc type
 * @name KpUniversalFormComponentOptions
 * @module portaro.value-editors.universal-form
 *
 * @property {boolean} autofocusFirstField {@link kpValueEditorConfigurationServiceProvider}
 *
 * @description
 * Options for {@link kpUniversalForm}
 *
 */

export interface KpUniversalFormComponentOptions {
    autofocusFirstField?: boolean;
}

/**
 * @ngdoc type
 * @name KpUniversalFormSettings
 * @module portaro.value-editors.universal-form
 *
 * @property {ObjectValueEditorField[]} fields Fields definition.
 * @property {string=} header Form header
 * @property {string=} footer Form footer
 *
 * @description
 *
 */
export interface KpUniversalFormSettings<MODEL_TYPE extends Record<string, any>, FIELDS extends ObjectValueEditorField<MODEL_TYPE>[] = ObjectValueEditorField<MODEL_TYPE>[]> extends LabeledIdentified<string> {
    fields: FIELDS;
    header?: string;
    footer?: string;
}