import type {ValueEditorLocalizations} from '../../../localizations/types';

/**
 * @ngdoc type
 * @name CommonValueEditorLocalizations
 * @module portaro.value-editors.localizations
 *
 * @property {string} description Input description
 *
 * @description
 * Localizations used by all elementary editors
 */
export interface CommonValueEditorLocalizations extends ValueEditorLocalizations {
    description: string;
}