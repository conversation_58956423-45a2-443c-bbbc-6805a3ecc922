import type {ValueEditorOptions, ValueEditorValidations} from '../../../kp-value-editor/types';
import type {AnyObject} from 'typings/portaro.fe.types';
import type {SvelteComponentConstructor} from 'core/types';
import type {CommonValueEditorLocalizations} from '../_shared/common-types';

/**
 * @ngdoc type
 * @name SearchOrEditValueEditorOptions
 * @module portaro.value-editors.search-or-edit
 *
 * @property {SvelteComponentConstructor<{value: MODEL}>} modelComponent Template for displaying model
 * @property {{}} searchParams Additional parameters passed to `searchModelFunction` and `editModelFunction`
 * @property {Function} searchModelFunction
 * ```
 * function({model, searchParams}: RequestFunctionParams<MODEL>) => Promise<MODEL>
 * ```
 * Returns model value in promise.
 *
 * @property {Function} editModelFunction
 * ```
 * function({model, editParams}: RequestFunctionParams<MODEL>) => Promise<MODEL>
 * ```
 *  Returns new model in promise.
 *
 * @property {boolean} immediatelyTriggerSearch Run search function right after initialization.
 * @property {boolean} allowToDeleteValue Displays delete button and sets empty model value.
 *
 * @description
 * Extends {@link type:ValueEditorOptions}
 *
 * @template MODEL
 *
 * Default value: {@link SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_OPTIONS}
 */
export interface SearchOrEditValueEditorOptions<MODEL = any> extends ValueEditorOptions {
    modelComponent?: SvelteComponentConstructor<{value: MODEL}>;
    searchParams?: AnyObject | undefined;
    createParams?: AnyObject | undefined;
    editParams?: AnyObject | undefined;
    searchModelFunction?: (params: SearchFunctionParams<MODEL>) => Promise<MODEL>;
    editModelFunction?: (params: EditFunctionParams<MODEL>) => Promise<MODEL>;
    immediatelyTriggerSearch?: boolean;
    allowToDeleteValue?: boolean;
}

export interface SearchFunctionParams<MODEL> {
    model: MODEL,
    searchParams?: any,
    createParams?: any
}

export interface EditFunctionParams<MODEL> {
    model: MODEL,
    editParams?: any
}

/**
 * @ngdoc type
 * @name SearchOrEditValueEditorLocalizations
 * @module portaro.value-editors.search-or-edit
 *
 * @property {string} search
 * @property {string} searchOther
 * @property {string} editValue
 * @property {string} createNew
 * @property {string} delete
 *
 * @description
 * Default localizations: {@link SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS}
 */
export interface SearchOrEditValueEditorLocalizations extends CommonValueEditorLocalizations {
    search;
    searchOther;
    editValue;
    createNew;
    delete;
}

export interface SearchOrEditValueEditorTypeMap {
    'search-or-edit': {options: SearchOrEditValueEditorOptions, validations: ValueEditorValidations, localizations: SearchOrEditValueEditorLocalizations};
}