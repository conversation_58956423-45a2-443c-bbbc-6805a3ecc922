<script lang="ts">
    import type {FormControlOptions} from '../../forms/types';
    import type {AutocompleteValueEditorLocalizations, AutocompleteValueEditorOptions} from './types';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import type {TextValueEditorValidations} from '../text/types';
    import type {FormControl} from '../../forms/form-control';
    import type {ValueEditorSize} from '../../../types';
    import {formControls} from '../../forms/use.form-controls';
    import {style} from 'svelte-forms';
    import {getContext, onDestroy} from 'svelte';
    import {FormControlBuilder} from '../../forms/form-control';
    import {createInputDescription} from '../../description/utils';
    import {ariaAutocompleteButton} from './use.aria-autocomplete-button';
    import {autocomplete} from 'shared/ui-widgets/autocomplete/use.autocomplete';
    import {focusOnMount} from '../../shared/use.focus-on-mount';
    import {touched} from '../../shared/use.touched';
    import {ariaInvalid} from '../../errors/use.aria-invalid';
    import {ariaErrormessage} from '../../errors/use.aria-errormessage';
    import {ariaDescribedby} from '../../description/use.aria-describedby';
    import {validatorFactory} from '../text/utils';
    import {emptyAsNullFormatter, emptyAsNullParser, trim} from '../_shared/editors-utils';
    import {required} from '../../forms/use.required';
    import {createAutocompleteFormControlOptions} from './utils';
    import {getLogger} from 'core/svelte-context/context';
    import {EDITORS_LOCALIZE} from '../../../context-keys';
    import ErrorMessages from '../../errors/ErrorMessages.svelte';
    import Description from '../../description/Description.svelte';
    import AutocompleteWrapper from '../../../../ui-widgets/autocomplete/AutocompleteWrapper.svelte';
    import KpLoadingBlock from '../../../../components/kp-loading/KpLoadingBlock.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let model: string;
    export let formControlOptions: FormControlOptions;

    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;

    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: AutocompleteValueEditorOptions;
    export let validations: TextValueEditorValidations;

    const logger = getLogger();
    const localize: EditorsLocalizationFunction<AutocompleteValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);

    let autocompleteOptions: string[] = [];
    let fetchingAutocompleteOptionsPromise: Promise<any>;
    let isFetchingAutocompleteOptions = false;

    onDestroy(() => {
        subscription.unsubscribe();
    });

    const formControl = FormControlBuilder.for(editorId, editorName, model)
        .withValidators([...validatorFactory(validations, 'text')])
        .withOptions(createAutocompleteFormControlOptions(formControlOptions))
        .withModifiers([trim(options), emptyAsNullParser(options)])
        .withFormatters([emptyAsNullFormatter(options)])
        .build(logger);

    const subscription = formControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: formControl.setModelValue(model); // two-way data binding -> receiving new model value from parent
    const viewValue$ = formControl.getViewValue$();

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    export function getFormController(): FormControl<string> {
        return formControl;
    }

    async function fetchItemsIfNeed(): Promise<void> {
        if (autocompleteOptions === null || (Array.isArray(autocompleteOptions) && autocompleteOptions.length === 0)) {
            fetchingAutocompleteOptionsPromise = fetchItems();
            autocompleteOptions = await fetchingAutocompleteOptionsPromise;
        }
    }

    async function fetchItems(): Promise<string[]> {
        isFetchingAutocompleteOptions = true;

        let fetchedAutocompleteOptions: string[];
        try {
            fetchedAutocompleteOptions = await options.dataSource({
                model,
                staticParams: options.staticParams
            });
            logger.debug('AutocompleteValueEditor: Loaded autocomplete options: ', fetchedAutocompleteOptions);
        } catch (e) {
            logger.error('AutocompleteValueEditor: Loading autocomplete options failed, setting []: ', e);
            fetchedAutocompleteOptions = [];
        } finally {
            isFetchingAutocompleteOptions = false;
        }

        if (fetchedAutocompleteOptions.some((item) => typeof item !== 'string')) {
            throw new TypeError('Loaded autocomplete options are not string values.');
        }

        return fetchedAutocompleteOptions;
    }

    let inputElement: HTMLInputElement;
    let minLength = options.minLength;
    let showAll = false;

    async function search(inputValue: string): Promise<string[]> {
        await fetchingAutocompleteOptionsPromise;
        let results;
        if (inputValue.length < minLength) {
            results = [];
        } else {
            results = autocompleteOptions.filter((autocompleteOption) => autocompleteOption.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1);
        }
        if (showAll) {
            showAll = false;
            minLength = options.minLength;
        }
        return results;
    }

    function open() {
        showAll = true;
        minLength = 0;
        inputElement.focus(); // this also set touched on form control
    }
</script>

<AutocompleteWrapper {search} currentInputValue={$viewValue$} {size} let:autocompleteController>
    <input bind:this={inputElement}
           id={editorId}
           class="form-control input-{size}"
           name={editorName}
           type="text"
           {placeholder}
           disabled={isDisabled}
           maxlength={validations.maxlength}
           minlength={validations.minlength}
           use:required={validations.required}

           on:focus={fetchItemsIfNeed}
           use:focusOnMount={isFocused}

           use:formControls={formControl}
           use:style={{field: formControl.getFieldStateAsStore()}}
           use:touched={{formControl}}
           use:ariaInvalid={formControl}
           use:ariaErrormessage={formControl}
           use:ariaDescribedby={inputDescription}

           use:autocomplete={autocompleteController}
           data-main-input/>

    <button class="unset-style size-{size} {isFetchingAutocompleteOptions ? 'loading-icon' : 'expand-arrow'}"
            id="expand-btn-{editorId ?? editorName}"
            type="button"
            disabled={isDisabled}
            on:click={open}
            title={localize('showOptions')}
            aria-label={localize('showOptions')}
            use:ariaAutocompleteButton={autocompleteController}>

        {#if (isFetchingAutocompleteOptions)}
            <KpLoadingBlock size="xs"/>
        {:else}
            <UIcon icon="angle-small-down"/>
        {/if}
    </button>
</AutocompleteWrapper>

<ErrorMessages formController={formControl} {forceShowErrors} {size}/>

<Description {inputDescription}/>

<style lang="less">
    @import "bootstrap-less/bootstrap/mixins/tab-focus";

    button.unset-style {
        position: absolute;
        cursor: pointer;
        right: 1px;
        top: 1px;

        &:focus {
            .tab-focus()
        }

        &.expand-arrow {
            padding: 8px 8px 4px;

            &.size-sm {
                padding: 6px 6px 2px;
            }

            &.size-xs {
                padding: 2px 2px 0;
            }
        }

        &.loading-icon {
            padding: 6px 7px 6px;

            &.size-sm {
                padding: 4px 5px 4px;
            }

            &.size-xs {
                padding: 0 1px 0;
            }
        }
    }
</style>