import type {ValueEditorOptions, ValueEditorValidations} from '../../../kp-value-editor/types';
import type {CommonValueEditorLocalizations} from '../_shared/common-types';


/**
 * @ngdoc type
 * @name TTextValueEditorType
 * @module portaro.value-editors.text
 *
 * @description
 * This type defines type of text editor.
 *
 * - `text`: Classic HTML input element.
 * - `textarea`: Classic HTML textarea element.
 * - `rich-textarea`: ACE editor with some syntax highlight.
 *
 * Possible values are: `'text' | 'textarea' | 'rich-textarea' | 'email'`.
 *
 */

export type TTextValueEditorType = 'text' | 'textarea' | 'rich-textarea' | 'email' | 'url' | 'tel';

/**
 * @ngdoc type
 * @name TextValueEditorOptions
 * @module portaro.value-editors.text
 *
 * @property {TTextValueEditorType} type Input type. Possible values are `text`, `textarea`, `rich-textarea`, `email`, `url`, `tel`.
 * @property {object} aceOptions Options for ACE editor. Applicable only if `type` is `'rich-textarea'`.
 * @property {string} prefix Non-editable prefix before input element.
 * @property {string} suffix Non-editable prefix after input element.
 * @property {boolean} includePrefixAndSuffixToModel If `true`, prefix and suffix will be appended to the model.
 * @property {boolean} trim If true, model will be trimmed.
 * @property {boolean} switchToTextareaThreshold If type is `text` and length of input text is greater or equal than this threshold, input will force to `textarea` type. If `0`, input never switch to `textarea` type.
 * @description
 * Extends {@link type:ValueEditorOptions}
 *
 * Defaults: {@link TEXT_VALUE_EDITOR_DEFAULT_OPTIONS}
 */
export interface TextValueEditorOptions extends ValueEditorOptions {
    type?: TTextValueEditorType;
    aceOptions?: any;
    prefix?: string;
    suffix?: string;
    includePrefixAndSuffixToModel?: boolean;
    trim?: boolean;
    switchToTextareaThreshold?: number;
}

/**
 * @ngdoc type
 * @name TextValueEditorValidations
 * @module portaro.value-editors.text
 *
 * @property {number=} minlength Min length.
 * @property {number=} maxlength Max length.
 * @property {string=} pattern Regexp pattern.
 * @property {boolean=} notBlank Not blank (only whitespace) validation
 *
 * @description
 * Extends {@link type:ValueEditorValidations}
 */
export interface TextValueEditorValidations extends ValueEditorValidations {
    minlength?: number;
    maxlength?: number;
    pattern?: string | RegExp;
    notBlank?: boolean;

    // Only for localizations
    readonly url?: never;
    readonly email?: never;
}

/**
 * @ngdoc type
 * @name TextValueEditorLocalizations
 * @module portaro.value-editors.text
 *
 *
 * @description
 * Extends {@link type:CommonValueEditorLocalizations}
 *
 * Default localizations: {@link TEXT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS}
 */
export type TextValueEditorLocalizations = CommonValueEditorLocalizations

export interface TextValueEditorTypeMap {
    'text': {options: TextValueEditorOptions, validations: TextValueEditorValidations, localizations: TextValueEditorLocalizations}
}