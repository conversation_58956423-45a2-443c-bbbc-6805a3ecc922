import type {ObjectValueEditorLocalizations, ObjectValueEditorOptions} from './types';
import type {DefaultOptions} from '../../../types';

/**
 * @ngdoc constant
 * @name OBJECT_VALUE_EDITOR_DEFAULT_OPTIONS
 * @module portaro.value-editors.object
 *
 * @description
 * For description see {@link ObjectValueEditorOptions}
 *
 * ```javascript
 * {
 *      fields: [],
 *      attributesTransformation: undefined,
 *      labelsWidth: 2
 * }
 * ```
 */
export const OBJECT_VALUE_EDITOR_DEFAULT_OPTIONS: DefaultOptions<ObjectValueEditorOptions<any>> = {
    fields: [],
    attributesTransformation: undefined,
    labelsWidth: 2,
};

/**
 * @ngdoc constant
 * @name OBJECT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS
 * @module portaro.value-editors.object
 *
 * @description
 * ```
 * {
 *     hint: 'Hint'
 * }
 * ```
 */
export const OBJECT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS = Object.freeze<ObjectValueEditorLocalizations>({
    hint: 'Hint',
    required: 'required'
});