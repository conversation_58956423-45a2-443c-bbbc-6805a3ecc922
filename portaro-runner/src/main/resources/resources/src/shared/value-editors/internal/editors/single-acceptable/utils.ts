import type {ValueEditorValidations} from '../../../kp-value-editor/types';
import type {Validator} from 'svelte-forms';
import {isDefined, isNull, isNullOrUndefined, isUndefined} from '../../../../utils/custom-utils';
import {requiredValidator} from '../../shared/validators';
import type {Formatter, Parser} from '../../forms/types';
import type {SingleAcceptableValueEditorOptions} from './types';

export function validatorFactory(validations: ValueEditorValidations): Validator[] {
    const validators: Validator[] = [];

    if (isDefined(validations.required) && validations.required) {
        validators.push(requiredValidator());
    }

    return validators;
}

export function scalarValueToArrayParser<T = any>(): Parser<T, T[]> {
    return (value: T | null) => {
        if (value === null) {
            return null;
        }
        return [value];
    }
}

export function arrayToScalarValueFormatter<T = any>(): Formatter<T[] , T > {
    return (value: T[] | null) => {
        if (isNullOrUndefined(value)) {
            return null;
        }

        if (!Array.isArray(value) || value.length !== 1) {
            throw new Error(`Expected model value to be array with length 1, but got ${JSON.stringify(value)}`);
        }
        return value[0];
    }
}

export function getOptionIdResolver<VALUE, ID>(options: SingleAcceptableValueEditorOptions<VALUE, ID>): (_: VALUE) => ID {
    return (option) => {
        try {
            return options.optionIdResolver({option});
        } catch (e) {
            throw new Error('Error in custom optionIdResolver', {cause: e});
        }
    }
}

export function tryToFindOptionById<VALUE, ID>(options: VALUE[], idResolver: (_: VALUE) => ID, id: ID): VALUE {
    if (isNull(id)) {
        return null;
    }

    return options.find((option) => idResolver(option) === id);
}

export function getOptionById<VALUE, ID>(options: VALUE[], idResolver: (_: VALUE) => ID, id: ID): VALUE {
    const foundOption = tryToFindOptionById(options, idResolver, id);

    if (isUndefined(foundOption)) {
        // eslint-disable-next-line @typescript-eslint/no-base-to-string
        throw new Error(`Acceptable option with ID: ${id} was not found`);
    }

    return foundOption;
}

export function getOptionId<VALUE, ID>(option: VALUE,  idResolver: (_: VALUE) => ID): ID {
    if (isNull(option)) {
        return null;
    }
    return idResolver(option);
}

export function modelToIdentifierFormatter<T = any>(options: SingleAcceptableValueEditorOptions<T>): Formatter {
    return (value: T) => {
        if (isNullOrUndefined(value)) {
            return null;
        }

        return getOptionId(value, getOptionIdResolver(options));
    }
}

// acceptable values can change over time (e.g. search facets update after new search)
export function identifierToModelParser<T = any>(acceptableValuesProvider: () => T[], options: SingleAcceptableValueEditorOptions<T>): Parser {
    return (value: string) => {
        if (value === null) {
            return null;
        }
        return getOptionById(acceptableValuesProvider(), getOptionIdResolver(options), value);
    }
}