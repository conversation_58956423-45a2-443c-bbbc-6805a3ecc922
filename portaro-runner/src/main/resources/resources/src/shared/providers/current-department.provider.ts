import type {Department} from 'typings/portaro.be.types';
import type {IWindowService} from 'angular';

declare global {
    interface Window {
        currentDep: Department;
    }
}

export default class CurrentDepartmentProvider {
    public static providerName = 'currentDepartment';

    public readonly $get: (_: IWindowService) => Department;

    /*@ngInject*/
    constructor(private $windowProvider: IWindowService) {
        this.$get = /*@ngInject*/ ($window: IWindowService) => $window.currentDep;
    }

    public getCurrentDepartment(): Department {
        return this.$windowProvider.$get().currentDep;
    }
}