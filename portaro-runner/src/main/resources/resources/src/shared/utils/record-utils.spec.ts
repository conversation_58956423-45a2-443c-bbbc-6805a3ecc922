import type {Rec, ViewableField} from 'typings/portaro.be.types';
import {getFields, getSubfields} from './record-utils';
import {cloneDeep} from 'lodash-es';
import {deepFreeze} from 'shared/utils/object-utils';

describe('record-utils test', () => {

    const RECORD_MOCK = deepFreeze({
        fields: [
            {
                code: 'd1',
                fields: [
                    {code: 'a'}, {code: 'b'}, {code: 'c'}
                ]
            },
            {
                code: 'd2',
                fields: [
                    {code: 'a'}, {code: 'b'}, {code: 'c'}
                ]
            },
            {
                code: 'd3',
                fields: [
                    {code: 'a'}, {code: 'b'}, {code: 'c'}
                ]
            },
            {
                code: 'd4',
                fields: [
                    {code: 'a'}, {code: 'b'}, {code: 'c'}
                ]
            }
        ]
    }) as Rec;

    let document: Rec;

    beforeEach(() => {
        document = cloneDeep(RECORD_MOCK);
    });

    describe('getFields', () => {

        it('should return empty array', () => {
            document.fields = null;
            expect(document.fields).toBeNull();

            expect(getFields(document, 'd1')).toEqual([]);
        });

        it('should return only field d1 and d3', () => {
            const extractedFields = getFields(document, ['d1', 'd3']);

            expect(extractedFields.length).toBe(2);

            const extractedFieldsNumbers = extractedFields.map((field) => field.code);

            expect(extractedFieldsNumbers).toEqual(['d1', 'd3']);
        });

        it('should return only field d2', () => {
            const extractedFields = getFields(document, 'd2');

            expect(extractedFields.length).toBe(1);
            expect(extractedFields).toEqual([RECORD_MOCK.fields[1]]);
        });
    });

    describe('getSubfields', () => {

        it('should return empty array', () => {
            document.fields = null;

            expect(getSubfields(document, 'd1')).toEqual([]);
        });

        it('should return empty array', () => {
            document.fields = null;

            expect(getSubfields(document, 'd1', ['a', 'c'])).toEqual([]);
        });

        it('should return empty array if document has no subfields', () => {
            document.fields = document.fields.map((field) => ({...field, fields: []}));

            expect(getSubfields(document, 'd1')).toEqual([]);
        });

        it('should return empty array if document has no subfields', () => {
            document.fields = document.fields.map((field) => ({...field, fields: []}));

            expect(getSubfields(document, 'd1', ['a', 'c'])).toEqual([]);
        });

        it('should return all subfields of field d2', () => {
            const extractedFields = getSubfields(document, 'd2');

            expect(extractedFields).toEqual([{code: 'a'}, {code: 'b'}, {code: 'c'}] as ViewableField[]);
        });

        it('should return subfield b of field d1 and d3', () => {
            const extractedFields = getSubfields(document, ['d1', 'd3'], ['b']);

            expect(extractedFields).toEqual([{code: 'b'}, {code: 'b'}] as ViewableField[]);
        });
    });
});