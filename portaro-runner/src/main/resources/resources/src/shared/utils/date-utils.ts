import {DateTime} from 'luxon';
import {assertExists} from 'shared/utils/custom-utils';
import {stringifyArgument} from 'shared/utils/error-utils';
import {isString} from 'shared/utils/string-utils';
import {isNumber} from 'shared/utils/number-utils';

/**
 * @module date-utils
 * @name formatDateAsYYMMDD
 *
 * @param {Date} date - Date to be formatted.
 * @returns {string} - Date formatted as a string in YYMMDD format.
 *
 * @description
 * Formats the date as YYMMDD string.
 */
export function formatDateAsYYMMDD(date: Date): string {
    const dd = date.getDate().toString().padStart(2, '0');
    const mm = (date.getMonth() + 1).toString().padStart(2, '0');
    const yy = date.getFullYear().toString().slice(-2);
    return `${yy}${mm}${dd}`;
}

/**
 * @module date-utils
 * @name formatDateAsYYYY
 *
 * @param {Date} date - Date to be formatted.
 * @returns {string} - Year as a string in YYYY format.
 *
 * @description
 * Extracts and formats the year from the provided date as a four-digit YYYY string.
 */
export function formatDateAsYYYY(date: Date): string {
    return date.getFullYear().toString().padStart(4, '0');
}

/**
 * @module date-utils
 * @name constructJsDate
 *
 * @param {number} day - Day of the month (1-31).
 * @param {number} month - Month of the year (1-12).
 * @param {number} year - Full year (e.g., 2023).
 * @returns {Date} - JavaScript Date object.
 *
 * @description
 * Creates a JavaScript Date object for the specified day, month, and year.
 * Note that the month parameter is 1-based (January = 1).
 */
export function constructJsDate(day: number, month: number, year: number): Date {
    return new Date(
        year,
        month - 1, // JS dates are indexed from 0
        day
    );
}

type ISO = string; // datetime as ISO string
type Milliseconds = number; // datetime as number of milliseconds
type AllowedDatetimeFormat = Milliseconds | ISO | Date | DateTime;

export function datetimeEquals(value1: AllowedDatetimeFormat, value2: AllowedDatetimeFormat): boolean {
    return asDateTime(value1).equals(asDateTime(value2));
}

export function asDateTime(datetime: AllowedDatetimeFormat) {
    assertExists(datetime);

    if (isNumber(datetime)) {
        return DateTime.fromMillis(datetime);
    }

    if (isString(datetime)) {
        return DateTime.fromISO(datetime);
    }

    if (isJsDate(datetime)) {
        return DateTime.fromJSDate(datetime);
    }

    assertIsDateTime(datetime);
    return datetime;
}

export function isJsDate(value: unknown): value is Date {
    return value instanceof Date;
}

export function isDateTime(value: unknown): value is DateTime {
    return value instanceof DateTime;
}

export function assertIsDateTime(value: unknown): asserts value is DateTime {
    if (!isDateTime(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a DateTime.`)
    }
}