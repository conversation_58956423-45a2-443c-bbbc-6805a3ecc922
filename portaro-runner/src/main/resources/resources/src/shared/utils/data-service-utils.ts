import type {IHttpResponse} from 'angular';
import type {ActionResponse, Identified, Rec, SetFieldValueRequest} from 'typings/portaro.be.types';
import {DateTime} from 'luxon';
import {assertExists, assertIsDefined, assertTrue, isDefined, isFunction, isNull, isNullOrUndefined, isUndefined} from './custom-utils';
import {cloneDeep, isDate} from 'lodash-es';
import {assertIsBasicPrimitive, isActionResponse, isIdentified, isIHttpResponse, isObject, isRecord, isRecordLabeledReference, isValuable} from './types-utils';

/**
 * @module data-service-utils
 * @name defaultIdMapper
 * @kind function
 *
 * @template ID
 * @param {Identified<ID>} object Object to transform
 * @return {ID}
 *
 * @description
 * default id mapper for function @see {@link transferify}
 */
export function defaultIdMapper<ID>(object: Identified<ID>): ID {
    return object.id;
}

/**
 * @module data-service-utils
 * @name transferify
 * @kind function
 *
 * @param {*} object Object to transform
 * @param {boolean=} idfyAlsoFirstLevel If `true`, it transform first level also.
 * @param idMapper {(_: Identified<ID>) => any} mapper that maps object to its id (default: obj => obj.id)
 * @return {*}
 *
 * @description
 * Function transforms object to ID. If some object contains property `id`, function returns its value.
 * If `obj` is JS Date, function transform it to ISO 8601 format.
 */
export function transferify<ID>(object: any, idfyAlsoFirstLevel = false, idMapper: ((_: Identified<ID>) => any) = defaultIdMapper): any {
    const transferifyRecursion = (obj, idfy = true) => {
        if (isUndefined(obj) || obj === null) {
            return null;
        }

        if (idfy && isIdentified<ID>(obj)) {
            return idMapper(obj);
        }

        if (typeof obj === 'object' && obj instanceof Date) {
            return DateTime.fromJSDate(obj).toISO();
        }

        if (Array.isArray(obj)) {
            for (let i = 0; i < obj.length; i++) {
                obj[i] = transferifyRecursion(obj[i]);
            }
            return obj;
        }

        if (isObject(obj)) {
            for (const propertyName in obj) {
                if (propertyName.startsWith('$')) { // removing angular (e.g. Resource object) properties
                    delete obj[propertyName];
                } else if (Object.hasOwn(obj, propertyName)) {
                    obj[propertyName] = transferifyRecursion(obj[propertyName]);
                }
            }
            return obj;
        }

        return obj;
    };

    const copy = cloneDeep(object);
    return transferifyRecursion(copy, idfyAlsoFirstLevel);
}

export interface DataContainingResponse<T> {
    data: T;

    [x: string]: any;
}

/**
 * @module data-service-utils
 * @name responseToData
 * @kind function
 *
 * @param {Object} res Response
 *
 * @return {*}
 *
 * @description
 * Returns data from response wrapper
 */
export function responseToData<T>(res: DataContainingResponse<T> | IHttpResponse<T>): T {
    return res.data;
}

/**
 * @module data-service-utils
 * @name transferify
 * @kind function
 *
 * @return {ActionResponse}
 * @param response {ActionResponse | IHttpResponse<ActionResponse>}
 *
 * @description
 * Transforms ActionResponse or HttpResponse containing ActionResponse to ActionResponse
 */
export function getActionResponse(response: ActionResponse | IHttpResponse<ActionResponse>): ActionResponse {
    if (isActionResponse(response)) {
        return response;
    } else if (isIHttpResponse(response)) {
        return response.data;
    }

    throw new Error('Response is not IHttpResponse or ActionResponse');
}

export function getRecordUrlId(object: Rec): string {
    return object.id;
}

export function getRecordPath(recordId: string): string {
    return `/#!/records/${recordId}`;
}

export function createSetFieldValueRequest(object: any): SetFieldValueRequest | null {
    if (isNullOrUndefined(object)) {
        return null;
    }

    if (isRecordLabeledReference(object) || isRecord(object)) {
        return {recordId: object.id};
    }

    if (isValuable(object)) {
        return {value: transferify(object.value, true)};
    }

    return {value: transferify(object, true)};
}

// tagged template function to create valid relative URLs
export function urlPath(strings: TemplateStringsArray, ...values: (string | number)[]): string {
    assertTrue(strings.length > 0);
    const [head, ...tail] = strings;
    let path = tail.reduce((pathBuilder, currentString, index) => {
        const currentValue = values[index];
        assertExists(currentValue);
        return pathBuilder + encodeURIComponent(currentValue) + currentString;
    }, head);

    // Normalize multiple slashes
    path = path.replace(/\/+/g, '/');

    // Remove trailing slash
    if (path.endsWith('/')) {
        path = path.slice(0, -1);
    }

    // Ensure leading slash
    if (!path.startsWith('/')) {
        path = `/${path}`;
    }

    return path;
}

export function serializeUrlParams(params: Record<string, any>): string {
    if (isNullOrUndefined(params)) {
        return '';
    }
    const urlParams = new URLSearchParams();

    Object.entries(params)
        .filter(([, value]) => isDefined(value) && !isFunction(value))
        .forEach(([key, value]) => {
            if (Array.isArray(value)) {
                value.forEach((v) => urlParams.append(key, serializeUrlParamValue(v)));
            } else {
                urlParams.append(key, serializeUrlParamValue(value));
            }
        });

    return urlParams.toString();
}

function serializeUrlParamValue(v: any): string {
    assertIsDefined(v);

    if (isNull(v)) {
        return SERIALIZED_NULL_VALUE;
    }

    if (isObject(v)) {
        return isDate(v) ? v.toISOString() : JSON.stringify(v);
    }

    assertIsBasicPrimitive(v);
    return v.toString();
}

const SERIALIZED_NULL_VALUE = '~null~';
