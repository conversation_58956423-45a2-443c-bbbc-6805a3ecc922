import {deepFreeze, isEmptyObject, omitPropertiesBy, pickPropertiesBy} from 'shared/utils/object-utils';

describe('custom-utils tests', () => {

    describe('deepFreeze', () => {
        const sourceObj = {a: 'a', subObj: {b: 'b'}};

        let testingObj: typeof sourceObj;

        beforeEach(() => {
            testingObj = structuredClone(sourceObj);
        });

        it('should let edit subObj.b', () => {
            expect(testingObj.subObj.b).toBe('b');

            testingObj.subObj.b = 'a';
            expect(testingObj.subObj.b).toBe('a');

            Object.freeze(testingObj);
            testingObj.subObj.b = 'c';
            expect(testingObj.subObj.b).toBe('c');
        });

        it('should not let edit subObj.b', () => {
            deepFreeze(testingObj);

            expect(testingObj.subObj.b).toBe('b');

            expect(() => testingObj.a = 'b').toThrowError();

            expect(() => testingObj.subObj.b = 'a').toThrowError();
        });
    });

    describe('isEmptyObject', () => {

        it('should return true', () => {
            expect(isEmptyObject({})).toBeTrue();
            expect(isEmptyObject(new Object())).toBeTrue();
        });

        it('should return false', () => {
            expect(isEmptyObject({a: 4})).toBeFalse();
            expect(isEmptyObject({x: 'ddd'})).toBeFalse();
            expect(isEmptyObject({a: 4, b: true, c: 'string'})).toBeFalse();
            // eslint-disable-next-line no-new-wrappers
            expect(isEmptyObject(new Number())).toBeFalse();
            expect(isEmptyObject([])).toBeFalse();
            expect(isEmptyObject(() => 5)).toBeFalse();
            // eslint-disable-next-line no-new-wrappers
            expect(isEmptyObject(new String())).toBeFalse();
        });
    });
    describe('pickPropertiesBy', () => {

        it('should pick properties based on value predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = pickPropertiesBy(obj, (value) => value > 1);
            expect(result).toEqual({b: 2, c: 3});
        });

        it('should pick properties based on key predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = pickPropertiesBy(obj, (value, key) => key === 'b');
            expect(result).toEqual({b: 2});
        });

        it('should return an empty object if no properties match the predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = pickPropertiesBy(obj, () => false);
            expect(result).toEqual({});
        });

        it('should return the same object if all properties match the predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = pickPropertiesBy(obj, () => true);
            expect(result).toEqual(obj);
        });

        it('should handle empty objects', () => {
            const obj = {};
            const result = pickPropertiesBy(obj, () => true);
            expect(result).toEqual({});
        });

        it('should correctly handle complex predicates', () => {
            const obj = {a: 1, b: 'test', c: null, d: undefined};
            const result = pickPropertiesBy(obj, (value) => typeof value === 'string');
            expect(result).toEqual({b: 'test'});
        });

    });

    describe('omitPropertiesBy', () => {

        it('should remove properties based on value predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = omitPropertiesBy(obj, (value) => value > 1);
            expect(result).toEqual({a: 1});
        });

        it('should remove properties based on key predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = omitPropertiesBy(obj, (value, key) => key === 'b');
            expect(result).toEqual({a: 1, c: 3});
        });

        it('should return an empty object if all properties are removed', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = omitPropertiesBy(obj, () => true);
            expect(result).toEqual({});
        });

        it('should return the same object if no properties match the predicate', () => {
            const obj = {a: 1, b: 2, c: 3};
            const result = omitPropertiesBy(obj, () => false);
            expect(result).toEqual(obj);
        });

        it('should handle empty objects', () => {
            const obj = {};
            const result = omitPropertiesBy(obj, () => true);
            expect(result).toEqual({});
        });

        it('should correctly handle complex predicates', () => {
            const obj = {a: 1, b: 'test', c: null, d: undefined};
            const result = omitPropertiesBy(obj, (value) => value === null || value === undefined);
            expect(result).toEqual({a: 1, b: 'test'});
        });

    });
});