<script lang="ts">
    import type {Px} from 'shared/ui-widgets/types';
    import {getGridSystemRowContext} from 'shared/layouts/grid-system/grid-system-context';
    import {getLogger} from 'core/svelte-context/context';
    import {onMount} from 'svelte';

    export let column: number | null = null;
    export let aside = false;
    export let verticalGap: Px | null = null;
    export let hideWhenEmpty = false;

    let htmlElement: HTMLElement;
    let isEmpty = false;

    const logger = getLogger();
    const rowContext = getGridSystemRowContext();

    if (column > rowContext.columnsCount) {
        logger.warn(`Invalid column number specified. This row only expects ${rowContext.columnsCount} columns.`);
    }

    onMount(() => {
        isEmpty = htmlElement.children.length === 0;
    });
</script>

<svelte:element this="{aside ? 'aside' : 'div'}"
                class="kp-column"
                class:column-hidden={hideWhenEmpty && isEmpty}
                bind:this={htmlElement}
                style:--grid-column="{column}"
                style:--vertical-gap="{verticalGap}">
    <slot/>
</svelte:element>

<style lang="less">
    .kp-column {
        display: flex;
        flex-direction: column;
        grid-column: var(--grid-column);
        gap: var(--vertical-gap);
        min-width: 0; // Needed for proper sizing

        &.column-hidden {
            display: none;
        }
    }
</style>