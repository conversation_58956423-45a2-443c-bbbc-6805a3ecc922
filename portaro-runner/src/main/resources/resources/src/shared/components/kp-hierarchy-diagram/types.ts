import type {Identified} from 'typings/portaro.be.types';
import type {SvelteComponentConstructor} from 'core/types';
import type {Readable} from 'svelte/store';

export interface DiagramItem extends Identified<string | number> {
    parentId?: string | number;
    text: string;
}

export interface DiagramCustomNodeSettings {
    component: SvelteComponentConstructor;
    width: number;
    height: number;
}

export interface DiagramNodeDetailsModalSettings {
    headingGetter: (item: DiagramItem) => string;
    component: SvelteComponentConstructor;
}

export interface DiagramItemData<ITEM> {
    item: ITEM;
    orientation: Readable<DiagramOrientation>;
    width: number;
    height: number;
}

export type DiagramOrientation = 'LR' | 'TD';