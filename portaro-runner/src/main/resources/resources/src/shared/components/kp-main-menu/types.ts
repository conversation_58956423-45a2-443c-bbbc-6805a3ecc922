import type {LabeledIdentified} from 'typings/portaro.be.types';
import type {DefinedAction} from 'shared/defined-actions/types';
import type {UIcons} from 'shared/ui-widgets/uicons/types';

export const RECALCULATE_NAV_OVERFLOW_EVENT = 'recalculate-main-menu-nav-overflow';

export interface MenuItem extends LabeledIdentified<string> {
    requiresAuth: boolean;
    type: 'user' | null;
    action?: DefinedAction;
    submenu: MenuItem[] | null;
    iconName?: UIcons;
    userActionRequired: boolean;
}

export interface KpMainMenuSettings {
    backgroundColor: string;
    textColor: string;
    highlightBackgroundColor?: string;
    highlightTextColor?: string;
    items: MenuItem[];
}