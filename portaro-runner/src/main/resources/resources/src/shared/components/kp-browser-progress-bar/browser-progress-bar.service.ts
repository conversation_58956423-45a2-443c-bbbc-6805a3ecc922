import type {BrowserProgressBarSettings} from 'shared/components/kp-browser-progress-bar/types';
import type {Observable} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import {assertExists, exists} from 'shared/utils/custom-utils';
import {clamp} from 'shared/utils/number-utils';
import {DEFAULT_BROWSER_PROGRESS_BAR_SETTINGS} from 'shared/components/kp-browser-progress-bar/types';

export class BrowserProgressBarService {
    public static serviceName = 'browserProgressBarService';

    private pendingRequestsCounter = 0;

    private progress$: BehaviorSubject<number>
    private visibility$: BehaviorSubject<boolean>;
    private trickleInterval: number | null = null;

    private settings = DEFAULT_BROWSER_PROGRESS_BAR_SETTINGS;

    constructor() {
        this.progress$ = new BehaviorSubject<number>(0); // Progress as a value between 0 and 1
        this.visibility$ = new BehaviorSubject<boolean>(false);
    }

    public incrementPendingRequestsCounter(): void {
        this.pendingRequestsCounter++;
        if (this.pendingRequestsCounter === 1) {
            this.start();
        } else {
            this.set(this.progress$.getValue());
        }
    }

    public decrementPendingRequestsCounter(): void {
        if (this.pendingRequestsCounter > 0) {
            this.pendingRequestsCounter--;
        }

        if (this.pendingRequestsCounter === 0) {
            this.done();
        } else {
            this.increment();
        }
    }

    public setSettings(settings: BrowserProgressBarSettings): void {
        assertExists(settings);
        this.settings = settings;
    }

    public getSettings(): Readonly<BrowserProgressBarSettings> {
        return this.settings;
    }

    public get getProgress$(): Observable<number> {
        return this.progress$.asObservable();
    }

    public get getVisibility$(): Observable<boolean> {
        return this.visibility$.asObservable();
    }

    private start(): void {
        this.set(0);
        this.visibility$.next(true);
        this.trickle();
    }

    private set(value: number): void {
        value = clamp(value, this.settings.minimum, 1);
        this.progress$.next(value);

        if (value === 1) {
            setTimeout(() => {
                this.visibility$.next(false);
            }, this.settings.transitionsSpeedMs);
        }
    }

    private increment(amount?: number): void {
        const current = this.progress$.getValue();
        if (!current) {
            return this.start();
        }

        if (!exists(amount)) {
            amount = (1 - current) * clamp(Math.random() * current, 0.1, 0.95);
        }

        this.set(clamp(current + amount, 0, 0.994));
    }

    private done(): void {
        this.set(1);
        if (this.trickleInterval) {
            window.clearInterval(this.trickleInterval);
            this.trickleInterval = null;
        }

        window.setTimeout(() => {
            this.visibility$.next(false);
        }, this.settings.transitionsSpeedMs);
    }

    private trickle(): void {
        if (this.trickleInterval) {
            window.clearInterval(this.trickleInterval);
        }

        this.trickleInterval = window.setInterval(() => {
            this.increment(this.settings.trickleFillRate);
        }, this.settings.trickleFrequencyMs);
    }
}