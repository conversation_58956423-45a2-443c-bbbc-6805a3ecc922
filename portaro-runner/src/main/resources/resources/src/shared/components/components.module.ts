import register from '@kpsys/angularjs-register';

import filtersModule from '../filters/filters.module';
import openingCalendarComponentModule from 'shared/components/kp-opening-calendar/kp-opening-calendar.module';
import localeModule from './kp-locale/kp-locale.module';
import {SubSearchLinksDataService} from './kp-sub-search-links/sub-search-links.data-service';
import {LandingPageStatsDataService} from './kp-landing-page-stats/landing-page-stats.data-service';
import {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import {BrowserProgressBarService} from 'shared/components/kp-browser-progress-bar/browser-progress-bar.service';

/**
 * @ngdoc module
 * @name portaro.shared.components
 * @module portaro.shared.components
 */
export default register('portaro.shared.components', [
    filtersModule,
    openingCalendarComponentModule,
    localeModule
])
    .service(SubSearchLinksDataService.serviceName, SubSearchLinksDataService)
    .service(LandingPageStatsDataService.serviceName, LandingPageStatsDataService)
    .service(ToastMessageService.serviceName, ToastMessageService)
    .service(BrowserProgressBarService.serviceName, BrowserProgressBarService)
    .name();