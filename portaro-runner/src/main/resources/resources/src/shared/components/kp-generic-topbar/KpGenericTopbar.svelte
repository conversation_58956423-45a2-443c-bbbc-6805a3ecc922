<script lang="ts">
    import type {CssSize, CssVariable} from 'shared/ui-widgets/types';

    export let height: CssSize | CssVariable = '48px';
    export let gap: CssSize = '16px';
    export let horizontalPadding: CssSize = '32px';
    export let zIndex: number | null = null;
    export let animate = false;
    export let additionalClasses = '';
</script>

<div class="kp-generic-topbar {additionalClasses}"
     class:with-animation={animate}
     style:--horizontal-padding="{horizontalPadding}"
     style:--height="{height}"
     style:--gap="{gap}"
     style:--z-index="{zIndex}">

    <slot/>
</div>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .kp-generic-topbar {
        width: 100%;
        height: var(--height);
        gap: var(--gap);
        display: flex;
        flex-shrink: 0;
        align-items: center;
        padding: 0 var(--horizontal-padding);
        background-color: @themed-body-bg;
        z-index: var(--z-index, initial);
        border-bottom: 1px solid @themed-border-default;

        &.with-animation {
            animation: 0.4s ease-in-out 0s 1 topbar-slide-from-top;
        }
    }

    @keyframes topbar-slide-from-top {
        0% {
            transform: translateY(-100%);
            opacity: 0;
        }
        100% {
            transform: translateY(0);
            opacity: 100%;
        }
    }
</style>