<script lang="ts">
    import type {MenuItem} from 'shared/components/kp-main-menu/types';
    import type {UUID} from 'typings/portaro.be.types';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {slide} from 'svelte/transition';
    import {createEventDispatcher} from 'svelte';
    import {getMainMenuContext} from 'shared/components/kp-main-menu/main-menu-context';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {offset} from 'svelte-floating-ui/dom';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';

    export let itemUuid: UUID;
    export let subMenu: MenuItem[];
    export let anchorElement: HTMLElement;
    export let hoveringOver: boolean;
    export let dataQaPrefix: string;

    const context = getMainMenuContext();
    const dispatch = createEventDispatcher<{ 'item-click': void }>();

    const [floatingRef, floatingContent] = createFloatingActions({
        placement: 'bottom',
        strategy: 'fixed',
        middleware: [
            offset(-1)
        ]
    });

    const handleItemClick = (subMenuItem: MenuItem) => {
        context.service.performClick(subMenuItem);
        dispatch('item-click');
    }

    floatingRef(anchorElement);
</script>

<div class="sub-menu-list-container"
     class:hovering-over={hoveringOver}
     use:floatingContent>

    <ul id="kp-main-nav-item-submenu-{itemUuid}"
        class="sub-menu-list theme-{context.backgroundTheme}"
        use:clickOutside={{ignoredElementsSelectors: [`.menu-uuid-${itemUuid}`]}}
        on:click-outside
        in:slide={{duration: 250}}
        out:slide={{duration: 250}}>

        {#each subMenu as subMenuItem}
            <li class="sub-menu-item">
                <a class="menu-id-{subMenuItem.id} full-size-anchor"
                   href={context.service.getMenuItemAnchorHref(subMenuItem)}
                   data-qa="{dataQaPrefix}-{subMenuItem.id}"
                   on:click={() => handleItemClick(subMenuItem)}>

                    {subMenuItem.text}

                    {#if subMenuItem.userActionRequired}
                        <KpChipTag additionalClasses="warning-chip" chipSize="xs" chipStyle="danger-new">
                            <UIcon icon="exclamation" color="white"/>
                        </KpChipTag>
                    {/if}
                </a>
            </li>
        {/each}
    </ul>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .sub-menu-list-container {
        z-index: 100;

        &.hovering-over {
            z-index: 110;
        }

        .sub-menu-list {
            min-width: 200px;
            background-color: var(--main-menu-background-color);
            color: var(--main-menu-text-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: rgba(0, 0, 0, 0.1) 0 3px 9px;
            border-bottom-left-radius: @border-radius-base;
            border-bottom-right-radius: @border-radius-base;

            &.theme-light {
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-top: none;
            }

            .sub-menu-item {
                width: 100%;
                transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;

                &:hover {
                    background-color: var(--main-menu-highlight-background-color);
                    color: var(--main-menu-highlight-text-color);
                }

                .full-size-anchor {
                    color: inherit;
                    text-decoration: none;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: @padding-large-vertical @padding-large-horizontal;
                    gap: @padding-xs-horizontal;

                    &:focus-visible {
                        outline-offset: -6px !important;
                        box-shadow: none !important;
                    }
                }
            }
        }
    }
</style>