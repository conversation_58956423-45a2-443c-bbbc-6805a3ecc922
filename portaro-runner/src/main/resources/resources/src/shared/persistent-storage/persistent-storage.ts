import type {Writable} from 'svelte/store';
import type {BehaviorSubject} from 'rxjs';
import {exists} from 'shared/utils/custom-utils';

export type PersistentStorageUnsubscriber = () => void;

export function persistentStorageLocal<T>(store: Writable<T> | BehaviorSubject<T>, key: string): PersistentStorageUnsubscriber {
    return persistentStorage<T>(store, window.localStorage, key);
}

export function persistentStorageSession<T>(store: Writable<T> | BehaviorSubject<T>, key: string): PersistentStorageUnsubscriber {
    return persistentStorage<T>(store, window.sessionStorage, key);
}

// Implementation
export function persistentStorage<T>(store: Writable<T> | BehaviorSubject<T>, storage: Storage, key: string): PersistentStorageUnsubscriber {
    const storedValue = storage.getItem(key);

    if (exists(storedValue)) {
        try {
            const parsedValue: T = JSON.parse(storedValue);
            if (isSvelteStore(store)) {
                store.set(parsedValue);
            } else if (isRxjsSubject(store)) {
                store.next(parsedValue);
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error parsing stored value for key "${key}":`, error);
        }
    }

    const unsubscriberOrSubscription = store.subscribe((value) => {
        try {
            storage.setItem(key, JSON.stringify(value));
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error saving to storage for key "${key}":`, error);
        }
    });

    if (typeof unsubscriberOrSubscription !== 'function') {
        return () => {
            unsubscriberOrSubscription.unsubscribe();
        }
    }

    return unsubscriberOrSubscription;
}

// Helper functions
function isSvelteStore<T>(store: any): store is Writable<T> {
    return store && typeof store.subscribe === 'function' && typeof store.set === 'function';
}

function isRxjsSubject<T>(subject: any): subject is BehaviorSubject<T> {
    return subject && typeof subject.subscribe === 'function' && typeof subject.next === 'function';
}