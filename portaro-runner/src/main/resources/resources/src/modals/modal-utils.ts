import type {PayloadActionResponse} from 'typings/portaro.fe.types';
import type {ActionResponse, ActionResponseWithText, ExceptionActionResponse} from 'typings/portaro.be.types';
import {isActionResponse, isExceptionActionResponse, isObject} from 'shared/utils/types-utils';
import {ExceptionSeverity, responseTypes} from 'shared/constants/portaro.constants';

export function createPayloadActionResponse<T>(payload: T): PayloadActionResponse<T> {
    return {
        finished: true,
        responseType: responseTypes.PAYLOAD,
        payload
    };
}

export function createFinishedResponseActionResponse(text: string): ActionResponseWithText {
    return {
        finished: true,
        responseType: responseTypes.FINISHED_RESPONSE,
        text
    };
}

export function createAcknowledgeActionResponse(): ActionResponse {
    return {
        finished: true,
        responseType: responseTypes.ACKNOWLEDGE,
    };
}

export function createInterruptActionResponse(): ActionResponse {
    return {
        finished: true,
        responseType: responseTypes.INTERRUPT,
    };
}

export function createExceptionActionResponse(name: string, text: string, actionText: string = null): ExceptionActionResponse {
    return {
        text,
        message: text,
        simpleName: name,
        actionText,
        severity: ExceptionSeverity.SEVERITY_ERROR,
        responseType: responseTypes.EXCEPTION,
        finished: true,

    };
}

export function isInterruptedActionResponse(actionResponse: unknown): boolean {
    return isObject(actionResponse)
        && isActionResponse(actionResponse)
        && actionResponse.finished
        && actionResponse.responseType === responseTypes.INTERRUPT
}

export function isExceptionResponseOf(actionResponse: unknown, simpleName: string): boolean {
    return isObject(actionResponse)
        && isExceptionActionResponse(actionResponse)
        && actionResponse.finished
        && actionResponse.responseType === responseTypes.EXCEPTION
        && actionResponse.simpleName === simpleName;
}

export async function catchManualInterruption<E>(fn: () => Promise<E>): Promise<E> {
    try {
        return await fn();
    } catch (exceptionResponse) {
        if (!isInterruptedActionResponse(exceptionResponse)) {
            throw exceptionResponse;
        }
        return null;
    }
}