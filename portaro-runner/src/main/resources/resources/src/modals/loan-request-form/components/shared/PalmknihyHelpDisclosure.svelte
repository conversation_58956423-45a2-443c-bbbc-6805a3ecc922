<script lang="ts">
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';
    import {getLocalization, getSanitize} from 'core/svelte-context/context';
    import HelpDisclosure from 'src/modals/loan-request-form/components/shared/HelpDisclosure.svelte';

    export let requesterEmail: string;

    const localize = getLocalization();
    const sanitize = getSanitize();
</script>

<HelpDisclosure>
    <svelte:fragment slot="summary">
        {localize(/* @kp-localization loan.palmknihy.JakPujcovat */ 'loan.palmknihy.JakPujcovat')}
    </svelte:fragment>
    <svelte:fragment slot="details">
        <ol class="loan-request-list">
            <li>
                {localize(/* @kp-localization loan.palmknihy.KlikneteNaVypujcit */ 'loan.palmknihy.KlikneteNaVypujcit')}
            </li>
            <li>
                {@html sanitize(
                    pipe(
                        localize(/* @kp-localization loan.palmknihy.ZaregistrujteNaXSeSEmailemY */ 'loan.palmknihy.ZaregistrujteNaXSeSEmailemY'),
                        strParams(`<a href=\"https://www.palmknihy.cz\" target=\"_blank\">${localize(/* @kp-localization loan.palmknihy.Nazev */ 'loan.palmknihy.Nazev')}</a>`, `<strong>${requesterEmail}</strong>`)
                    )
                )}
            </li>

            <li>
                {@html sanitize(
                    pipe(
                        localize(/* @kp-localization loan.palmknihy.VKonteXNajdeteVypujcku */ 'loan.palmknihy.VKonteXNajdeteVypujcku'),
                        strParams(`<a href=\"https://www.palmknihy.cz/muj-ucet/vypujcky\" target=\"_blank\">${localize(/* @kp-localization loan.palmknihy.Nazev */ 'loan.palmknihy.Nazev')}</a>`)
                    )
                )}
            </li>
        </ol>
        <a href="{localize(/* @kp-localization loan.palmknihy.moreInfoUrl */ 'loan.palmknihy.moreInfoUrl')}" target="_blank">
            {localize(/* @kp-localization commons.ViceInformaci */ 'commons.ViceInformaci')}
        </a>
    </svelte:fragment>
</HelpDisclosure>