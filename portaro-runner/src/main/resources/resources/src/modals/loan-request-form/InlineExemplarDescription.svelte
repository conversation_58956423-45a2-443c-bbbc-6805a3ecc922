<script lang="ts">
    import type {Exemplar} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';

    export let exemplar: Exemplar;

    const localize = getLocalization();
</script>

<span>
    {localize(/* @kp-localization commons.Oznaceni */ 'commons.Oznaceni')}: {exemplar.designation}
</span>

{#if exemplar.issue}
    {#if exists(exemplar.issueName)}
        <span>, {localize(/* @kp-localization exemplar.issueName */ 'exemplar.issueName')}: {exemplar.issueName}</span>
    {/if}
{/if}

{#if exemplar.bundledVolume}
    {#if exists(exemplar.bundledVolumeNumber)}
        <span>, {localize(/* @kp-localization exemplar.bundledVolumeNumber */ 'exemplar.bundledVolumeNumber')}: {exemplar.bundledVolumeNumber}</span>
    {/if}

    {#if exists(exemplar.bundledVolumeYear)}
        <span>, {localize(/* @kp-localization exemplar.bundledVolumeYear */ 'exemplar.bundledVolumeYear')}: {exemplar.bundledVolumeYear}</span>
    {/if}

    {#if exists(exemplar.bundledVolumeIssueRange)}
        <span>, {localize(/* @kp-localization exemplar.bundledVolumeIssueRange */ 'exemplar.bundledVolumeIssueRange')}: {exemplar.bundledVolumeIssueRange}</span>
    {/if}
{/if}

{#if exemplar.binding}
    {#if exists(exemplar.bindingIssueRange)}
        <span>, {localize(/* @kp-localization exemplar.bindingIssueRange */ 'exemplar.bindingIssueRange')}: {exemplar.bindingIssueRange}</span>
    {/if}
{/if}