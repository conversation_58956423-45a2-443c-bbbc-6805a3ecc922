<script lang="ts">
    import type {LoanRequestOption, SlotVisitOrderability} from 'typings/portaro.be.types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {LoanRequestFormService} from '../loan-request-form.service';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import type {Field} from 'node_modules/svelte-forms/types';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import type {DateValueEditorValidations} from 'shared/value-editors/internal/editors/date/types';
    import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
    import {onMount} from 'svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import LoginFirstButton from './shared/LoginFirstButton.svelte';
    import SendLoanRequestButton from './shared/SendLoanRequestButton.svelte';
    import BuildingsList from './shared/BuildingsList.svelte';
    import DesiredBuilding from './shared/DesiredBuilding.svelte';
    import LoanOptionPanelBody from './shared/LoanOptionPanelBody.svelte';
    import FullWidthBox from './shared/FullWidthBox.svelte';
    import type {Observable} from 'rxjs';

    export let option: LoanRequestOption<SlotVisitOrderability>;

    const localize = getLocalization();
    const service = getInjector().getByClass(LoanRequestFormService);

    $: showLoginButtonFirst = service.requiresLogin(option);

    const slotScopeDateRangeValueEditorValidations: DateValueEditorValidations = {
        minDate: new Date(new Date().setHours(0, 0, 0, 0)).toISOString()
    };

    const slotScopeDateRange = {
        fromDate: new Date(new Date().setHours(0, 0, 0, 0)).toISOString()
    };
    const formForceSettings: ForceSetting[] = [
        {type: 'single-acceptable', options: {switchToInlineModeThreshold: 0, optionIdResolver: ({option: selectableOption}) => selectableOption.text}},
        {type: 'multiple-acceptable', options: {switchToInlineModeThreshold: 0, optionIdResolver: ({option: selectableOption}) => selectableOption.text}}
    ];

    let submitted = false;
    let universalForm: {getFormController(): FormGroup<any>};
    let formState$: Observable<Field<any>>;
    let initialFormModel: LoanRequestOption<SlotVisitOrderability>['formObject'];

    onMount(async () => {
        initialFormModel = structuredClone(option.formObject);
        option = service.setDesiredBuildingDefault(option);
        option = await service.fetchSlotOrderingForm(option, slotScopeDateRange);
    })

    $: {
        if (exists(universalForm)) {
            formState$ = universalForm.getFormController().getFieldState$();
        }
    }

    async function updateSlotOrderForm() {
        option = await service.fetchSlotOrderingForm(option, slotScopeDateRange);
        universalForm = null;
        option.formObject = structuredClone(initialFormModel);
    }

    function isInvalidRequest(request: typeof option.formObject): boolean {
        return isNullOrUndefined(request) || request.desiredBuilding === null;
    }
</script>

<KpGenericPanel>
    <svelte:fragment slot="heading">
        <strong>{localize(/* @kp-localization loan.Objednavka */ 'loan.Objednavka')}</strong>
        <KpChipTag chipSize="sm" chipStyle="warning">{localize(/* @kp-localization loan.Prezencni */ 'loan.Prezencni')}</KpChipTag>
    </svelte:fragment>

    <LoanOptionPanelBody>
        <div>
            {localize(/* @kp-localization loan.slot.SelectDateAndTime */ 'loan.slot.SelectDateAndTime')}
        </div>

        {#if showLoginButtonFirst}
            <BuildingsList {option}/>
            <LoginFirstButton/>
        {:else}
            <DesiredBuilding capability={option.capability} building={option.formObject.desiredBuilding}>
                {localize(/* @kp-localization exemplar.department */ 'exemplar.department')}
            </DesiredBuilding>

            <FullWidthBox>
                <KpValueEditor type="date"
                               validations="{slotScopeDateRangeValueEditorValidations}"
                               bind:model="{slotScopeDateRange.fromDate}"
                               on:model-change={updateSlotOrderForm} />
            </FullWidthBox>

            {#key option}
                {#if exists(option.form)}
                    <FullWidthBox>
                        <KpValueEditorForceSettings forceSettings="{formForceSettings}">
                            <KpUniversalForm bind:this={universalForm}
                                             formSettings="{option.form}"
                                             model="{option.formObject}"
                                             forceShowErrors="{submitted}"
                                             on:submit={() => submitted = true}/>
                        </KpValueEditorForceSettings>
                    </FullWidthBox>
                {/if}
            {/key}

            <SendLoanRequestButton {option}
                                   apiPath="/loan-items/request/slotOrderRequest"
                                   isDisabled="{isInvalidRequest(option.formObject) || $formState$?.invalid}">
                {localize(/* @kp-localization loan.Objednat */ 'loan.Objednat')}
            </SendLoanRequestButton>
        {/if}
    </LoanOptionPanelBody>
</KpGenericPanel>