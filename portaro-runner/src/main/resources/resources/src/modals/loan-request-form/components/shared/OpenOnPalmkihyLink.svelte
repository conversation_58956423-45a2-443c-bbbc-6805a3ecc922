<script lang="ts">
    import type {PalmknihyLoanability, PalmknihyAudioLoanability, PalmknihyPdfLoanability} from 'typings/portaro.be.types';
    import {getLocalization} from 'core/svelte-context/context';
    import OpenOnExternalLoanProviderWebsiteLink from './OpenOnExternalLoanProviderWebsiteLink.svelte';

    export let capability: PalmknihyLoanability | PalmknihyAudioLoanability | PalmknihyPdfLoanability;

    const localize = getLocalization();
</script>

<OpenOnExternalLoanProviderWebsiteLink {capability}>
    {localize(/* @kp-localization loan.palmknihy.ShowOnPalmknihy */ 'loan.palmknihy.ShowOnPalmknihy')}
</OpenOnExternalLoanProviderWebsiteLink>