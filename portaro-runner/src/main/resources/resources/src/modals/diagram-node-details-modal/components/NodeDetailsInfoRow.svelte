<script lang="ts">
    export let heading: string;
</script>

<div class="node-details-info-row">
    <span class="heading">{heading}</span>

    <div class="separator"></div>

    <span class="value">
        <slot/>
    </span>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .node-details-info-row {
        display: flex;
        align-items: center;
        gap: @spacing-ml;

        .heading {
            color: @themed-text-muted;
        }

        .separator {
            flex: 1;
            height: 100%;
            align-self: end;
            margin-bottom: @spacing-xs;
            border-bottom: 1px dashed @themed-border-muted;
        }

        .value {
            font-weight: 500;
        }
    }
</style>