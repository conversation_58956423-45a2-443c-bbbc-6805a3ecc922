import type {FinishedSaveResponse, User} from 'typings/portaro.be.types';
import type UserDataService from 'src/features/user/user.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';

export class UserFilesUploadModalPresenter {
    public static presenterName = 'userFilesUploadModalPresenter'

    /*@ngInject*/
    constructor(private userDataService: UserDataService, private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async uploadFiles(user: User, files: File[]): Promise<FinishedSaveResponse<string[]>> {
        try {
            const response = await this.userDataService.uploadUserFiles({
                user,
                files,
            })
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }
}