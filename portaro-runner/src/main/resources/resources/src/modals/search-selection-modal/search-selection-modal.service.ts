import type {SearchManagerBuilderFactoryService} from '../../features/search/search-manager/search-manager-factory.service';
import type {SearchService} from '../../features/search/search.service';
import type {SearchParams, StaticSearchParams} from 'typings/portaro.be.types';
import type {SearchSelectionModalModelResultPostprocessFunction, SelectableItem} from './types';
import {firstValueFrom} from 'rxjs';

export class SearchSelectionModalService {
    public static serviceName = 'searchSelectionModalService';


    /*@ngInject*/
    constructor(private searchManagerBuilderFactoryService: SearchManagerBuilderFactoryService,
                private searchService: SearchService) {
    }

    public createSearchManager(resultPostProcessFunction: SearchSelectionModalModelResultPostprocessFunction, staticParams?: Partial<StaticSearchParams>) {
        const searchFunction = async (params: SearchParams) => {
            const viewableSearch = await this.searchService.search<SelectableItem>(params);
            const originalResultContent = viewableSearch.result.content;
            const processedResultContent = await firstValueFrom(resultPostProcessFunction(originalResultContent));
            return {...viewableSearch, result: {...viewableSearch.result, content: processedResultContent}};
        }

        return this.searchManagerBuilderFactoryService
                            .createBuilder<SearchParams, SelectableItem>()
                            .withSearchFunction(searchFunction)
                            .withStaticParams(staticParams)
                            .createLocalSearch();

    }
}