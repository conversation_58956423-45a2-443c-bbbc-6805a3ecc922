<script lang="ts">
    import {firstValueFrom, of} from 'rxjs';
    import type {Exemplar, PagedParameteredResult, Rec, SearchParams, ViewableSearch} from 'typings/portaro.be.types';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import KpSelectableRecordCard
        from 'shared/value-editors/internal/editors/search-or-edit/templates/KpSelectableRecordCard.svelte';
    import KpSelectableExemplarCard
        from '../../features/record/kp-selectable-exemplar-card/KpSelectableExemplarCard.svelte';
    import {cleanup, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import KpAvailableIntentsContainer
        from '../../features/search/kp-available-intents/KpAvailableIntentsContainer.svelte';
    import type {SearchSelectionModalModel, SelectableItem} from './types';
    import type {ObjectValueEditorField} from 'shared/value-editors/internal/meta-editors/object/types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import {Kind, Subkind} from 'shared/constants/portaro.constants';
    import {createPayloadActionResponse} from '../modal-utils';
    import {someSpecifiedParamsAreEmpty, specifiedParamsHaveChanged} from './search-params-utils';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import type {FormControlOptions} from 'shared/value-editors/internal/forms/types';
    import {defaultUpdateTriggers} from 'shared/value-editors/internal/forms/form-control';
    import {SearchSelectionModalService} from './search-selection-modal.service';
    import {onDestroy, onMount, tick} from 'svelte';
    import SelectableItemsContainer from './SelectableItemsContainer.svelte';
    import {focusOnMount} from 'shared/value-editors/internal/shared/use.focus-on-mount';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpPageableSearchResults from '../../features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpSearchSorting from '../../features/search/kp-search-sorting/KpSearchSorting.svelte';

    export let model: SearchSelectionModalModel;
    export let modalWindowActions: ModalWindowActions;

    const service = getInjector().getByClass(SearchSelectionModalService);
    const localize = getLocalization();
    const formOptionsForDebounce: FormControlOptions = {updateTriggers: [defaultUpdateTriggers.BLUR, {...defaultUpdateTriggers.INPUT, debounceValue: 500}]};
    const customComponent = model.customComponent;
    const resultPostProcessFunction = model.resultPostProcessFunction ?? ((_) => of(_));

    let submitRequested = false;
    let selectedItem = model?.selectedItem ?? null;

    let lastSearch: ViewableSearch<SearchParams, SelectableItem>;
    let lastUsedFrontendParams: Partial<SearchParams> = {};
    let currentFrontendParams: Partial<SearchParams> = {};

    let formSettings: KpUniversalFormSettings<SearchParams>;
    let editableParamsNames: string[];
    let requiredEditableParamNames: string[];
    let formsHaveRequiredParams: boolean;

    const searchManager = service.createSearchManager(resultPostProcessFunction, model.staticSearchParams);
    const currentFrontendParamsSubscription = searchManager.getState$().subscribe((searchManagerState) => {
        currentFrontendParams = searchManagerState.frontendParams;
    });
    const lastSearchSubscription = searchManager.getLastSearch$().subscribe((lastSearchData) => {
        lastSearch = lastSearchData;
        handleResult(lastSearch.result);
    });

    onMount(async () => {
        await loadSearchForm();
    });

    onDestroy(() => {
        cleanup(currentFrontendParamsSubscription, lastSearchSubscription);
    });

    $: itemIsSelected = exists(selectedItem);
    $: canCreateNew = exists(model.createNewValueFn) && exists(lastSearch);
    $: isResultEmpty = exists(lastSearch) && lastSearch.result.totalElements === 0;
    $: implicitlySubmittable = isNullOrUndefined(model.staticSearchParams.kind) || !model.staticSearchParams.kind.includes(Kind.KIND_RECORD);
    $: showQuantityInput = itemIsSelected && isExemplar(selectedItem) && selectedItem.loanCategory.chunkable;
    $: isSubmittable = itemIsSelected && (!isExemplar(selectedItem) || (!selectedItem.loanCategory.chunkable || selectedItem.quantity > 0));

    async function loadSearchForm() {
        if (exists(model.initialFrontendParams)) {
            searchManager.newSearchWithParams(model.initialFrontendParams);
            await firstValueFrom(searchManager.getLastSearch$());
            copyLastSearchParams(); // create copy of real params for later comparison
        } else {
            await searchManager.loadForms();
        }

        const fields = searchManager.getForms().flatMap((form) => form.fields);

        editableParamsNames = fields.map((field) => field.fieldName);

        requiredEditableParamNames = fields
            .filter((field) => field.editor.validations?.required)
            .map((field) => field.fieldName);

        formsHaveRequiredParams = requiredEditableParamNames.length > 0;

        formSettings = {
            id: 'search-selection-modal-form',
            text: '',
            fields: searchManager.getForms().reduce<ObjectValueEditorField<any>[]>((accumulator, currentForm) => {
                return accumulator.concat(currentForm.fields);
            }, [])
        };
    }

    function isRecord(item: SelectableItem): item is Rec {
        return exists(model.staticSearchParams.kind) && model.staticSearchParams.kind.includes(Kind.KIND_RECORD) && exists(item) && 'type' in item && (item.type === Subkind.SUBKIND_AUTHORITY || item.type === Subkind.SUBKIND_DOCUMENT);
    }

    function isExemplar(item: SelectableItem): item is Exemplar {
        return exists(model.staticSearchParams.kind) && model.staticSearchParams.kind.includes(Kind.KIND_EXEMPLAR) && exists(item) && 'quantity' in item && exists(item.quantity);
    }

    async function select(item: SelectableItem) {
        selectedItem = item;
        await tick(); // wait for update of reactive values denpended on 'selectedItem`
        submitSelectedItem();
    }

    function tryToSubmit() {
        if (itemIsSelected && implicitlySubmittable) {
            submitSelectedItem();
        } else {
            submitRequested = true;
            requestSearch();
        }
    }

    async function requestSearch() {
        if (paramsHaveNotChanged()) {
            return;
        }

        copyLastSearchParams(); // create copy of real params for later comparison
        selectedItem = null;
        await tick(); // wait for update of reactive values denpended on 'selectedItem`

        // do not search if current search params are invalid
        if (currentParamsAreInvalid()) {
            resetSearch();
            return;
        }

        searchManager.newSearchWithParams(currentFrontendParams);
    }

    async function handleResult(searchResult: PagedParameteredResult<SelectableItem, SearchParams>) {
        const isSingleResult = searchResult.totalElements === 1 && searchResult.content.length === 1;

        if (isSingleResult) {
            selectedItem = searchResult.content[0];
            await tick(); // wait for update of reactive values denpended on 'selectedItem`
        }

        if (submitRequested && isSingleResult && implicitlySubmittable) {
            submitSelectedItem();
            return;
        }

        // reset search if incoming results are no longer needed, because current search params are invalid
        if (currentParamsAreInvalid()) {
            resetSearch();
            resetSearchParams();
            return;
        }

        copyLastSearchParams(); // create copy of real params for later comparison
    }

    async function createNew() {
        const editedRecord = await model.createNewValueFn();
        await select(editedRecord);
    }

    function submitSelectedItem() {
        if (isSubmittable) {
            modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(selectedItem)));
        }
    }

    function currentParamsAreInvalid() {
        return formsHaveRequiredParams && someSpecifiedParamsAreEmpty(lastUsedFrontendParams, requiredEditableParamNames);
    }

    function paramsHaveNotChanged(): boolean {
        return !specifiedParamsHaveChanged(lastUsedFrontendParams, currentFrontendParams, editableParamsNames);
    }

    function copyLastSearchParams() {
        lastUsedFrontendParams = {...currentFrontendParams};
    }

    function resetSearchParams() {
        searchManager.setFrontendParams({...lastUsedFrontendParams});
    }

    function resetSearch() {
        searchManager.reset();
        submitRequested = false;
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="search-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            {#if exists(model.title)}
                {model.title}
            {:else}
                {localize(/* @kp-localization hledani.vybratZVyhledanych */ 'hledani.vybratZVyhledanych')}
            {/if}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(formSettings)}
            <KpUniversalForm formId="{formSettings.id}"
                             {formSettings}
                             bind:model={currentFrontendParams}
                             formControlOptions="{formOptionsForDebounce}"
                             on:model-change={requestSearch}
                             on:submit={tryToSubmit}/>
        {/if}

        <KpPageableSearchResults pagination="{searchManager.getPagination()}"
                                 showPageButtons={false}
                                 let:paginationData>

            <KpSearchSorting {searchManager}/>

            <SelectableItemsContainer items="{paginationData.items}"
                                      let:item
                                      on:select={(event) => select(event.detail)}>
                {#if isRecord(item)}
                    <KpSelectableRecordCard value="{item}" isSelected="{item === selectedItem}"/>
                {/if}

                {#if isExemplar(item)}
                    <KpSelectableExemplarCard exemplar="{item}" isSelected="{item === selectedItem}"/>
                {/if}

                {#if !isRecord(item) && !isExemplar(item) && exists(customComponent)}
                    <svelte:component this="{customComponent}" value="{item}" isSelected="{item === selectedItem}"/>
                {/if}
            </SelectableItemsContainer>
        </KpPageableSearchResults>

        <KpAvailableIntentsContainer {searchManager}/>

        {#if canCreateNew}
            <div class="create-new-btn-container">
                <KpButton buttonStyle="{isResultEmpty ? 'primary' : 'default'}"
                          dataQa="search-selection-modal-create-new-button"
                          on:click={createNew}>
                    {localize(/* @kp-localization record.editation.CreateNew */ 'record.editation.CreateNew')}
                </KpButton>
            </div>
        {/if}

        {#if isExemplar(selectedItem) && showQuantityInput}
            <label>
                <span>{localize(/* @kp-localization commons.PocetKusu */ 'commons.PocetKusu')}</span>
                <input class="form-control"
                       type="number"
                       required
                       use:focusOnMount={true}
                       bind:value={selectedItem.quantity}/>
            </label>
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary"
                  isDisabled="{!isSubmittable}"
                  dataQa="search-selection-select"
                  on:click={submitSelectedItem}>

            {localize(/* @kp-localization commons.Select */ 'commons.Select')}
        </KpButton>

        <KpModalFooterCloseButton dataQa="search-selection-close"/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    .create-new-btn-container {
        display: flex;
        justify-content: center;
    }
</style>