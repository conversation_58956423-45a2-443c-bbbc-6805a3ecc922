<script lang="ts">
    import type {AuthorityDetailReactivePageData, AuthorityDetailStaticPageData} from '../types';
    import {SearchType} from 'shared/constants/portaro.constants';
    import {getPageContext} from 'shared/layouts/page-context';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';

    const pageContext = getPageContext<AuthorityDetailStaticPageData, AuthorityDetailReactivePageData>();
    const searchContextParams = {
        type: SearchType.TYPE_BY_AUTHORITY,
        recordRelatedRecord: pageContext.staticData.model.record.id
    };
</script>

<KpSearchPageContainerWithContextActions staticParams="{searchContextParams}" pageClass="kp-authority-detail-search-results"/>