<script lang="ts">
    import type {TableRow} from './types';
    import {getLocalization, getSanitize} from 'core/svelte-context/context';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';

    export let rows: TableRow[];

    const localize = getLocalization();
    const sanitizeHtml = getSanitize();
</script>

<KpClassicTable additionalClasses="detail-table"
                stripedRows
                hoverRows
                colorAccented
                columnHeadersCentered
                horizontallyDivided>
    <tr slot="header">
        <th class="header-field-name">{localize(/* @kp-localization detail.nazevPole */ 'detail.nazevPole')}</th>
        <th class="header-field-content">{localize(/* @kp-localization detail.obsahPole */ 'detail.obsahPole')}</th>
    </tr>

    <svelte:fragment slot="body">
        {#each rows as row}
            <tr>
                <td>{@html sanitizeHtml(row.columns[0].content)}</td>
                <td class={row.columns[1]?.classesInString}>{@html sanitizeHtml(row.columns[1].content)}</td>
            </tr>
        {/each}
    </svelte:fragment>
</KpClassicTable>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    th {
        padding: @table-cell-padding @padding-small-horizontal !important;
    }

    td {
        padding: @table-cell-padding @padding-small-horizontal !important;
    }

    :global(.detail-table) {
        table-layout: fixed;
        width: 100%;
        border-radius: @border-radius-base;
        overflow: hidden; // To show border radius

        .header-field-content {
            width: 60%;
        }
    }
</style>