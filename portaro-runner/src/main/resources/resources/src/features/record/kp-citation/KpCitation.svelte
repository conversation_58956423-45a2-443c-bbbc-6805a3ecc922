<script lang="ts">
    import type {LabeledIdentified, Rec} from 'typings/portaro.be.types'
    import type {Citation, CitationCitacePro} from './types';
    import {isCitationCitacePro} from './types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {CitationPresenter} from './citation.presenter';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let record: Rec;
    export let availableStyles: LabeledIdentified<string>[] = [];

    const localize = getLocalization();
    const injector = getInjector();
    const presenter = injector.getByToken<CitationPresenter>(CitationPresenter.presenterName);

    let selectedStyle: LabeledIdentified<string>;
    let citationPromise: Promise<Citation | CitationCitacePro> = presenter.getCitation(record);

    function load() {
        citationPromise = presenter.getCitation(record, selectedStyle);
    }
</script>

<div class="kp-citation-container">
    {#if availableStyles.length > 1}
        <div class="citation-style-choose">
            <select bind:value={selectedStyle} on:change={load}>
                {#each availableStyles as availableStyle (availableStyle.id)}
                    <option value={availableStyle}>
                        {availableStyle.text}
                    </option>
                {/each}
            </select>
        </div>
    {/if}

    {#await citationPromise}
        <KpLoadingBlock size="sm"/>
    {:then citation}
        <blockquote>
            {@html citation.value}
        </blockquote>

        {#if isCitationCitacePro(citation)}
            <!-- CITACE PRO button -->
            <form action={citation.redirectUrlPath} method="post" target="_blank">
                {#each Object.entries(citation.redirectUrlParameters) as [key, value]}
                    <input type="hidden" name={key} value={value[0]}>
                {/each}
                <button type="submit" class="btn btn-default btn-xs">
                    <img src="https://www.citacepro.com/logoCitacePROkatalog.png" alt="Citace PRO">
                </button>

                <span class="info-message">
                    {localize(/* @kp-localization detail.CitaceComInfoMessage */ 'detail.CitaceComInfoMessage')}
                </span>
            </form>
        {/if}
    {:catch}
        <div>Chyba při načítání citace</div>
    {/await}
</div>

<style lang="less">
    .citation-style-choose {
        margin-bottom: 20px;
    }

    blockquote {
        font-size: 1.2em;
        word-break: break-word;
    }

    .info-message {
        margin-left: 30px;
    }
</style>