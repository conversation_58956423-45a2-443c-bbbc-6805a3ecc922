import type {AllowIdentifiedStubs, AnyObject, TypeOrIdentifiedStub} from 'typings/portaro.fe.types';
import type {RecordCopyOptions, RecordCopyRequest} from './types';
import type {AuthorityDetailView} from './kp-authority-detail-page/types';
import type {DocumentDetailView} from './kp-document-detail-page/types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {transferify} from 'shared/utils/data-service-utils';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {RecordMergeRequest} from './record-merge.request';
import type {ActionResponse, Department, Document, FinishedActionResponse, FinishedSaveResponse, HierarchyTree, Rec, RecordDescriptor, RecordHierarchyTreeNode, RecordHolding, RecordRevision, UUID, Valuable} from 'typings/portaro.be.types';
import type {RecordEditationPhaseId} from 'src/features/record-grid/lib/types';

/**
 * @ngdoc service
 * @name RecordDataService
 * @module uc.record
 *
 * @description
 * Service for `Record` handling
 *
 * @requires service:$http
 */
export default class RecordDataService {
    public static readonly serviceName = 'recordDataService';
    public static readonly RECORD_ROUTE = 'records';
    public static readonly RECORD_HIERARCHY_ROOT_ROUTE = 'records/root';
    public static readonly RECORD_HOLDINGS_ROUTE = 'record-holdings';
    public static readonly RECORD_HOLDINGS_DELETE_ROUTE = 'record-holdings/delete';
    public static readonly RECORD_HOLDINGS_DISCARD_ROUTE = 'record-holdings/discard';
    public static readonly RECORD_MERGE_ROUTE = 'records/merge';
    public static readonly RECORD_COPY_ROUTE = 'records/copy';
    public static readonly RECORD_DELETE_SUBTREE_HOLDINGS_ROUTE = 'records/delete-subtree-holdings';
    public static readonly RELATED_RECORDS_ROUTE = 'related-records-count';
    public static readonly PARTS_ROUTE = 'parts';
    public static readonly ARTICLES_ROUTE = 'articles';
    public static readonly RECORD_REVISIONSS_ROUTE = 'record-revisions';
    public static readonly HIERARCHIES_ROUTE = 'hierarchies';
    public static readonly TEMPLATED_MAIL_ROUTE = 'templated-mail';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getById(recordId: UUID): Promise<Rec> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}`)
            .get();
    }

    public async createRecordHolding(record?: Rec, department?: Department): Promise<FinishedSaveResponse<RecordHolding>> {
        return this.ajaxService
            .createRequest(RecordDataService.RECORD_HOLDINGS_ROUTE)
            .post(transferify({department, record}));
    }

    public async mergeRecords(target: Rec, sources: Rec[], mergeDetail?: boolean): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_MERGE_ROUTE}`)
            .post(transferify(new RecordMergeRequest(target, sources, mergeDetail)));
    }

    public async copyRecord(source: TypeOrIdentifiedStub<Rec>, copyOptions: AllowIdentifiedStubs<RecordCopyOptions> = {
        targetFond: null,
        copyMetadata: null,
        copyFiles: null
    }): Promise<FinishedSaveResponse<Rec>> {
        const request: AllowIdentifiedStubs<RecordCopyRequest> = {source, ...copyOptions};
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_COPY_ROUTE}`)
            .post(transferify(request));
    }

    public async deleteSubtreeHoldings(record: TypeOrIdentifiedStub<Rec>): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_DELETE_SUBTREE_HOLDINGS_ROUTE}`)
            .post(transferify({record}));
    }

    public async changePhase(record: TypeOrIdentifiedStub<Rec>, phase: RecordEditationPhaseId): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/phase`)
            .post({
                record: record.id,
                phase,
            });
    }

    public async queryRecordRevisions(record: Rec): Promise<RecordRevision[]> {
        return this.ajaxService
            .createRequest(RecordDataService.RECORD_REVISIONSS_ROUTE)
            .get({record: record.id});
    }

    public async queryRecordHoldings(params: AnyObject): Promise<RecordHolding[]> {
        return this.ajaxService
            .createRequest(RecordDataService.RECORD_HOLDINGS_ROUTE)
            .get(transferify(params, false));
    }

    public async deleteRecordHolding(recordHolding: RecordHolding): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(RecordDataService.RECORD_HOLDINGS_DELETE_ROUTE)
            .post(transferify({recordHolding}));
    }

    public async discardRecordHolding(recordHolding: RecordHolding): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_HOLDINGS_DISCARD_ROUTE}`)
            .post(transferify({recordHolding}));
    }

    public async getRecordParts(recordId: UUID, pageNumber: number, pageSize: number): Promise<Document[]> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/${RecordDataService.PARTS_ROUTE}`)
            .get({pageNumber, pageSize});
    }

    public async getRecordArticles(recordId: UUID, pageNumber: number, pageSize: number): Promise<Document[]> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/${RecordDataService.ARTICLES_ROUTE}`)
            .get({pageNumber, pageSize});
    }

    @ngAsync()
    public async getRelatedRecordsCount(recordId: UUID): Promise<Valuable<number>> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/${RecordDataService.RELATED_RECORDS_ROUTE}`)
            .get();
    }

    public async getRecordHierarchyTrees(recordId: UUID): Promise<HierarchyTree<UUID, RecordHierarchyTreeNode>[]> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/${RecordDataService.HIERARCHIES_ROUTE}`)
            .get();
    }

    public async getHierarchyRoot(): Promise<RecordDescriptor> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_HIERARCHY_ROOT_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async query(filterParam: AnyObject): Promise<RecordDescriptor[]> {
        return this.ajaxService
            .createRequest(RecordDataService.RECORD_ROUTE)
            .get(filterParam);
    }

    @ngAsync()
    public async sendTemplatedMail(recordId: UUID): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/${RecordDataService.TEMPLATED_MAIL_ROUTE}`)
            .post(transferify({record: recordId}));
    }

    @ngAsync()
    public async getRecordDetailView(recordId: UUID): Promise<AuthorityDetailView | DocumentDetailView> {
        return this.ajaxService
            .createRequest(`${RecordDataService.RECORD_ROUTE}/${recordId}/view`)
            .get();
    }
}