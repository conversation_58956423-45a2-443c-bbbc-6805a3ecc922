<script lang="ts">
    import type {Rec} from 'typings/portaro.be.types';
    import {onDestroy} from 'svelte';
    import {KpRecordClipboardButtonPresenter} from './kp-record-clipboard-button.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let record: Rec;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpRecordClipboardButtonPresenter>(KpRecordClipboardButtonPresenter.presenterName);

    let showAddButton = false;
    let showRemoveButton = false;

    const recordsSubscription = presenter.getRecords$().subscribe(() => {
        showAddButton = presenter.canAddToClipboard(record);
        showRemoveButton = presenter.canRemoveToClipboard(record);
    });

    onDestroy(() => {
        recordsSubscription.unsubscribe();
    });

    function add() {
        presenter.addRecord(record);
    }

    function remove() {
        presenter.removeRecord(record);
    }
</script>

{#if showAddButton}
    <button type="button"
            class="btn btn-default btn-xs btn-block add-to-clipboard-button"
            on:click={() => add()}>

        <slot name="add">
            <UIcon icon="check-circle"/> {localize(/* @kp-localization commons.Mark */ 'commons.Mark')}
        </slot>
    </button>
{/if}

{#if showRemoveButton}
    <button type="button"
            class="btn btn-default btn-xs btn-block active remove-from-clipboard-button"
            on:click={() => remove()}>

        <slot name="remove">
            <UIcon icon="check-circle"/> {localize(/* @kp-localization commons.Marked */ 'commons.Marked')}
        </slot>
    </button>
{/if}

<style lang="less">
    .remove-from-clipboard-button {
        visibility: visible; /* tlacitko odebrat je viditelne porad (ne jen kdyz se najede na nejaky jeho kontejner) */
    }
</style>