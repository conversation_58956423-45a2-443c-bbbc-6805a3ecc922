import type {Department, Exemplar, Rec, SimpleFond, ViewableExemplar} from 'typings/portaro.be.types';

export type ExemplarsType = 'bindings'
    | 'exemplars'
    | 'issues';

export interface ExemplarsPair {
    building: Department;
    exemplars: ViewableExemplar[];
}

export type ExemplarFilterFunction = (exemplar: Exemplar) => boolean;

export interface RecordCopyRequest {
    source: Rec;
    targetFond: SimpleFond;
    copyMetadata: boolean;
    copyFiles: boolean;
    publishCopiedRecord?: boolean;
}

export type RecordCopyOptions = Omit<RecordCopyRequest, 'source'>;