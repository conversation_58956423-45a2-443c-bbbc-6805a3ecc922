<script lang="ts">
    import type {Px} from 'shared/ui-widgets/types';
    import {createEventDispatcher} from 'svelte';
    import {onMount, onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';

    export let autoSlide = true;
    export let autoSlideInterval = 5000;

    export let gap: Px = '0px';
    export let fill = true;
    export let transitionDuration = 300;
    export let threshold = 30;
    export let currentIndex = 0;
    export let shown: number[] = [];
    export let sliderWidth = 0;
    export let currentScrollPosition = 0;
    export let maxWidth = 0;
    export let reachedEnd = false;
    export let distanceToEnd = 0;

    let isDragging = false;
    let passedThreshold = false;
    let movementStartX = 0;
    let finalScrollPosition = 0;
    let sliderElement: HTMLDivElement;
    let contentElement: HTMLDivElement;
    let observer: ResizeObserver;
    let autoSlidingInterval: number | undefined;

    const dispatch = createEventDispatcher();

    $: if (contentElement) setShown();
    $: if (contentElement) distanceToEnd = maxWidth - currentScrollPosition - sliderWidth;

    onMount(() => {
        createResizeObserver();

        if (autoSlide) {
            resetInterval();
        }
    });

    onDestroy(() => {
        if (exists(observer)) {
            observer.disconnect();
        }

        if (exists(autoSlidingInterval)) {
            window.clearInterval(autoSlidingInterval);
        }
    });

    export function setIndex(i: number, calledFromEnd: boolean = false) {
        const length = contentElement.children.length;

        if (i < 0) {
            i = 0;
        }

        if (i > length - 1) {
            i = length - 1;
        }

        snapToPosition({setIndexProp: i, calledFromEnd});
        setShown();
        resetInterval();
    }

    function resetInterval() {
        if (exists(autoSlidingInterval)) {
            window.clearInterval(autoSlidingInterval);
        }

        autoSlidingInterval = window.setInterval(() => setIndex(currentIndex + 1), autoSlideInterval);
    }

    function down(event: MouseEvent | TouchEvent) {
        if (!isCurrentSlider(event.target as Element)) {
            return;
        }

        event.preventDefault();

        movementStartX = (event as MouseEvent).pageX || (event as TouchEvent).touches[0].pageX;
        isDragging = true;
    }

    function up() {
        if (!isDragging) {
            return;
        }

        if (!passedThreshold) {
            snapToPosition({setIndexProp: currentIndex});
        } else {
            const difference = currentScrollPosition - finalScrollPosition;
            const direction = difference > 0 ? 1 : -1;

            if (difference !== 0) {
                snapToPosition({direction});
            }
        }

        isDragging = false;
        passedThreshold = false;
    }

    function move(event: MouseEvent | TouchEvent) {
        if (!isDragging) {
            return;
        }

        passedThreshold = Math.abs(currentScrollPosition - finalScrollPosition) > threshold;

        const pageX = (event as MouseEvent).pageX || (event as TouchEvent).touches[0].pageX;

        setScrollPosition(finalScrollPosition + (movementStartX - pageX));
        setShown();
    }

    function keydown(event: KeyboardEvent) {
        if (!isCurrentSlider(document.activeElement)) {
            return;
        }

        if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
            return;
        }

        if (event.key === 'ArrowLeft') {
            setIndex(currentIndex - 1);
        }

        if (event.key === 'ArrowRight') {
            setIndex(currentIndex + 1);
        }
    }

    function snapToPosition({setIndexProp = -1, direction = 1, calledFromEnd = false}: { setIndexProp?: number; direction?: number, calledFromEnd?: boolean } = {}) {
        const offsets = getItemOffsets();
        const startIndex = currentIndex;

        currentIndex = 0;

        let i = 0;
        for (i = 0; i < offsets.length; i++) {
            if (setIndexProp !== -1) {
                if (i >= setIndexProp) break;
            } else if ((direction > 0 && offsets[i] > currentScrollPosition) || (direction < 0 && offsets[i + 1] > currentScrollPosition)) {
                break;
            }
        }

        currentIndex = Math.min(i, getContentChildren().length - 1);
        setScrollPosition(offsets[currentIndex], true, calledFromEnd);
        finalScrollPosition = currentScrollPosition;

        if (currentIndex !== startIndex) {
            dispatch('change', currentIndex);
        }
    }

    function setScrollPosition(left: number, limit = false, calledFromEnd = false) {
        currentScrollPosition = left;

        const end = maxWidth - sliderWidth;

        reachedEnd = currentScrollPosition >= end;
        if (reachedEnd && !isDragging) {
            if(!calledFromEnd) {
                dispatch('end');
            }

            if (fill && limit) {
                currentScrollPosition = end;
            }
        }
    }

    function setShown() {
        const offsets = getItemOffsets();

        Array.from(offsets).forEach((offset, index) => {
            if (currentScrollPosition + sliderWidth < offset) return;
            if (!shown.includes(index)) shown = [...shown, index];
        });
    }

    function getItemOffsets(): number[] {
        return getContentChildren().map((item) => item.offsetLeft);
    }

    function getContentChildren(): HTMLDivElement[] {
        return Array.from(contentElement.children).filter((child) => child.getAttribute('src') !== 'about:blank') as HTMLDivElement[];
    }

    function createResizeObserver() {
        observer = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const contentBoxSize = Array.isArray(entry.contentBoxSize) ? entry.contentBoxSize[0] : entry.contentBoxSize;
                maxWidth = contentBoxSize.inlineSize;
            }
        });

        observer.observe(contentElement);
    }

    function isCurrentSlider(element: Element): boolean {
        const closestElement = element.closest('.kp-slider');
        return element === sliderElement || closestElement === sliderElement;
    }
</script>

<svelte:window on:mousedown={down}
               on:mouseup={up}
               on:mousemove={move}
               on:touchstart={down}
               on:touchend={up}
               on:touchmove={move}
               on:keydown={keydown}/>

<div class="kp-slider"
     tabindex="-1"
     class:dragging={isDragging}
     class:passed-threshold={passedThreshold}
     bind:this={sliderElement}
     bind:clientWidth={sliderWidth}>

    <!-- svelte-ignore a11y-no-noninteractive-tabindex -->
    <div class="slider-content"
         tabindex="0"
         bind:this={contentElement}
         style:transform="translateX({currentScrollPosition * -1}px)"
         style:transition-duration="{isDragging ? 0 : transitionDuration}ms"
         style:--gap={gap}>

        <slot {sliderWidth}
              {shown}
              {currentIndex}
              {setIndex}
              {currentScrollPosition}
              {maxWidth}
              {reachedEnd}
              {distanceToEnd}/>
    </div>
</div>

<slot name="controls"
      {sliderWidth}
      {shown}
      {currentIndex}
      {setIndex}
      {currentScrollPosition}
      {maxWidth}
      {reachedEnd}
      {distanceToEnd}/>

<style lang="less">
    .kp-slider {
        overflow-x: hidden;
        touch-action: pan-y;

        &.passed-threshold {
            @media (pointer: fine) {
                pointer-events: none;
            }
        }
    }

    .slider-content {
        display: flex;
        align-items: flex-start;
        width: fit-content;
        gap: var(--gap, 0);
        user-select: none;
        transition: transform;
    }

    @media (prefers-reduced-motion) {
        .slider-content {
            transition: none;
        }
    }
</style>