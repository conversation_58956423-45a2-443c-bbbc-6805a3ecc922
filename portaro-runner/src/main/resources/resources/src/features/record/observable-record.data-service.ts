import type {ObservableAjaxService} from 'core/data-services/observable-ajax.service';
import type {Observable} from 'rxjs';
import type {Document} from 'typings/portaro.be.types';
import RecordDataService from './record.data-service';

/**
 * @ngdoc service
 * @name observableRecordDataService
 * @module portaro.data-services
 *
 * @description
 * Service for `Document` handling
 *
 * @requires service:observableAjaxService
 */
export default class ObservableRecordDataService {
    public static serviceName = 'observableRecordDataService';

    /*@ngInject*/
    constructor(private observableAjaxService: ObservableAjaxService) {
    }

    /**
     * @ngdoc method
     * @name observableRecordDataService#getDocument
     *
     * @param {string} recordId Document ID
     * @returns {Observable<Document>}
     *
     * @description
     * Return observable Document by ID
     */
    public getDocument(recordId: string): Observable<Document> {
        return this.observableAjaxService
            .forUri(`${RecordDataService.RECORD_ROUTE}/${recordId}`)
            .get<Document>();
    }
}
