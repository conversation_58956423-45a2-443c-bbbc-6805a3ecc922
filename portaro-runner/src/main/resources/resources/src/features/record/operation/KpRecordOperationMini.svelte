<script lang="ts">
    import {pipe} from 'core/utils';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {Kind} from 'shared/constants/portaro.constants';
    import Label from 'shared/components/kp-label/Label.svelte';
    import type {RecordOperation} from 'typings/portaro.be.types';

    export let operation: RecordOperation;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
</script>

<div class="record-operation-mini">
    <div class="details">
        <strong>{operation.recordOperationType.text}</strong>

        <span class="text-muted">{localize(/* @kp-localization commons.byUser */ 'commons.byUser')}</span>
        <Label labeled={operation.user} explicitKind={Kind.KIND_USER}/>

        <span class="text-muted">na</span>
        <span>{operation.department.text}</span>
    </div>

    <div class="timestamp">
        <span>{pipe(operation.date, dateFormatter('d<PERSON><PERSON><PERSON>yyyy'))}</span>
        <span class="text-muted">{pipe(operation.date, dateFormatter('HH:mm'))}</span>
    </div>
</div>

<style lang="less">
    .record-operation-mini {
        display: flex;
        justify-content: space-between;

        .timestamp {
            white-space: nowrap;
        }
    }
</style>