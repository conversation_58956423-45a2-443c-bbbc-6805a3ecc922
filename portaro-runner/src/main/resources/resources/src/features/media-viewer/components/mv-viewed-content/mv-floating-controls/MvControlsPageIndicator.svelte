<script lang="ts">
    import type {ViewedContentPageData} from '../../../lib/mv-viewed-content';

    export let pageData: ViewedContentPageData;
</script>

<div class="mv-controls-page-indicator">
    {pageData.current}/{pageData.total}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-controls-page-indicator {
        display: flex;
        height: 100%;
        align-items: center;
        padding: 0 @spacing-xl;
        justify-content: center;
        flex-shrink: 0;
        background-color: var(--viewer-orange-highlight);
        border-right: 1px solid var(--viewer-default-border);
    }
</style>