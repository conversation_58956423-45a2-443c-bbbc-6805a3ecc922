import register from '@kpsys/angularjs-register';
import {KpPaymentFormPresenter} from './kp-payment-form.presenter';
import PaymentDataService from './payment.data-service';
import {AmountTypeDataService} from './amount-type.data-service';

export default register('portaro.features.payment')
    .service(AmountTypeDataService.serviceName, AmountTypeDataService)
    .service(KpPaymentFormPresenter.presenterName, KpPaymentFormPresenter)
    .service(PaymentDataService.serviceName, PaymentDataService)
    .name();