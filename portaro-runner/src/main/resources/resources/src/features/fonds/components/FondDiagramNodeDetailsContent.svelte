<script lang="ts">
    import type {Fond} from 'typings/portaro.be.types';
    import type {FondFieldType} from '../types';
    import {onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {KpFondsService} from '../services/kp-fonds.service';
    import {exists} from 'shared/utils/custom-utils';
    import NodeDetailsInfoRow from '../../../modals/diagram-node-details-modal/components/NodeDetailsInfoRow.svelte';
    import BooleanValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/BooleanValue.svelte';
    import KpTitledSection from 'shared/layouts/containers/KpTitledSection.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import FondDetailsFieldTypeRow from './FondDetailsFieldType.svelte';

    export let item: Fond;

    const service = getInjector().getByToken<KpFondsService>(KpFondsService.serviceName);

    let fondFieldTypes: FondFieldType[] = null;

    onMount(async () => {
        fondFieldTypes = await service.getFondFieldTypes(item);
    });
</script>

<div class="fond-diagram-node-details-content">
    <KpTitledSection title="Základní informace"
                     headingLine
                     collapsible
                     collapsedByDefault>

        <NodeDetailsInfoRow heading="ID">{item.id}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Název">{item.name}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Povolen">
            <BooleanValue value="{item.enabled}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="ID rodiče">{item.parentId ?? '-'}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Typ">{item.ofAuthority ? 'Autoritní' : 'Dokumentový'}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Má exempláře">
            <BooleanValue value="{item.withExemplars}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Periodikum">
            <BooleanValue value="{item.periodical}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Typ záznamu (dok)">{item.recordType ?? '-'}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Bib. úroveň (dok)">{item.bibliographicLevel ?? '-'}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Z35.50 typ (aut)">{item.z39Typ ?? '-'}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="Zdroj dok. (aut)">
            <BooleanValue value="{item.forSourceDocument}"/>
        </NodeDetailsInfoRow>
    </KpTitledSection>

    <KpTitledSection title="Pole" headingLine>
        {#if !exists(fondFieldTypes)}
            <KpLoadingBlock size="xs"/>
        {:else}
            <ul class="field-types-list">
                {#each fondFieldTypes as fieldType(fieldType.code)}
                    <FondDetailsFieldTypeRow {fieldType}/>
                {/each}
            </ul>
        {/if}
    </KpTitledSection>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .fond-diagram-node-details-content {
        display: flex;
        flex-direction: column;
        gap: @spacing-xl;

        .field-types-list {
            display: flex;
            flex-direction: column;
            gap: @spacing-l;
        }
    }
</style>