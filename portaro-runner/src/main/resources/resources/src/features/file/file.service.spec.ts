import fileModule from './file.module'
import type {FileService} from './file.service';
import type {FileDataService} from './file.data-service';
import type {FileContentDataService} from './file-content.data-service';
import type {FileUploadService} from './file-upload.service';
import type {FinishedSaveResponse, PermissionResult, ViewableFile} from 'typings/portaro.be.types';
import {ignoreUnusedProperties} from 'shared/utils/custom-utils';

describe('FileService test', () => {

    let fileService: FileService;

    let fileDataServiceMock: Partial<FileDataService>;
    let fileContentDataServiceMock: Pick<FileContentDataService, 'getFileContent'>;
    let fileUploadServiceMock: Pick<FileUploadService, 'upload'>;

    const allowedPermission: PermissionResult = {
        allowed: true
    }

    const testFile: ViewableFile = {
        exportPermission: allowedPermission,
        printPermission: allowedPermission,
        showPermission: allowedPermission,
        accessType: undefined,
        category: undefined,
        creationDate: '',
        creatorId: 0,
        directory: {
            parentDirectoryId: 0,
            id: 42,
            name: '',
            text: '',
            order: 0,
            accessType: {
                id: 1,
                text: ''
            }
        },
        filename: '',
        hasTextualForm: false,
        id: 666,
        name: '',
        size: 0,
        source: '',
        text: '',
        url: '',
        viewForms: []
    }

    const progressHandler = (progress: number) => {
        ignoreUnusedProperties(progress);
    }

    const testFiles = [testFile, {...testFile, id: 667}, {...testFile, id: 668}];
    const testFinishedSavedResponse: FinishedSaveResponse<ViewableFile> = {
        finished: true,
        responseType: 'test-type',
        savedObject: testFile,
        text: 'Pošťák Pat'
    };

    beforeEach(() => {
        angular.mock.module(fileModule);

        fileContentDataServiceMock = {
            getFileContent: jasmine.createSpy().and.resolveTo('content')
        }

        fileUploadServiceMock = {
            upload: jasmine.createSpy().and.resolveTo(testFile)
        }

        fileDataServiceMock = {
            createFile: jasmine.createSpy().and.resolveTo({savedObject: testFile}),
            editFileMetadata: jasmine.createSpy().and.resolveTo(testFinishedSavedResponse),
            queryFiles: jasmine.createSpy().and.resolveTo(testFiles),
            getFile: jasmine.createSpy().and.resolveTo(testFile),
            removeFile: jasmine.createSpy().and.resolveTo(),
            modifyFileContent: jasmine.createSpy().and.resolveTo(testFile),
        }

        angular.mock.module(/*@ngInject*/ ($provide) => {
            $provide.value('fileDataService', fileDataServiceMock);
            $provide.value('fileContentDataService', fileContentDataServiceMock);
            $provide.value('fileUploadService', fileUploadServiceMock);
        });

        inject(/*@ngInject*/ ($injector) => {
            fileService = $injector.get('fileService');
        });
    });

    describe('getFilePath', () => {
        it('should return /files/666', () => {
            expect(fileService.getFilePath(testFile)).toEqual('/files/666');
        });
    });

    describe('getFilesByDirectory', () => {
        it('should call fileDataService', async () => {
            await fileService.getFilesByDirectory(42, [69]);
            expect(fileDataServiceMock.queryFiles).toHaveBeenCalledWith({directory: [42], excludedFile: [69]});
        });

        it('should return defined viewableFiles as result', async () => {
            const result = await fileService.getFilesByDirectory(42, [69]);
            expect(result).toBeDefined();
            expect(result).toEqual(testFiles);
        });

        it('should set files URL to /files/{id}', async () => {
            const result = await fileService.getFilesByDirectory(42, [69]);
            expect(result.length).toEqual(3);
            expect(result[0].url).toEqual('/files/666');
            expect(result[1].url).toEqual('/files/667');
            expect(result[2].url).toEqual('/files/668');
        });

        it('should cache results', async () => {
            // @ts-ignore get private property for testing only
            const {filesByDirectoryCache} = fileService;

            expect(filesByDirectoryCache.get(42)).not.toBeDefined();

            const result1 = await fileService.getFilesByDirectory(42);
            expect(filesByDirectoryCache.get(42)).toBeDefined();
            expect(filesByDirectoryCache.get(42).length).toEqual(3);
            expect(filesByDirectoryCache.get(42)).toEqual(testFiles);
            expect(filesByDirectoryCache.get(42)).toBe(result1);

            const result2 = await fileService.getFilesByDirectory(42);
            expect(result1).toBe(result2);
            expect(fileDataServiceMock.queryFiles).toHaveBeenCalledTimes(1);
        });
    });

    describe('getFileById', () => {
        it('should call fileDataService', async () => {
            await fileService.getFileById(666);
            expect(fileDataServiceMock.getFile).toHaveBeenCalledWith(666);
        });

        it('should return defined viewableFile as result', async () => {
            const result = await fileService.getFileById(666);
            expect(result).toBeDefined();
            expect(result).toEqual(testFile);
            expect(result.id).toEqual(666);
        });

        it('should set file URL to /files/666', async () => {
            const result = await fileService.getFileById(666);
            expect(result.url).toEqual('/files/666');
        });
    });

    describe('deleteFile', () => {
        it('should call fileDataService', async () => {
            await fileService.deleteFile(testFile);
            expect(fileDataServiceMock.removeFile).toHaveBeenCalledWith(testFile);
        });

        it('should clear filesByDirectoryCache', async () => {
            // @ts-ignore get private property for testing only
            const {filesByDirectoryCache} = fileService;

            await fileService.getFilesByDirectory(42);
            expect(filesByDirectoryCache.get(42)).toBeDefined();

            await fileService.deleteFile(testFile);
            expect(filesByDirectoryCache.get(42)).not.toBeDefined();
        });
    });

    describe('createNewFile', () => {
        it('should call fileDataService', async () => {
            await fileService.createNewFile(42, 'some-file.txt');
            expect(fileDataServiceMock.createFile).toHaveBeenCalledWith(42, 'some-file.txt');
        });

        it('should return defined viewableFile as result', async () => {
            const result = await fileService.createNewFile(42, 'some-file.txt');
            expect(result).toBeDefined();
            expect(result).toEqual(testFile);
            expect(result.id).toEqual(666);
        });

        it('should set file URL to /files/666', async () => {
            const result = await fileService.createNewFile(42, 'some-file.txt');
            expect(result.url).toEqual('/files/666');
        });

        it('should clear filesByDirectoryCache', async () => {
            // @ts-ignore get private property for testing only
            const {filesByDirectoryCache} = fileService;

            await fileService.getFilesByDirectory(42);
            expect(filesByDirectoryCache.get(42)).toBeDefined();

            await fileService.createNewFile(42, 'some-file.txt')
            expect(filesByDirectoryCache.get(42)).not.toBeDefined();
        });
    });

    describe('uploadFile', () => {
        it('should call fileUploadService', async () => {
            await fileService.uploadFile(42, {} as File, progressHandler);
            expect(fileUploadServiceMock.upload).toHaveBeenCalledWith('/api/files/upload', {} as File, {directory: 42}, progressHandler);
        });

        it('should return defined viewableFile as result', async () => {
            const result = await fileService.uploadFile(42, {} as File);
            expect(result).toBeDefined();
            expect(result).toEqual(testFile);
            expect(result.id).toEqual(666);
        });

        it('should clear filesByDirectoryCache', async () => {
            // @ts-ignore get private property for testing only
            const {filesByDirectoryCache} = fileService;

            await fileService.getFilesByDirectory(42);
            expect(filesByDirectoryCache.get(42)).toBeDefined();

            await fileService.uploadFile(42, {} as File);
            expect(filesByDirectoryCache.get(42)).not.toBeDefined();
        });
    });

    describe('editFileMetadata', () => {
        it('should call fileDataService with confirmed confirmable request as param', async () => {
            await fileService.editFileMetadata(testFile);
            expect(fileDataServiceMock.editFileMetadata).toHaveBeenCalledWith({...testFile, confirmed: false});
        });

        it('should return FinishedSaveResponse', async () => {
            const response = await fileService.editFileMetadata(testFile);
            expect(response).toBeDefined();
            expect(response).toEqual(testFinishedSavedResponse);
        });

        it('should clear filesByDirectoryCache', async () => {
            // @ts-ignore get private property for testing only
            const {filesByDirectoryCache} = fileService;

            await fileService.getFilesByDirectory(42);
            expect(filesByDirectoryCache.get(42)).toBeDefined();

            await fileService.editFileMetadata(testFile);
            expect(filesByDirectoryCache.get(42)).not.toBeDefined();
        });
    });

    describe('getFileContent', () => {
        it('should call fileContentDataService', async () => {
            await fileService.getFileContent(testFile);
            expect(fileContentDataServiceMock.getFileContent).toHaveBeenCalledWith(testFile);
        });

        it('should return \'content\'', async () => {
            const result = await fileService.getFileContent(testFile);
            expect(result).toBeDefined();
            expect(result).toEqual('content');
        });
    });
});