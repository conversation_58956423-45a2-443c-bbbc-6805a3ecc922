import type {SearchSelectionModalModel} from '../../modals/search-selection-modal/types';
import type {SvelteComponentConstructor} from 'core/types';
import type {LoanSelectionModalModel} from '../../modals/loan-selection-modal/types';
import type LoanDataService from './loan.data-service';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type ExemplarService from '../record/exemplar.service';
import {Kind, SearchType} from 'shared/constants/portaro.constants';
import {LoanState} from 'typings/portaro.be.types';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {isInterruptedActionResponse} from '../../modals/modal-utils';
import KpSelectableReturningLoanCard from '../user/KpSelectableReturningLoanCard.svelte';
import type {
    Exemplar,
    FinishedLendingActionResponse,
    FinishedReturningActionResponse,
    Loan,
    User
} from 'typings/portaro.be.types';

type ResponseBasedOnSubjectType<ITEM = Exemplar | Loan> = ITEM extends Exemplar ? FinishedLendingActionResponse : FinishedReturningActionResponse;

/**
 * @ngdoc service
 * @name loanService
 * @module portaro.services
 *
 * @description
 * Service for lending and returning
 */
export default class LoanService {
    public static readonly serviceName = 'loanService';

    private readonly ONE_QUANTITY = 1;

    /*@ngInject*/
    constructor(
        private loanDataService: LoanDataService,
        private modalDialogService: ModalDialogService,
        private finishedResponseInteractionService: FinishedResponseInteractionService,
        private exemplarService: ExemplarService
    ) {
    }

    /**
     * Show lending form and perform lending in loop.
     */
    @ngAsync()
    public async showLendingForm(user: User): Promise<FinishedLendingActionResponse[]> {
        const modalModel = {
            resultPostProcessFunction: this.exemplarService.extendExemplarsOfDocumentAndAvailability.bind(this.exemplarService),
            staticSearchParams: {
                kind: [Kind.KIND_EXEMPLAR],
                type: SearchType.TYPE_SEARCH_SELECTION
            }
        };
        return this.showLoopingForm(modalModel, (exemplar: Exemplar) => this.lendExemplar(exemplar, user));
    }

    /**
     * Show returning form and perform loan return in loop.
     */
    @ngAsync()
    public async showReturningForm(): Promise<FinishedReturningActionResponse[]> {
        const modalModel: SearchSelectionModalModel = {
            staticSearchParams: {
                kind: [Kind.KIND_LOAN],
                type: SearchType.TYPE_SEARCH_SELECTION
            },
            customComponent: KpSelectableReturningLoanCard as SvelteComponentConstructor<{
                value: Loan,
                isSelected: boolean
            }>
        };
        return this.showLoopingForm(modalModel, (loan: Loan) => this.returnLoan(loan));
    }

    /**
     * Try to return loan and show result in toast.
     */
    @ngAsync()
    public async returnLoanAndShowResult(loan: Loan): Promise<FinishedReturningActionResponse> {
        try {
            const response = await this.returnLoan(loan);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response;

        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            }
            throw exceptionResponse;
        }
    }

    public async returnLoan(loan: Loan): Promise<FinishedReturningActionResponse> {
        return this.loanDataService.returnLoan(loan);
    }

    /**
     * Show searchSelection model, which is basically modal with universal form (form is resolved by backend by modalModel.staticSearchParams.kind field) and perform search
     * with given parameters and then show results in selectable components. Then user select one item and this selected item is used as input for `action` param.
     * In this case it can be only `Exemplar` or `Loan`. Then action is performed (return loan or lend exemplar) and response is saved into array.
     * If all is well done, user is informed about success and next searching (loop) is performed.
     * Modal is closed by user or by failing of action.
     * Loop ends by closing of modal and list of responses is returned.
     *
     * @param {SearchSelectionModalModel} modalModel Custom modal model.
     * @param {(selectedItem: ITEM) => Promise<ResponseBasedOnSubjectType<ITEM>>} action What to do with selected item.
     * @returns {Promise<ResponseBasedOnSubjectType<ITEM>[]>} List of responses.
     * @private
     */
    private async showLoopingForm<ITEM = Exemplar | Loan>(modalModel: SearchSelectionModalModel, action: (selectedItem: ITEM) => Promise<ResponseBasedOnSubjectType<ITEM>>): Promise<ResponseBasedOnSubjectType<ITEM>[]> {
        const successResponses: ResponseBasedOnSubjectType<ITEM>[] = [];

        while (true) {
            try {
                const selectedItem = await this.modalDialogService.openModalWindowAndGetPayload('searchSelection', modalModel) as ITEM;
                const response = await action(selectedItem);
                successResponses.push(response);
                await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);

            } catch (exceptionResponse) {
                if (!isInterruptedActionResponse(exceptionResponse)) {
                    this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
                }
                break;
            }
        }

        return successResponses;
    }

    private lendExemplar(exemplar: Exemplar, user: User): Promise<FinishedLendingActionResponse> {
        return this.loanDataService.lendExemplar(exemplar, user, exemplar.loanCategory.chunkable ? exemplar.quantity : this.ONE_QUANTITY)
    }

    public async processLoan() {
        try {
            const exemplar = await this.exemplarService.searchExemplarViaModal();

            const loanSearchModalModel: LoanSelectionModalModel = {
                exemplarId: exemplar.id,
                loansFilter(loans: Loan[]): Loan[] {
                    return loans.filter(({state}) => [LoanState.UNPROCESSED_ORDER, LoanState.UNSENT_RESERVATION].includes(state.id as LoanState));
                }
            };

            const loan = await this.selectLoan(loanSearchModalModel);

            const actionResponse = await this.loanDataService.processLoan(loan.id, exemplar.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            }
            throw exceptionResponse;
        }
    }

    private async selectLoan(loanSearchModalModel: LoanSelectionModalModel): Promise<Loan> {
        return this.modalDialogService.openModalWindowAndGetPayload('loanSelection', loanSearchModalModel);
    }

}
