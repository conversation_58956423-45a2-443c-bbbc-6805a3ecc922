<script lang="ts">
    import type {SutorHomePageData} from './types';
    import {getInjector} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {onMount} from 'svelte';
    import {SutorHomePageService} from './services/sutor-home-page.service';
    import SutorHomepageNotificationsPart from './parts/SutorHomepageNotificationsPart.svelte';
    import SutorHomeWelcomePart from './parts/SutorHomeWelcomePart.svelte';
    import ErpPageLayout from '../../../erp/components/ErpPageLayout.svelte';
    import SutorHomeCardsPart from 'src/features/erp-sutor/pages/homepage/parts/SutorHomeCardsPart.svelte';

    const service = getInjector().getByToken<SutorHomePageService>(SutorHomePageService.serviceName);

    let homepageData: SutorHomePageData;
    let loading = true;
    $: loadError = !exists(homepageData) && !loading;

    onMount(async () => {
        try {
            homepageData = await service.getHomePageData();
        } finally {
            loading = false;
        }
    });
</script>

<ErpPageLayout pageClass="sutor-landing-page"
               dontCollapseSidebar
               {loading}
               {loadError}>

    <SutorHomeWelcomePart slot="heading" let:auth catalogUrls="{homepageData.catalogs}" user="{auth.activeUser}"/>

    <svelte:fragment>
        <SutorHomepageNotificationsPart/>
        <SutorHomeCardsPart {homepageData}/>
    </svelte:fragment>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro-erp.less";

    :global {
        sutor-landing-page {
            .flex-grow();
        }
    }
</style>