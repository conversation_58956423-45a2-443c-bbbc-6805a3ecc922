import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Filter} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class FiltersDataService {
    public static serviceName = 'filtersDataService';

    public static readonly ROUTE = 'filters';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<Filter[]> {
        return this.ajaxService
            .createRequest(`${FiltersDataService.ROUTE}`)
            .get();
    }
}