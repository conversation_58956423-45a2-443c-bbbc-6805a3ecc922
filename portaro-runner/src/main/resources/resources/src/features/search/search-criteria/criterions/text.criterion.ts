import type {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Matcher} from '../criteria.types';
import {isUndefined} from 'shared/utils/custom-utils';

export default class TextCriterion implements Criterion {
    public value: { extension: string; row: string };

    constructor(public field: <PERSON><PERSON><PERSON><PERSON><PERSON>, matcher?, startsWith?) {
        this.value = {
            extension: isUndefined(startsWith) || startsWith ? 'startsWith' : 'equals',
            row: matcher ? matcher.value : ''
        };
    }

    public clear() {
        this.value.row = '';
    }

    public isEmpty() {
        return !this.value.row;
    }

    public toMatcher(): Matcher {
        if (this.value.extension === 'startsWith') {
            return {
                startsWithWords: {
                    value: this.value.row
                }
            };
        } else {
            return {
                eqWords: {
                    value: this.value.row
                }
            };
        }
    }
}
