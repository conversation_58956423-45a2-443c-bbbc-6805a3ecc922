<script lang="ts" generics="ITEM">
    import type {Conjunction, Criterion} from '../search-criteria/criteria.types';
    import type {SearchParams, ViewableSearch} from 'typings/portaro.be.types';
    import type {SearchManagerState} from '../search-manager/search-manager';
    import type {Facet} from 'typings/portaro.be.types';
    import {getSearchContext} from '../kp-search-context/search-context';
    import {cleanup, exists, identifiedValuesEquals} from 'shared/utils/custom-utils';
    import {findFirst} from 'shared/utils/array-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onDestroy} from 'svelte';
    import CriterionsFormManager from '../search-criteria/criterions-form-manager';
    import CriterionFactory from '../search-criteria/criterion-factories/criterion.factory';
    import CriterionMarshaller from '../search-criteria/criterion-marshallers/criterion-marshaller';
    import {isEqual} from 'lodash';
    import SearchToolbarSection from './SearchToolbarSection.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import SearchToolbarFacetDropdown from 'src/features/search/kp-search-toolbar/SearchToolbarFacetDropdown.svelte';

    const searchManager = getSearchContext<ITEM>();
    const criterionsFormManager = new CriterionsFormManager(new CriterionFactory());
    const criterionMarshaller = new CriterionMarshaller(new CriterionFactory());

    let lastSearch: ViewableSearch<SearchParams, ITEM>;
    let searchManagerState: SearchManagerState;
    let criterions: Criterion[] = [];
    let isAnySelected = false;
    const lastSearchSubscription = searchManager.getLastSearch$().subscribe((currentLastSearch) => lastSearch = currentLastSearch);
    const searchManagerSubscription = searchManager.getState$().subscribe((currentSearchManagerState) => searchManagerState = currentSearchManagerState);

    $: hasAnyFacets = exists(lastSearch) && lastSearch.features && lastSearch.features.includes('faceting') && lastSearch.result.availableFacets.length > 0;
    $: if (exists(lastSearch)) refresh(lastSearch.result.availableFacets, searchManagerState.frontendParams.facetRestriction);

    onDestroy(() => {
        cleanup(lastSearchSubscription, searchManagerSubscription);
    });

    function changeRestrictions() {
        searchManager.newSearchWithPartialParams({facetRestriction: criterionMarshaller.fromCriterionsToConjunction(criterions)});
    }

    function deactivateAllKeys(): void {
        criterionsFormManager.clearCriterionValues();
        criterions = criterionsFormManager.getCriterions();
        isAnySelected = criterionsFormManager.isAnyCriterionFilled();
        changeRestrictions();
    }

    function refresh(updatedFacets: Facet[], updatedFacetRestriction: Conjunction | null): void {
        const filledCriterions = !exists(updatedFacetRestriction) ? [] : criterionMarshaller.fromConjunctionToCriterions(updatedFacetRestriction);

        criterionsFormManager.setCriterions(
            updatedFacets
                .map((facet) => {
                    // We try to find the criterion in the filled ones; if we don’t find it, we create a new one from the field
                    const filledCrit = findFirst(filledCriterions, (filledCriterion) => identifiedValuesEquals(filledCriterion.field, facet));
                    if (exists(filledCrit)) {
                        filledCrit.field = facet;
                        return filledCrit;
                    }

                    return criterionsFormManager.createCriterionByField(facet);
                })
        );

        if (!isEqual(criterions, criterionsFormManager.getCriterions())) {
            criterions = criterionsFormManager.getCriterions();
        }

        isAnySelected = criterionsFormManager.isAnyCriterionFilled();
    }

    function sortCriterions(criterionsToSort: Criterion[]): Criterion[] {
        return criterionsToSort.sort((a, b) => {
            const fieldA = pipe(a.field, loc()).toLowerCase();
            const fieldB = pipe(b.field, loc()).toLowerCase();

            if (fieldA < fieldB) return -1;
            if (fieldA > fieldB) return 1;
            return 0;
        });
    }
</script>

{#if hasAnyFacets}
    <SearchToolbarSection icon="filter" title="Filtry" additionalClasses="search-toolbar-facets" wrap>
        {#each sortCriterions(criterions) as criterion (criterion.field.id)}
            <SearchToolbarFacetDropdown {criterion}
                                        {searchManagerState}
                                        {criterionMarshaller}
                                        on:criterion-change={changeRestrictions}/>
        {/each}

        {#if isAnySelected}
            <button class="deactivate-facets-button" on:click={deactivateAllKeys}>
                <IconedContent icon="cross-circle">Vymazat filtry</IconedContent>
            </button>
        {/if}
    </SearchToolbarSection>
{/if}

<style lang="less">
    :global {
        .search-toolbar-facets .facet .facet-name {
            border-top: none;
            white-space: nowrap;
        }
    }

    .deactivate-facets-button {
        outline: none;
        border: none;
        background: none;
        cursor: pointer;
        padding: 0;
        color: var(--danger-red);
        transition: opacity 0.3s ease-in-out;

        &:hover {
            opacity: 0.75;
        }
    }
</style>