import type {Readable} from 'svelte/store';
import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
import {writable} from 'svelte/store';
import {getContext, hasContext, setContext} from 'svelte';

const contextKey = 'search-field-type-filters-context';

export interface SearchFieldTypeFiltersContext {
    fieldTypeDefinitions$: Readable<FondFieldTypeDefinitions | null>;
    setFieldTypeDefinitions: (fieldTypeDefinitions: FondFieldTypeDefinitions | null) => void;
}

export function createSearchFieldTypeFiltersContext(): SearchFieldTypeFiltersContext {
    const fieldTypeDefinitions = writable<FondFieldTypeDefinitions | null>(null);

    return setContext<SearchFieldTypeFiltersContext>(contextKey, {
        fieldTypeDefinitions$: fieldTypeDefinitions,
        setFieldTypeDefinitions: (newFieldTypeDefinitions) => fieldTypeDefinitions.set(newFieldTypeDefinitions)
    });
}

export function getSearchFieldTypeFiltersContextIfExists(): SearchFieldTypeFiltersContext | null {
    if (!hasContext(contextKey)) {
        return null;
    }

    return getContext<SearchFieldTypeFiltersContext>(contextKey);
}