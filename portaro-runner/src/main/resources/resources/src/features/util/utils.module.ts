import register from '@kpsys/angularjs-register';
import {utilRoutes} from './util.routes';
import {UtilsDataService} from './service/utils.data-service';
import {LoggerDataService} from './service/logger.data-service';
import {ServerStateDataService} from './service/server-state.data-service';
import {KpUtilService} from './service/kp-util.service';

export default register('portaro.features.utils')
    .config(utilRoutes)
    .service(UtilsDataService.serviceName, UtilsDataService)
    .service(LoggerDataService.serviceName, LoggerDataService)
    .service(ServerStateDataService.serviceName, ServerStateDataService)
    .service(KpUtilService.serviceName, KpUtilService)
    .name();