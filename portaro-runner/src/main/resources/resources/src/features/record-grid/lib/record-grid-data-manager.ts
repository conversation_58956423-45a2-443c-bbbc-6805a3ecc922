import type {GridStateManager} from 'shared/ui-widgets/grid/state/state-management';
import type {Row, Table} from '@tanstack/svelte-table';
import type {Logger} from 'src/core/logging/types';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {GridCommand} from 'shared/ui-widgets/grid/types';
import {CellCoordinates} from 'shared/ui-widgets/grid/types';
import type {ClipboardCellData} from 'shared/ui-widgets/grid/clipboard/clipboard';
import {createClipBoardItem, resolveClipboardData} from 'shared/ui-widgets/grid/clipboard/clipboard';
import {getCellValueByCoordinates, getColumnByIndex, getRowByIndex} from 'shared/ui-widgets/grid/utils';
import {assertUnreachable, cleanup, exists} from 'shared/utils/custom-utils';
import type {Grid<PERSON>ield, HierarchicalRecordRow, KpRecordGridDisplayEvents, RecordEditationPhaseId, RecordRow, SimpleGridFieldValue} from 'src/features/record-grid/lib/types';
import type {BehaviorSubject, Subscription} from 'rxjs';
import {Subject, tap} from 'rxjs';
import {byIdOf, removeAll, replaceAll, uuidComparator, zip} from 'shared/utils/array-utils';
import type {RecordGridService} from 'src/features/record-grid/services/record-grid.service';
import type {Readable, Unsubscriber} from 'svelte/store';
import {COLUMN_TYPES} from 'src/features/record-grid/lib/constants';
import {resolveGridFieldAttributeValue} from 'src/features/record-grid/lib/grid-utils';
import type {FieldId, FieldTypeId, Fond, SetFieldValueRequest,} from 'typings/portaro.be.types';
import type {EventDispatcher} from 'svelte';
import {tick} from 'svelte';
import {deleteFieldByFieldIdRecursive, setFieldByFieldIdRecursive} from 'src/features/record-grid/lib/grid-fields';
import type {LocalizeFunction} from 'core/types';
import type {PlainTextParsingContext} from 'src/features/record-grid/lib/clipboard';
import {marshallCellValue, unmarshallCellValue} from 'src/features/record-grid/lib/clipboard';
import {generateIdOfInitialField} from 'src/features/record-grid/components/cells/data-cell/utils';
import {asFieldTypeId} from 'shared/utils/record-field-utils';
import {bufferDebounce, shareWithRefCount, updateValue} from 'shared/utils/observables-utils';
import {downgradeToRecordRow, isSubrow} from 'src/features/record-grid/lib/record-row';

export class RecordGridDataManager {

    private table: Table<HierarchicalRecordRow>;
    private refreshRowRequest$ = new Subject<Row<HierarchicalRecordRow>>();
    private readonly refreshRequestSubscription: Subscription;
    private readonly commandsSubscription: Subscription;
    private readonly tableUnsubscriber: Unsubscriber;

    constructor(private table$: Readable<Table<HierarchicalRecordRow>>,
                private stateManager: GridStateManager,
                private flatRootRows$: BehaviorSubject<RecordRow[]>,
                private flatDraftRootRows$: BehaviorSubject<RecordRow[]>,
                private flatSubrows$: BehaviorSubject<RecordRow[]>,
                private command$: Subject<GridCommand>,
                private recordGridService: RecordGridService,
                private initialValuesEntries: [FieldTypeId, SetFieldValueRequest][],
                private localize: LocalizeFunction,
                private locale: string,
                private logger: Logger,
                private toastMessageService: ToastMessageService,
                private eventDispatcher: EventDispatcher<KpRecordGridDisplayEvents>) {

        this.tableUnsubscriber = this.table$.subscribe((table) => this.table = table);

        this.refreshRequestSubscription = this.refreshRowRequest$.pipe(
            shareWithRefCount(),
            bufferDebounce(500),
            tap((rows) => {this.refreshRows(rows);})
        ).subscribe();

        this.commandsSubscription = this.command$.subscribe((command) => this.processCommand(command));
    }

    public disconnect() {
        cleanup(this.refreshRequestSubscription, this.commandsSubscription, this.tableUnsubscriber);
    }

    public processCommand(command: GridCommand) {
        switch (command.type) {
            case 'delete':
                this.deleteCell(command.target);
                break;
            case 'copy':
                this.copyCell(command.target);
                break;
            case 'paste':
                this.pasteCell(command.target);
                break;
            case 'cut':
                this.cutCell(command.target);
                break;
            case 'rollback-cell-change':
                break;
            default:
                assertUnreachable(command.type);
        }
    }

    public async copyCell(sourceCell: CellCoordinates) {
        try {
            const cellData = this.extractCellData(sourceCell);
            const clipboardItem = createClipBoardItem(cellData);
            await navigator.clipboard.write([clipboardItem]);
        } catch (error) {
            this.logger.error('Error during copying from grid', error);
            this.toastMessageService.showError('Error during copying from grid');
        }
    }

    public async pasteCell(targetCell: CellCoordinates) {
        try {
            const clipboardItems = await navigator.clipboard.read();
            const clipboardData = await resolveClipboardData(clipboardItems);
            if (exists(clipboardData)) {
                const targetColumn = getColumnByIndex(this.table, targetCell.columnIndex);
                const ctx: PlainTextParsingContext = {
                    datatype: targetColumn.columnDef.meta.datatype,
                    editorType: targetColumn.columnDef.meta.editorType,
                    localize: this.localize,
                    locale: this.locale
                };
                const fieldValues = unmarshallCellValue(clipboardData, ctx);
                const row = getRowByIndex(this.table, targetCell.rowIndex);
                const recordRow = row.original;
                const fieldId = generateIdOfInitialField(asFieldTypeId(targetColumn.id));

                this.updateTableDataCellState(recordRow, fieldId, fieldValues.at(0));
                await this.updateServerCellState(recordRow, fieldId, fieldValues.at(0));
                this.requestRowRefresh(row);
            }
        } catch (error) {
            this.logger.error('Error during pasting values to grid', error);
            this.toastMessageService.showError('Error during pasting values to grid');
        }
    }

    public async cutCell(sourceCell: CellCoordinates) {
        await this.copyCell(sourceCell);
        await this.deleteCell(sourceCell);
    }


    public async deleteCell(targetCell: CellCoordinates) {
        const row = getRowByIndex(this.table, targetCell.rowIndex);
        const recordRow = row.original;
        const fields = getCellValueByCoordinates<HierarchicalRecordRow, GridField<SimpleGridFieldValue>[]>(this.table, targetCell);

        fields.forEach((field) => this.deleteCellValueFromTableData(recordRow, field.fieldId));

        try {
            await Promise.all(fields.map((field) => this.deleteCellValueOnServer(recordRow, field.fieldId)));
        } catch (error) {
            this.logger.error('Error during deleting values from grid', error);
            this.toastMessageService.showError('Error during deleting values from grid');
        } finally {
            this.requestRowRefresh(row);
        }
    }

    public requestRowRefresh(row: Row<HierarchicalRecordRow>) {
        this.refreshRowRequest$.next(row);
    }

    public async createRow(selectedSubFond: Fond) {
        const initialValues = this.createInitialValues();
        const newRecordRow = await this.recordGridService.createNewRow(selectedSubFond, initialValues);
        await this.addDraftRow(newRecordRow);
    }

    public async changePhase(row: Row<HierarchicalRecordRow>, phase: RecordEditationPhaseId) {
        const newRow = await this.recordGridService.changePhase(row.original, phase);
        if (isSubrow(row.original)) {
            this.replaceRowIn(newRow, this.flatSubrows$);
        } else if (row.original.draft) {
            this.removeRowFrom(row.original, this.flatDraftRootRows$);
            this.appendRowTo(newRow, this.flatRootRows$);
        } else {
            this.replaceRowIn(newRow, this.flatRootRows$);
        }

        this.eventDispatcher('row-published', newRow);
    }

    public async deleteRow(row: Row<HierarchicalRecordRow>) {
        this.removeRowFrom(row.original, this.getOriginalRecordRowSource(row.original));
        await this.recordGridService.deleteRow(row.original);
        this.eventDispatcher('row-deleted', row.original);
    }

    public async duplicateRow(row: Row<HierarchicalRecordRow>) {
        const newRecordRow = await this.recordGridService.duplicateRow(row.original);
        await this.addDraftRow(newRecordRow);
    }

    public updateTableDataCellState(row: HierarchicalRecordRow, fieldId: FieldId, value: SimpleGridFieldValue) {
        if (exists(value)) {
            this.editCellValueInTableData(row, fieldId, {...value, fieldId});
        } else {
            this.deleteCellValueFromTableData(row, fieldId);
        }
    }

    public async updateServerCellState(row: HierarchicalRecordRow, fieldId: FieldId, value: SimpleGridFieldValue) {
        if (exists(value)) {
            await this.editCellValueOnServer(row, fieldId, value);
        } else {
            await this.deleteCellValueOnServer(row, fieldId);
        }
    }

    private async addDraftRow(newRecordRow: RecordRow) {
        this.appendRowTo(newRecordRow, this.flatDraftRootRows$);
        await tick();
        const requiredColumn = this.findFirstRequiredColumn(this.table, newRecordRow.fond.id);
        if (exists(requiredColumn)) {
            const targetCoordinates = new CellCoordinates({
                rowIndex: this.table.getRowModel().rows.length - 1,
                columnIndex: requiredColumn.getIndex(requiredColumn.getIsPinned() || undefined)
            });
            this.stateManager.forceEditModeIn(targetCoordinates);
        }
    }

    private editCellValueInTableData(row: HierarchicalRecordRow, fieldId: FieldId, value: GridField) {
        const updatedRow = structuredClone(downgradeToRecordRow(row));
        setFieldByFieldIdRecursive(updatedRow, fieldId, value);
        this.replaceRowIn(updatedRow, this.getOriginalRecordRowSource(row));
    }

    private deleteCellValueFromTableData(row: HierarchicalRecordRow, fieldId: FieldId) {
        const updatedRow = structuredClone(downgradeToRecordRow(row));
        deleteFieldByFieldIdRecursive(updatedRow, fieldId);
        this.replaceRowIn(updatedRow, this.getOriginalRecordRowSource(row));
    }

    private async editCellValueOnServer(row: RecordRow, fieldId: FieldId, value: SimpleGridFieldValue) {
        await this.recordGridService.editCell(row.id, fieldId, value);
    }

    private async deleteCellValueOnServer(row: RecordRow, fieldId: FieldId) {
        await this.recordGridService.deleteCell(row.id, fieldId);
    }

    private extractCellData(coordinates: CellCoordinates): ClipboardCellData {
        const data = getCellValueByCoordinates<HierarchicalRecordRow, GridField<SimpleGridFieldValue>[]>(this.table, coordinates);
        return marshallCellValue(data);
    }

    private async refreshRows(rows: Row<HierarchicalRecordRow>[]) {
        const parentChainsRows = rows.flatMap((row) => row.getParentRows());
        const subtreesRows = rows.flatMap((row) => row.getLeafRows());

        const recordRowsToBeRefreshed = ([...rows, ...parentChainsRows, ...subtreesRows])
            .map((row) => row.original)
            .toSorted(uuidComparator);

        const refreshedRecordRows: RecordRow[] = await this.recordGridService.loadRows(recordRowsToBeRefreshed.map((recordRow) => recordRow.id));
        refreshedRecordRows.sort(uuidComparator);

        const originalAndRefreshedRows = zip(recordRowsToBeRefreshed, refreshedRecordRows);

        const refreshedSubrows = originalAndRefreshedRows
            .filter(([originalRow,]) => isSubrow(originalRow))
            .map(([,refreshedRow]) => refreshedRow);

        const refreshedDraftRootRows = originalAndRefreshedRows
            .filter(([originalRow, refreshedRow]) => !isSubrow(originalRow) && refreshedRow.draft)
            .map(([,refreshedRow]) => refreshedRow);

        const refreshedRootRows = originalAndRefreshedRows
            .filter(([originalRow, refreshedRow]) => !isSubrow(originalRow) && !refreshedRow.draft)
            .map(([,refreshedRow]) => refreshedRow);

        this.replaceRowsIn(refreshedSubrows, this.flatSubrows$);
        this.replaceRowsIn(refreshedDraftRootRows, this.flatDraftRootRows$);
        this.replaceRowsIn(refreshedRootRows, this.flatRootRows$);

        this.eventDispatcher('rows-updated', refreshedRecordRows);
    }

    private findFirstRequiredColumn(table: Table<HierarchicalRecordRow>, fondId: number) {
        return table.getAllFlatColumns()
            .filter((column) => column.columnDef.meta.columnType === COLUMN_TYPES.DATA_COLUMN)
            .find((column) => resolveGridFieldAttributeValue(column.columnDef.meta.required, fondId));
    }

    private createInitialValues(): Record<FieldTypeId, SetFieldValueRequest> | undefined {
        return this.initialValuesEntries.length > 0 ? Object.fromEntries(this.initialValuesEntries) : undefined;
    }

    private getOriginalRecordRowSource(recordRow: HierarchicalRecordRow): BehaviorSubject<RecordRow[]> {
        if (isSubrow(recordRow)) {
            return this.flatSubrows$;
        } else if (recordRow.draft) {
            return this.flatDraftRootRows$;
        } else {
            return this.flatRootRows$;
        }
    }

    private appendRowTo(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => [...recordRows, recordRow]);
    }

    private removeRowFrom(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => removeAll(recordRows, byIdOf(recordRow)));
    }

    private replaceRowIn(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => replaceAll(recordRows, byIdOf(recordRow), recordRow));
    }

    private replaceRowsIn(replacementRows: RecordRow[], recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => {
            replacementRows.forEach((replacementRow) => {
                recordRows = replaceAll(recordRows, byIdOf(replacementRow), replacementRow);
            });
            return recordRows;
        });
    }
}