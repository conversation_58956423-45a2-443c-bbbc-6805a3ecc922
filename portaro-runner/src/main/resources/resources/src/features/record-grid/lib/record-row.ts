import type {GridFieldValue, HierarchicalRecordRow, RecordRow} from './types';
import type {FieldTypeId, UUID} from 'typings/portaro.be.types';
import {assertExists, exists} from 'shared/utils/custom-utils';
import {stringifyArgument} from 'shared/utils/error-utils';
import {getFieldByFieldTypeIdRecursive} from './grid-fields';
import {isEmpty} from 'shared/utils/array-utils';
import {assertHasRecordReference, hasRecordReference} from './types-utils';
import {asUuid} from 'shared/utils/types-utils';

export function parseRecordIdFromRowId(rowId: string): UUID {
    return asUuid(rowId.split('.').at(-1));
}


export function isSubrow(recordRow: HierarchicalRecordRow): boolean {
    return exists(recordRow.parentRowId);
}

export function downgradeToRecordRow(hierarchicalRecordRow: HierarchicalRecordRow): RecordRow {
    return {
        fields: hierarchicalRecordRow.fields,
        fond: hierarchicalRecordRow.fond,
        id: hierarchicalRecordRow.id,
        draft: hierarchicalRecordRow.draft,
        locked: hierarchicalRecordRow.locked,
        name: hierarchicalRecordRow.name,
        editationPhaseTransitions: hierarchicalRecordRow.editationPhaseTransitions
    };
}

export function createRootOnlyHierarchicalRows(rows: RecordRow[]): HierarchicalRecordRow[] {
    if (rows.length === 0) {
        return [];
    }

    return rows.map((row) => ({...row, subrows: [], parentRowId: null}))
}

export function createHierarchicalRows(rows: RecordRow[], expandableColumnsIds: FieldTypeId[], referenceRecordId: UUID): HierarchicalRecordRow[] {
    if (rows.length === 0) {
        return [];
    }

    const hierarchicalRecordRowFactory: HierarchicalRecordRowFactory = (row, subrows, parentRowId) => ({
        ...row,
        subrows: subrows.toSorted(compareRecordRowsWithDraftsLast),
        parentRowId
    })

    return transformIntoHierarchyNodes(rows, expandableColumnsIds, referenceRecordId, hierarchicalRecordRowFactory);
}


function compareRecordRowsWithDraftsLast(a: RecordRow, b: RecordRow): number {
    if (a.draft === b.draft) {
        return 0;
    }
    return a.draft ? 1 : -1;
}

function transformIntoHierarchyNodes(nodes: RecordRow[], expandableColumnsIds: FieldTypeId[], referenceRecordId: UUID, hierarchicalRecordRowFactory: HierarchicalRecordRowFactory): HierarchicalRecordRow[] {
    const descendantNodes: RecordRow[] = [];
    const rootNodes: RecordRow[] = [];
    const loadedRecordRowsIds = new Set(nodes.map((row) => row.id));

    // optimization: for-loop is faster than functional array functions
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < nodes.length; i++) {
        const recordRow = nodes[i];
        let descendantNodeFound = false;

        // optimization: for-loop is faster than functional array functions
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let j = 0; j < expandableColumnsIds.length; j++) {
            const expandableColumnId = expandableColumnsIds[j];
            const fields = getFieldByFieldTypeIdRecursive(recordRow, expandableColumnId);
            if (isEmpty(fields)) {
                continue;
            }
            assertIsFieldWithoutRepetition(fields, expandableColumnId);
            const field = fields.at(0);
            if (hasRecordReference(field) && field.recordReference.id !== referenceRecordId && loadedRecordRowsIds.has(field.recordReference.id)) {
                descendantNodes.push(recordRow);
                descendantNodeFound = true;
                break; // assume that field has only one parent
            }
        }

        if (!descendantNodeFound) {
            rootNodes.push(recordRow);
        }
    }

    return rootNodes.flatMap((rootNode) => transformIntoHierarchyNode(rootNode, null, expandableColumnsIds, descendantNodes, hierarchicalRecordRowFactory))
}

function transformIntoHierarchyNode(currentNode: RecordRow, parentNode: RecordRow | null, expandableColumnsIds: FieldTypeId[], untransformedNodes: RecordRow[], hierarchicalRecordRowFactory: HierarchicalRecordRowFactory): HierarchicalRecordRow {
    const directChildNodes: RecordRow[] = [];
    const otherDescendantNodes: RecordRow[] = [];

    // optimization: for-loop is faster than functional array functions
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < untransformedNodes.length; i++) {
        const untransformedNode = untransformedNodes[i];
        let directChildNodeFound = false;

        // optimization: for-loop is faster than functional array functions
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let j = 0; j < expandableColumnsIds.length; j++) {
            const expandableColumnId = expandableColumnsIds[j];
            const fields = getFieldByFieldTypeIdRecursive(untransformedNode, expandableColumnId);
            if (isEmpty(fields)) {
                continue;
            }
            assertIsFieldWithoutRepetition(fields, expandableColumnId);
            const field = fields.at(0);
            assertHasRecordReference(field);
            if (field.recordReference.id === currentNode.id) {
                directChildNodes.push(untransformedNode);
                directChildNodeFound = true;
                break;  // assume that field has only one parent
            }
        }

        if (!directChildNodeFound) {
            otherDescendantNodes.push(untransformedNode);
        }
    }

    const subtrees = directChildNodes.flatMap((childNode) => transformIntoHierarchyNode(childNode, currentNode, expandableColumnsIds, otherDescendantNodes, hierarchicalRecordRowFactory));
    return hierarchicalRecordRowFactory(currentNode, subtrees, exists(parentNode) ? parentNode.id : null);
}

function assertIsFieldWithoutRepetition(fields: GridFieldValue[], fieldTypeId: FieldTypeId): asserts fields is [GridFieldValue] {
    assertExists(fields);
    if (fields.length !== 1) {
        throw new Error(`Expected field of ${fieldTypeId} without repetition, but got: ${stringifyArgument(fields)}`);
    }
}

type HierarchicalRecordRowFactory = (node: RecordRow, subtrees: HierarchicalRecordRow[], parentRowId: UUID) => HierarchicalRecordRow;