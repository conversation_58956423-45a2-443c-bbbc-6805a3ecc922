<script lang="ts">
    import type {Row} from '@tanstack/svelte-table';
    import type {HierarchicalRecordRow, RecordEditationPhaseId} from '../../lib/types';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import type {UUID} from 'typings/portaro.be.types';
    import type {Readable} from 'svelte/store';
    import {readable} from 'svelte/store';
    import {sequence} from 'shared/utils/array-utils';
    import {exists} from 'shared/utils/custom-utils';
    import {getAncestorRowAtDepth, isFirstSubRow, isLastSubRow} from 'shared/ui-widgets/grid/utils';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import HierarchyHorizontalArrowPart from '../misc/HierarchyHorizontalArrowPart.svelte';
    import Checkbox from 'shared/ui-widgets/checkbox/Checkbox.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let recordGridDataManager: RecordGridDataManager;
    export let row: Row<HierarchicalRecordRow>;
    export let rowExpansionEnabled = false;
    export let expandButtonHidden = false;
    export let rowsWithSubrowsLoading$: Readable<UUID[]> = readable([]);

    $: isExpanded = row.getIsExpanded();
    $: canExpand = row.getCanExpand();
    $: depth = row.depth;
    $: subrowsAreLoading = $rowsWithSubrowsLoading$.includes(row.original.id);

    function shouldDisplayVerticalConnector(currentRow: Row<HierarchicalRecordRow>, depthLevel: number): boolean {
        const ancestorRowAtDepth = getAncestorRowAtDepth(currentRow, depthLevel);
        return !isLastSubRow(ancestorRowAtDepth);
    }

    const phaseTransitionButtons: Record<RecordEditationPhaseId, { icon: string; text: string }> = {
        draft: { icon: 'edit', text: 'Draft' },
        editing: { icon: 'share', text: 'Potvrdit' },
        finished: { icon: 'share', text: 'Dokončit' },
        locked: { icon: 'lock', text: 'Uzamknout' },
    };
</script>

<div class="kp-grid-control-cell control-cell-{row.id}">
    {#if !exists($$slots.default)}
        <Checkbox id="select-{row.original.id}"/>
    {/if}

    {#each row.original.editationPhaseTransitions as phaseTransition}
        <KpButton additionalClasses="add-new-row-btn"
                  buttonStyle="success-new"
                  buttonSize="sm"
                  isDisabled={!phaseTransition.permission.allowed}
                  tooltipLabel={phaseTransition.permission.allowed ? null : phaseTransition.permission.reason.text}
                  on:click={() => recordGridDataManager.changePhase(row, phaseTransition.targetPhase.id)}>

            <IconedContent icon={phaseTransitionButtons[phaseTransition.targetPhase.id].icon}>
                {phaseTransitionButtons[phaseTransition.targetPhase.id].text}
            </IconedContent>
        </KpButton>
    {/each}

    {#if row.original.draft}
        <KpButton additionalClasses="add-new-row-btn"
                  buttonStyle="danger-new"
                  buttonSize="sm"
                  on:click={() => recordGridDataManager.deleteRow(row)}>

            <IconedContent icon="trash">
                Smazat
            </IconedContent>
        </KpButton>
    {:else}
        {#if exists($$slots.default)}
            <slot/>
        {:else}
            <KpButtonStyleAnchor href="/#!/records/{row.original.id}" buttonSize="sm" buttonStyle="link">
                <IconedContent icon="link">Detail</IconedContent>
            </KpButtonStyleAnchor>
        {/if}

        {#if rowExpansionEnabled}
            {#if depth > 0}
                {#each sequence(1, depth - 1) as depthLevel}
                    <div class="offset-container">
                        {#if shouldDisplayVerticalConnector(row, depthLevel)}
                            <div class="vertical-connector"/>
                        {/if}
                    </div>
                {/each}

                <div class="hierarchy-arrow-container">
                    <div class="horizontal-arrow-part">
                        <HierarchyHorizontalArrowPart/>
                    </div>

                    <div class="vertical-arrow-part"
                         class:last-expanded={isLastSubRow(row)}
                         class:first-expanded={isFirstSubRow(row)}></div>
                </div>
            {/if}

            {#if canExpand && !expandButtonHidden}
                <div class="expand-button-container">
                    <KpButton additionalClasses="toggle-expand-btn"
                              buttonSize="sm"
                              isDisabled="{!canExpand}"
                              on:click={row.getToggleExpandedHandler()}>

                        <div class="arrow-container" class:opened={isExpanded}>
                            <UIcon icon="angle-small-down"/>
                        </div>
                    </KpButton>
                </div>
            {/if}

            {#if subrowsAreLoading}
                <KpLoadingBlock size="xs"/>
            {/if}
        {/if}
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @expand-offset-width: 32px;
    @expand-btn-width: 32px;
    @hierarchy-arrow-color: #ADACB2;

    @vertical-padding: 3px;
    @horizontal-padding: 5px;

    .kp-grid-control-cell {
        padding: @vertical-padding @horizontal-padding;
        display: flex;
        overflow: hidden;
        height: 100%;
        gap: @spacing-xs;

        --hierarchy-arrow-color: @hierarchy-arrow-color;

        :global(.expand-button-container .toggle-expand-btn) {
            width: calc(@expand-btn-width + 2px); // Button width + borders
        }

        :global(.kp-button),
        :global(.kp-button-style-link) {
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .offset-container {
        position: relative;
        width: @expand-offset-width;

        display: flex;
        align-items: center;
        justify-content: center;

        .vertical-connector {
            display: flex;
            width: 2px;
            border-radius: 1px;
            position: absolute;
            left: 50%;
            background-color: var(--hierarchy-arrow-color);
            transform: translateX(-50%);
            top: calc(@vertical-padding * -1);
            bottom: calc(@vertical-padding * -1);
        }
    }

    .hierarchy-arrow-container {
        flex-shrink: 0;
        position: relative;
        width: @expand-btn-width;

        .vertical-arrow-part {
            display: flex;
            width: 2px;
            border-radius: 1px;
            position: absolute;
            left: 50%;
            background-color: var(--hierarchy-arrow-color);
            transform: translateX(-50%);
            top: calc(@vertical-padding * -1);
            bottom: calc(@vertical-padding * -1);

            &.first-expanded {
                top: 0;
            }

            &.last-expanded {
                bottom: 47.5%;
            }
        }

        .horizontal-arrow-part {
            display: flex;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
        }
    }

    .expand-button-container .arrow-container {
        transition: rotate 0.2s ease-in-out;

        &.opened {
            rotate: 180deg;
        }
    }
</style>