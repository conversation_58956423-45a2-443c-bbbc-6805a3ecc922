import type {ErroredFieldResponse, FieldId, FieldTypeId, FondedRecordLabeledReference, Identified, Labeled, LabeledIdentified, PermissionResult, SetFieldValueRequest, SimpleFond, UUID, Valuable} from 'typings/portaro.be.types';

/*
 * ┌─────────────────────────────────────────────────┐
 * │                   GridField                     │◄──────────────┐
 * ├─────────────────────┬───────────────────────────┤               │
 * │                     │                           │               │
 * │ GridFieldMetadata   │ GridFieldValue            │               │
 * │  - fieldId          │  - text                   │               │
 * │                     │  - recordReference?       │               │
 * │                     │                           │               │
 * └─────────────────────┴───────────────────────────┘               │
 *                                   ▲                               │
 *                          inherits │                               │
 *             ┌─────────────────────┴─────────┐                     │
 *             │                               │                     │
 * ┌───────────┴────────────┐     ┌────────────┴───────────┐         │
 * │                        │     │                        │         │
 * │ SimpleGridFieldValue   │     │ CompoundGridFieldValue │ contains│
 * │                        │     │  - fields[] ───────────┼─────────┘
 * │                        │     │                        │
 * └────────────────────────┘     └────────────────────────┘
 *                  ▲
 *         inherits │
 *            ┌─────┴─────────────────────┬──────────────────────────┐
 *            │                           │                          │
 * ┌──────────┴────────────┐  ┌───────────┴───────────┐  ┌───────────┴───────────┐
 * │                       │  │                       │  │                       │
 * │ OptionGridFieldValue  │  │ ScalarGridFieldValue  │  │ ErrorGridFieldValue   │
 * │  - id                 │  │  - value              │  │  - error              │
 * │                       │  │                       │  │                       │
 * └───────────────────────┘  └───────────────────────┘  └───────────────────────┘
 *
 *
 */

export interface GridFieldMetadata {
    fieldId: FieldId
}

export interface GridFieldValue extends Labeled {
    recordReference?: FondedRecordLabeledReference | null;
}

export type GridField<GRID_FIELD_SUBTYPE extends GridFieldValue = GridFieldValue> = GridFieldMetadata & GRID_FIELD_SUBTYPE;

export type GridFieldValueWithReference<GRID_FIELD_SUBTYPE extends GridFieldValue = GridFieldValue> = GRID_FIELD_SUBTYPE & {recordReference: FondedRecordLabeledReference};

export interface ErrorGridFieldValue extends GridFieldValue {
    error: ErroredFieldResponse;
}

type SupportedScalarValues = string | number | boolean;

export type ScalarGridValue<VALUE extends SupportedScalarValues = SupportedScalarValues> = GridFieldValue & Valuable<VALUE>;

export type OptionGridFieldValue = GridFieldValue & LabeledIdentified<string | number | UUID>;

export type SimpleGridFieldValue = ErrorGridFieldValue | ScalarGridValue | OptionGridFieldValue | null;

export type CompoundGridFieldValue = GridFieldValue & Fieldable;

export interface Fieldable {
    fields: Record<FieldTypeId, GridField<CompoundGridFieldValue>[] | GridField<SimpleGridFieldValue>[]>;
}

export interface RecordRow extends Identified<UUID>, Fieldable { // ID can be UUID (record ID) or a string (for example, NEW_ROW_STUMP)
    fond: SimpleFond;
    draft: boolean;
    locked: boolean;
    editationPhaseTransitions: RecordEditationPhaseTransition[]
    name?: string;
}

export interface RecordEditationPhaseTransition {
    targetPhase: RecordEditationPhase;
    permission: PermissionResult;
}

export interface RecordEditationPhase {
    id: RecordEditationPhaseId;
    text: string;
}

export type RecordEditationPhaseId = 'draft' | 'editing' | 'finished' | 'locked';

export interface HierarchicalRecordRow extends RecordRow {
    subrows: HierarchicalRecordRow[];
    parentRowId: UUID | null;
}

export interface GridEditRequest {
    cellEditRequests: GridCellEditRequest[];
}

export interface GridCellEditRequest {
    record: UUID;
    fieldId: FieldId;
    setFieldValueRequest: SetFieldValueRequest;
}

export interface GridDeleteRequest {
    cellDeleteRequests: GridCellDeleteRequest[];
}

export interface GridCellDeleteRequest {
    record: UUID;
    fieldId: FieldId;
}

export interface KpRecordGridDisplayEvents {
    'rows-updated': RecordRow[];
    'row-deleted': RecordRow;
    'row-published': RecordRow;
}
