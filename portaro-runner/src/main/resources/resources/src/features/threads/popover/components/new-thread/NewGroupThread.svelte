<script lang="ts">
    import type {User} from 'typings/portaro.be.types';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getInjector} from 'core/svelte-context/context';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import {THREAD_USER_SELECTOR_FORCE_SETTINGS} from 'src/features/threads/threads.constants';

    export let threadName = '';
    export let addedUsers: User[] = [];

    const currentAuthService = getInjector().getByClass(CurrentAuthService);

    let selectedImage: File | null = null;
    let userModel: User | null = null;
    let imagePreviewUrl: string | null = null;
    let fileInputElement: HTMLInputElement;

    onDestroy(() => {
        if (exists(imagePreviewUrl)) {
            URL.revokeObjectURL(imagePreviewUrl);
        }
    });

    const handleImageClick = () => {
        if (exists(fileInputElement)) {
            fileInputElement.click();
        }
    };

    const handleFileChange = (event: Event) => {
        const input = event.target as HTMLInputElement;
        const files = input.files;

        if (!exists(files) || files.length === 0) {
            return;
        }

        if (exists(imagePreviewUrl)) {
            URL.revokeObjectURL(imagePreviewUrl);
        }

        selectedImage = files[0];
        imagePreviewUrl = URL.createObjectURL(selectedImage);
    };

    const handleUserModelChange = (event: CustomEvent<User>) => {
        userModel = null;

        if (!exists(event.detail)) {
            return;
        }

        if (exists(addedUsers.find((user) => user.id === event.detail.id))) {
            return;
        }

        const currentAuth = currentAuthService.getCurrentAuthValue();
        if (event.detail.id === currentAuth.activeUser.id) {
            return;
        }

        addedUsers = [...addedUsers, event.detail];
    };
</script>

<div class="image-selector-container">
    <button type="button" class="image-selector-button" on:click={handleImageClick}>
        {#if imagePreviewUrl}
            <img src={imagePreviewUrl} alt="Selected group icon" class="selected-image"/>
        {:else}
            <UIcon icon="mode-landscape"/>
        {/if}
    </button>

    <small class="text-center text-muted">
        Kliknutím vyberte obrázek ikony skupiny (není povinné)
    </small>

    <input class="sr-only"
           type="file"
           accept="image/*"
           bind:this={fileInputElement}
           on:change={handleFileChange}/>
</div>

<KpValueEditor type="text" placeholder="Název konverzce" bind:model={threadName}/>

<Flex direction="column" gap="m">
    <span class="text-muted">Účastníci ({addedUsers.length})</span>

    {#each addedUsers as user (user.id)}
        <Flex alignItems="center" gap="s" width="100%">
            <KpUserAvatar userRecordId="{user.recordId}" sizePx={24}/>
            <span>{pipe(user, loc())}</span>
            <Spacer flex="1"/>
            <KpIconButton icon="trash" on:click={() => addedUsers = addedUsers.filter((u) => u.id !== user.id)}/>
        </Flex>
    {/each}

    <Flex alignItems="center" justifyContent="center">
        <KpValueEditorForceSettings forceSettings="{THREAD_USER_SELECTOR_FORCE_SETTINGS}">
            <KpValueEditor type="user" bind:model={userModel} on:model-change={handleUserModelChange}/>
        </KpValueEditorForceSettings>
    </Flex>
</Flex>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @selector-size: 154px;

    .image-selector-container {
        display: flex;
        gap: @spacing-sm;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .image-selector-button {
            background: none;
            border: 1px solid @themed-border-default;
            border-radius: 50%;
            width: @selector-size;
            height: @selector-size;
            display: flex;
            padding: 0;
            font-size: 48px;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            outline: 2px solid transparent;
            outline-offset: 2px;
            color: @themed-text-muted;
            transition: outline-color 0.3s ease-in-out;
            overflow: hidden;

            &:hover {
                outline-color: var(--accent-blue-new);
            }

            .selected-image {
                width: 100%;
                height: 100%;
                padding: 0;
                margin: 0;
                object-fit: cover;
            }
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    }
</style>