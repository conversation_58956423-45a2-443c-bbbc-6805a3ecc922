import type {Directory, ViewableFile} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {exists} from 'shared/utils/custom-utils';
import {transferify} from 'shared/utils/data-service-utils';

interface StartChunkedFileUploadResponse {
    chunkedFileHandle: string;
}

export class ChunkedFileUploadService {
    public static serviceName = 'chunkedFileUploadService';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public createUploader(
        file: File,
        targetDir: Directory,
        onProgressUpdate: (progress: number) => void,
        onUploadComplete: (viewableFile?: ViewableFile) => void
    ): ChunkedFileUploader {
        return new ChunkedFileUploader(
            this.ajaxService,
            file,
            targetDir,
            onProgressUpdate,
            onUploadComplete
        );
    }
}

export class ChunkedFileUploader {

    private static CHUNK_SIZE = 1024 * 512; // 512 kb
    private static BEGIN_CHUNKED_FILE_UPLOAD_URL = 'files/chunked-upload/start';
    private static UPLOAD_FILE_CHUNK_URL = 'files/chunked-upload';
    private static END_CHUNKED_FILE_CHUNK_URL = 'files/chunked-upload/finish';

    private onUploadingBeganListeners: (() => void)[] = [];
    private onProgressUpdateListeners: ((progress: number) => void)[] = [];
    private onUploadErrorListeners: (() => void)[] = [];
    private onUploadCompleteListeners: ((viewableFile?: ViewableFile) => void)[] = [];

    private uploadingFile = false;
    private totalChunks = 0;
    private fileHandle: string | undefined = undefined;

    constructor(
        private ajaxService: AjaxService,
        private file: File,
        private targetDir: Directory,
        onProgressUpdate: (progress: number) => void,
        onUploadComplete: (viewableFile?: ViewableFile) => void
    ) {
        this.onProgressUpdateListeners.push(onProgressUpdate);
        this.onUploadCompleteListeners.push(onUploadComplete);
    }

    public async startUploading(): Promise<void> {
        if (this.uploadingFile) {
            return;
        }

        if (!exists(this.file) || this.file.size === 0) {
            this.onUploadErrorListeners.forEach((e) => e());
            return;
        }

        let handle: string;

        try {
            const response = await this.ajaxService
                .createRequest(ChunkedFileUploader.BEGIN_CHUNKED_FILE_UPLOAD_URL)
                .post<StartChunkedFileUploadResponse>(transferify({
                    name: this.file.name,
                    fileSize: this.file.size,
                    targetDirectory: this.targetDir
                }));

            handle = response.chunkedFileHandle;
        } catch {
            this.onUploadErrorListeners.forEach((e) => e());
            return;
        }

        this.fileHandle = handle;

        let chunkIndex = 0;
        this.totalChunks = Math.ceil(this.file.size / ChunkedFileUploader.CHUNK_SIZE);

        this.uploadingFile = true;
        this.onUploadingBeganListeners.forEach((e) => e());

        for (let start = 0; start < this.file.size; start += ChunkedFileUploader.CHUNK_SIZE) {
            if (!this.uploadingFile) {
                break;
            }

            const chunk = this.file.slice(start, start + ChunkedFileUploader.CHUNK_SIZE);
            const success = await this.uploadFileChunk(this.fileHandle, start, chunk, chunkIndex);

            if (!success) {
                this.onUploadErrorListeners.forEach((e) => e());
                return;
            }

            chunkIndex++;
        }

        if (this.uploadingFile) {
            await this.endFileUpload(this.fileHandle, false);
        }
    }

    public async stopUploading(): Promise<void> {
        if (!this.uploadingFile || !this.fileHandle) {
            return;
        }

        await this.endFileUpload(this.fileHandle, true);
    }

    private async uploadFileChunk(fileId: string, start: number, chunk: Blob, chunkIndex: number): Promise<boolean> {
        // Calculate the end byte position for the Range header
        const end = start + chunk.size - 1;

        try {
            await this.ajaxService
                .createRequest(`${ChunkedFileUploader.UPLOAD_FILE_CHUNK_URL}/${fileId}`)
                .withHeaders({
                    'Content-Type': chunk.type || 'application/octet-stream',
                    'Range': `bytes=${start}-${end}`
                })
                .post(chunk);
        } catch {
            return false;
        }

        const progressPercentage = Math.round(((chunkIndex + 1) / this.totalChunks) * 100);
        this.onProgressUpdateListeners.forEach((e) => e(progressPercentage));

        return true;
    }

    private async endFileUpload(fileId: string, quitUpload: boolean): Promise<void> {
        this.uploadingFile = false;

        try {
            const endUploadResult = await this.ajaxService
                .createRequest(`${ChunkedFileUploader.END_CHUNKED_FILE_CHUNK_URL}/${fileId}`)
                .post<ViewableFile | null>({
                    quitUpload
                });

            if (quitUpload) {
                this.onUploadCompleteListeners.forEach((e) => e(null));
                return;
            }

            this.onUploadCompleteListeners.forEach((e) => e(endUploadResult));
        } catch {
            this.onUploadErrorListeners.forEach((e) => e());
        }
    }

    public addOnUploadingBeganListener(listener: () => void): void {
        this.onUploadingBeganListeners.push(listener);
    }

    public addOnProgressUpdateListener(listener: (progress: number) => void): void {
        this.onProgressUpdateListeners.push(listener);
    }

    public addOnUploadErrorListener(listener: () => void): void {
        this.onUploadErrorListeners.push(listener);
    }

    public addOnUploadCompleteListener(listener: (viewableFile?: ViewableFile) => void): void {
        this.onUploadCompleteListeners.push(listener);
    }

    public removeOnUploadingBeganListener(listener: () => void): void {
        this.onUploadingBeganListeners = this.onUploadingBeganListeners.filter((e) => e !== listener);
    }

    public removeOnProgressUpdateListener(listener: (progress: number) => void): void {
        this.onProgressUpdateListeners = this.onProgressUpdateListeners.filter((e) => e !== listener);
    }

    public removeOnUploadErrorListener(listener: () => void): void {
        this.onUploadErrorListeners = this.onUploadErrorListeners.filter((e) => e !== listener);
    }

    public removeOnUploadCompleteListener(listener: (viewableFile?: ViewableFile) => void): void {
        this.onUploadCompleteListeners = this.onUploadCompleteListeners.filter((e) => e !== listener);
    }
}