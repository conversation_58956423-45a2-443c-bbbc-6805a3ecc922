<style type="text/css">
    .sourceDocument {
        font-style: italic;
    }
</style>


#set($dq = '"')
#set($fondId = $record.fond.id)
#set($article = ($fondId == 7 or $fondId == 9 or $fondId == 12))
#set($review = ($fondId == 7 or $fondId == 9 or $fondId == 12))





<!-- HLAVNI AUTOR -->
#fg($record.query("d100.[a,b]").purify() '' ' ' ' - ')
#fg($record.query("d110.[a,b]").purify() '' ' ' ' - ')
#fg($record.query("d111.[a,b]").purify() '' ' ' ' - ')




##RECENZE
    <!-- 099a. c. 098h 700a. 098a 245 362 260 300 500a -->
    #sfRaw($record 99 'ac')

    #sfRaw($record 98 'h')

    #sfRaw($record 98 'a')

    ##zbytek je ve zbyku
##RECENZE KONEC





<br/>


<!-- [d245.a](pokud neni fond 7,9,12, kurzivou)[pokud P245.b, ':' jinak '.'] [P245.b]. [P245.n][P245.p][P245.c]. -->
#set($cssClass = '')
#if(!$article)
    #set($cssClass = 'sourceDocument')
#end

#fg($record.query("d245.[a,b]") "<span class=$dq$cssClass$dq>" '</span>' '; ' ' ')

#sfRaw($record 'd245' 'np' '<span>' '</span>')

#set($sf245c = $record.getNewFormatSubfield('d245', 0, 'c', 0))
#if($sf245c)
    #if($sf245c.raw.endsWith('.'))
        #sfRaw($record 'd245' 'c' '<span>' '</span>')
    #end
    #if(!$sf245c.raw.endsWith('.'))
        #sfRaw($record 'd245' 'c' '<span>' '.</span>')
    #end
#end



<!-- d250.obsah -->
#sfRaw($record 'd250' '' ' ' '.')


<!-- vypsat [d260], kdyz je fond recenze (19) nebo kdyz existuje [P260.a]. Pokud existuje vice podpoli nez jen a, po acku dej tecku -->
#if($record.getNewFormatSubfield('d260', 0, 'a', 0) or $fondId == 19)
    #sfRaw($record 'd260' '' ' ' '.')
#end
#if($record.getNewFormatSubfield('d264', 0, 'a', 0) or $fondId == 19)
    #if($record.getNewFormatFields('d264').size() > 1)
        ## vice nez jedno opakovani pole 264 -> hledame to s ind2=1
        #fg($record.query('d264').filterSiblingContains('1', 'ind2').filterAutonomous() '' '.')
    #else
        ## jen jedno opakovani pole 264
        #sfRaw($record 'd264' '' ' ' '.')
    #end
#end


<!-- [d362.a] -->
#sfRaw($record 'd362' 'a')


<!-- [P300] (Zobrazovat jen kdyz je fond recenze (19) nebo pokud je vyplněno podpole a) -->
#if($fondId == 19)
    #sfRaw($record 300 '' '' '.')
#elseif($record.getNewFormatSubfield('d300', 0, 'a', 0))
    #sfRaw($record 300)
#end


<!-- If(not empty(P490.obsah),'('+Atrim$(P490.obsah)+')',' ') -->
#if($record.getField(490, 0))
    (#if($record.getNewFormatSubfield('d490', 0, 'a', 0))
        #sfRaw($record 490 'a')
    #end

    #if($record.getNewFormatSubfield('d490', 0, 'x', 0))
        #sfRaw($record 490 'x' 'ISSN: ')
    #end

    #if($record.getNewFormatSubfield('d490', 0, 'l', 0))
        #sfRaw($record 490 'l')
    #end

    #if($record.getNewFormatSubfield('d490', 0, 'v', 0))
        #sfRaw($record 490 'v')
    #end)

#end


<!-- If(not empty(P773.h),'In: '+P773.t+' '+if(Contains(P773.g,'('),Mid$(P773.d,0,StrPos(P773.d,',')),P773.d)+' '+P773.g, P773.t+' '+P773.g )
-->
#set($sf773h = $record.getNewFormatSubfield('d773', 0, 'h', 0))
#set($sf773g = $record.getNewFormatSubfield('d773', 0, 'g', 0))
#set($sf773d = $record.getNewFormatSubfield('d773', 0, 'd', 0))
#if($sf773h)
    In:
    #if($article)
        #sfRaw($record 773 't' '<span class="sourceDocument">' '</span>')
    #end
    #if(!$article)
        #sfRaw($record 773 't')
    #end

    #if($sf773g.raw.contains('('))
        #set($endIdx = $sf773d.raw.indexOf(','))
        #if($endIdx < 0)
            $sf773d.raw
        #end
        #if($endIdx > 0)
            $sf773d.raw.substring(0, $endIdx)
        #end
    #end
    #if(!$sf773g.raw.contains('('))
        $!{sf773d.raw}
    #end

    #set($sf773gSuffix = '')
    #if(!$sf773g.raw.endsWith('.'))
        #set($sf773gSuffix = '.')
    #end
    $!{sf773g.raw}$!{sf773gSuffix}
#end
#if(!$sf773h)

    #if($article)
        #sfRaw($record 773 'td' '<span class="sourceDocument">' '</span>')
    #end
    #if(!$article)
        #sfRaw($record 773 'td')
    #end

    #sfRaw($record 773 'g')
#end


<!-- ve fondu excerpovane sborniky zobrazovat 779 -->
#if($record.fond.id == 4)
    #sfRaw($record 779)
#end


<!-- if(Mid$(P500.a,0,1)='[',P500.a,if(not empty(P500.a),'['+P500.a+']' ,'' )) -->
#set($sf500a = $record.getNewFormatSubfield('d500', 0, 'a', 0))
#set($startsWithBracket = $sf500a.raw.startsWith('['))
#if($sf500a)
    #if($startsWithBracket)
        #sfRaw($record 500 'a')
    #end
    #if(!$startsWithBracket)
        [#sfRaw($record 500 'a')]
    #end
#end


#fg($record.query('d20.a').purify() 'ISBN:')


<!-- If(not empty(P22.a),'ISSN:'+P22.a,'') -->
#sfRaw($record 22 'a' 'ISSN:')


<!-- P856.obsah -->
#fg($record.query('d856.u'))