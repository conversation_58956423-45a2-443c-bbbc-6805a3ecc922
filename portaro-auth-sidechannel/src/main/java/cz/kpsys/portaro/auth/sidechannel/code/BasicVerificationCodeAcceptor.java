package cz.kpsys.portaro.auth.sidechannel.code;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.SingleValueRepository;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BasicVerificationCodeAcceptor implements VerificationCodeAcceptor {

    @NonNull SingleValueRepository<String, String> verificationCodeRepository;

    @Override
    public void acceptCode(@NonNull VerificationCodeAcceptCommand command,
                           @NonNull UserAuthentication currentAuth) {
        String requestKey = command.getRequestKey();
        Optional<String> persistedCode = verificationCodeRepository.restore(requestKey);
        if (persistedCode.isEmpty()) {
            throw new InvalidVerificationCodeException();
        }
        if (!persistedCode.get().equals(command.getSideChannelCode())) {
            throw new InvalidVerificationCodeException();
        }
    }
}
