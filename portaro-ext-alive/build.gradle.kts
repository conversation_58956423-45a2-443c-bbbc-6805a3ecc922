dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-integ-feign"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))
    implementation(project(":portaro-payment"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-web"))

    implementation("org.slf4j:slf4j-api:+")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.+")
    implementation("io.github.openfeign:feign-jackson:+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
}
