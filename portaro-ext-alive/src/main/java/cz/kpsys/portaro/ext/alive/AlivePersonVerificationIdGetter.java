package cz.kpsys.portaro.ext.alive;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.alive.datatypes.AliveVerificationResult;
import cz.kpsys.portaro.ext.alive.datatypes.VerifiedStudent;
import cz.kpsys.portaro.ext.alive.datatypes.VerifiedTeacher;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AlivePersonVerificationIdGetter {

    @NonNull AliveServiceUserVerifier aliveUserVerifier;
    @NonNull ContextualProvider<Department, @Nullable ReaderCategory> studentReaderCategory;
    @NonNull ContextualProvider<Department, @Nullable ReaderCategory> teacherReaderCategory;

    public Optional<Integer> getVerificationId(Department ctx, Person person) {
        AliveVerificationResult verify = aliveUserVerifier.verify(ctx, person);
        return switch (verify) {
            case VerifiedStudent student when ReaderRole.hasCategory(person, studentReaderCategory.getOn(ctx)) ->
                    Optional.of(student.verificationId());
            case VerifiedTeacher teacher when ReaderRole.hasCategory(person, teacherReaderCategory.getOn(ctx)) ->
                    Optional.of(teacher.verificationId());
            default -> Optional.empty();
        };
    }
}
