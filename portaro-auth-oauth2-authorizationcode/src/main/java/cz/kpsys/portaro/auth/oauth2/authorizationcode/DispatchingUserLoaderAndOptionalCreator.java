package cz.kpsys.portaro.auth.oauth2.authorizationcode;

import cz.kpsys.portaro.auth.oauth2.AuthUserLoaderAndOptionalCreator;
import cz.kpsys.portaro.auth.oauth2.DepartmentedOAuth2AuthorizationCodeAuthenticationRequest;
import cz.kpsys.portaro.auth.oauth2.OAuth2Provider;
import cz.kpsys.portaro.auth.oauth2.accesstoken.OAuth2TokenResponse;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Map;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DispatchingUserLoaderAndOptionalCreator implements AuthUserLoaderAndOptionalCreator {

    @NonNull Map<OAuth2Provider, AuthUserLoaderAndOptionalCreator> userLoaderAndOptionalCreatorProvider;

    @Override
    public User getOrCreateAndGetUser(DepartmentedOAuth2AuthorizationCodeAuthenticationRequest authRequest, OAuth2TokenResponse tokenResponse) {
        return Optional.ofNullable(userLoaderAndOptionalCreatorProvider.get(authRequest.getProvider()).getOrCreateAndGetUser(authRequest, tokenResponse))
                .orElseThrow(() -> new NoUserLoaderForOAuth2ProviderException(authRequest.getProvider()));
    }
}
