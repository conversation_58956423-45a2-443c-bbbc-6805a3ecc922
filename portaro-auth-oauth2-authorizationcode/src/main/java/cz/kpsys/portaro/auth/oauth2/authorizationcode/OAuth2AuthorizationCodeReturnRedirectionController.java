package cz.kpsys.portaro.auth.oauth2.authorizationcode;

import com.fasterxml.jackson.core.type.TypeReference;
import cz.kpsys.portaro.auth.AuthenticationSetter;
import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.oauth2.DepartmentedOAuth2AuthorizationCodeAuthenticationRequest;
import cz.kpsys.portaro.auth.oauth2.OAuth2ParameterNames;
import cz.kpsys.portaro.auth.oauth2.OAuth2Provider;
import cz.kpsys.portaro.auth.pairing.AuthPairingResponse;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.crypto.SecuredRandomStringGenerator;
import cz.kpsys.portaro.commons.object.repo.InJsonTokenSingleValueRepository;
import cz.kpsys.portaro.commons.object.repo.SingleValueRepository;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.AuthableUser;
import cz.kpsys.portaro.web.GenericController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import cz.kpsys.portaro.web.server.ServerUrl;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static cz.kpsys.portaro.auth.oauth2.OAuth2Constants.OAUTH2_AUTHORIZATION_CODE_AUTH_PATH;
import static cz.kpsys.portaro.user.BasicUser.ROLE_EVIDED;
import static cz.kpsys.portaro.user.BasicUser.ROLE_EXTERNALLY_AUTHENTICATED;

@RequestMapping(OAUTH2_AUTHORIZATION_CODE_AUTH_PATH)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OAuth2AuthorizationCodeReturnRedirectionController extends GenericController {

    static final String TARGET_PAGE_URL_PARAM = "redirect";

    @NonNull ContextualFunction<OAuth2Provider, Department, @NonNull String> clientIdProvider;
    @NonNull ContextualFunction<OAuth2Provider, Department, @NonNull String> requiredScopeProvider;
    @NonNull ContextualFunction<OAuth2Provider, Department, @NonNull String> authorizationUrlProvider;
    @NonNull ContextualFunction<OAuth2Provider, Department, @NonNull String> claimsRequestValueProvider;
    @NonNull ContextualFunction<OAuth2Provider, Department, String>  redirectUriProvider;
    @NonNull ContextualFunction<AuthableUser, OAuth2Provider, @NonNull AuthPairingResponse> authPairingResponseProvider;
    @NonNull Authenticator<DepartmentedOAuth2AuthorizationCodeAuthenticationRequest, OAuth2AuthorizationCodeSuccessAuthentication> oAuth2AuthorizationCodeAuthenticator;
    @NonNull AuthenticationSetter authenticationHolder;
    @NonNull SingleValueRepository<SecuredDecodedState, String> stateRepository = InJsonTokenSingleValueRepository.ofDefaultObjectMapper(new TypeReference<>() {});
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;
    @NonNull SecuredRandomStringGenerator securedRandomStringGenerator;

    @GetMapping(value = "{registrationId}", params = "!" + OAuth2ParameterNames.CODE)
    public ModelAndView redirectToLoginServer(@PathVariable("registrationId") OAuth2Provider provider,
                                              @RequestParam(value = TARGET_PAGE_URL_PARAM, required = false) String targetPageUrl,
                                              @CurrentDepartment Department ctx,
                                              @ServerUrl String serverUrl) {
        String encodedState = stateRepository.store(new SecuredDecodedState(ObjectUtil.firstNotNull(targetPageUrl, serverUrl), serverUrl, UuidGenerator.forIdentifier()));
        String url = new UrlCreator()
                .serverBaseUrl(authorizationUrlProvider.getOn(provider, ctx))
                .addParameter(OAuth2ParameterNames.RESPONSE_TYPE, "code")
                .addParameter(OAuth2ParameterNames.CLIENT_ID, clientIdProvider.getOn(provider, ctx))
                .addParameter(OAuth2ParameterNames.SCOPE, requiredScopeProvider.getOn(provider, ctx))
                .addParameter(OAuth2ParameterNames.STATE, encodedState)
                .addParameter(OAuth2ParameterNames.NONCE, securedRandomStringGenerator.generateLetters(20))
                .addParameter(OAuth2ParameterNames.REDIRECT_URI, redirectUriProvider.getOn(provider, ctx))
                .addParameterIfNotNull(OAuth2ParameterNames.CLAIMS, claimsRequestValueProvider.getOn(provider, ctx))
                .build();
        return ModelAndPageViewFactory.redirect(url);
    }


    @GetMapping(value = "{registrationId}", params = {"!" + TARGET_PAGE_URL_PARAM, OAuth2ParameterNames.CODE, OAuth2ParameterNames.STATE})
    public ModelAndView returnFromLoginServerAndRedirectToOriginServer(@PathVariable("registrationId") OAuth2Provider provider,
                                                                       @RequestParam(OAuth2ParameterNames.CODE) String code,
                                                                       @RequestParam(OAuth2ParameterNames.STATE) String state) {
        // phase 2 - returned from authorization server to registered redirect_url -> redirect to origin portaro
        SecuredDecodedState securedDecodedState = stateRepository.restore(state).orElseThrow();
        String url = new UrlCreator()
                .serverBaseUrl(securedDecodedState.origServerUrl())
                .path(OAUTH2_AUTHORIZATION_CODE_AUTH_PATH, provider)
                .addParameter(OAuth2ParameterNames.CODE, code)
                .addParameter(TARGET_PAGE_URL_PARAM, securedDecodedState.targetPageUrl())
                .build();
        return ModelAndPageViewFactory.redirect(url);
    }


    @GetMapping(value = "{registrationId}", params = {OAuth2ParameterNames.CODE, "!" + OAuth2ParameterNames.STATE})
    public ModelAndView authenticateAndRedirectToOriginUrl(@PathVariable("registrationId") OAuth2Provider provider,
                                                           @RequestParam(OAuth2ParameterNames.CODE) String code,
                                                           @RequestParam(TARGET_PAGE_URL_PARAM) String targetPageUrl,
                                                           @CurrentDepartment Department ctx,
                                                           HttpServletRequest request) {
        // phase 3 - redirected to origin portaro -> exchange code with token and authenticate
        DepartmentedOAuth2AuthorizationCodeAuthenticationRequest authRequest = new DepartmentedOAuth2AuthorizationCodeAuthenticationRequest(provider, code, redirectUriProvider.getOn(provider, ctx), ctx);
        OAuth2AuthorizationCodeSuccessAuthentication successAuth = oAuth2AuthorizationCodeAuthenticator.authenticate(authRequest);
        authenticationHolder.addAuthentication(successAuth);
        if (successAuth.getRole().contains(ROLE_EXTERNALLY_AUTHENTICATED) && !successAuth.getRole().contains(ROLE_EVIDED)) {
            return modelAndPageViewFactory.pageView("spa", ctx, request, Map.of(), List.of(authPairingResponseProvider.getOn(successAuth.getActiveUser(), provider)));
        }
        return ModelAndPageViewFactory.redirect(targetPageUrl);
    }

    public record SecuredDecodedState(@NonNull String targetPageUrl, @NonNull String origServerUrl, @NonNull UUID securityToken) {
    }

}
